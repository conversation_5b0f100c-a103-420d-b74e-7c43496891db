import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request) {
  try {
    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session: userSession }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !userSession) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const url = new URL(request.url)
    const searchParams = url.searchParams
    const ignoreUserLimits = searchParams.get("ignoreUserLimits") === "true"

    // Verificar se o usuário tem direcionamentos (limites de valor)
    let userValueLimits = null;
    if (userSession && !ignoreUserLimits) {
      console.log("🔍 [STATS] Verificando direcionamentos do usuário:", userSession.user.id);

      const { data: userDirecionamentos, error: direcionamentosError } = await supabase
        .from("direcionamento_usuarios")
        .select(`
          direcionamento:direcionamentos(
            id,
            nome,
            valor_minimo,
            valor_maximo
          )
        `)
        .eq("usuario_id", userSession.user.id);

      if (direcionamentosError) {
        console.error("❌ [STATS] Erro ao buscar direcionamentos do usuário:", direcionamentosError);
      } else {
        console.log("📋 [STATS] Direcionamentos encontrados:", userDirecionamentos);

        if (userDirecionamentos && userDirecionamentos.length > 0) {
          // Encontrar o valor máximo que o usuário pode ver
          const valoresMaximos = userDirecionamentos
            .filter(d => d.direcionamento && d.direcionamento.valor_maximo)
            .map(d => (d.direcionamento as any).valor_maximo);

          console.log("📋 [STATS] Valores máximos encontrados:", valoresMaximos);

          if (valoresMaximos.length > 0) {
            const maxValue = Math.max(...valoresMaximos);

            if (maxValue > 0) {
              userValueLimits = maxValue;
              console.log(`💰 [STATS] Usuário ${userSession.user.id} tem limite de valor: R$ ${userValueLimits}`);
            } else {
              console.log("⚠️ [STATS] Valor máximo é 0 ou inválido");
            }
          } else {
            console.log("⚠️ [STATS] Nenhum valor máximo válido encontrado nos direcionamentos");
          }
        } else {
          console.log("📝 [STATS] Usuário não tem direcionamentos configurados - pode ver todos os valores");
        }
      }
    } else {
      console.log("🚫 [STATS] Verificação de direcionamentos ignorada ou usuário não autenticado");
    }

    // Função para buscar borderos com filtro de direcionamento
    const getBorderosWithLimits = async (status: string) => {
      let query = supabase
        .from("borderos")
        .select("id, valor, status")
        .eq("status", status);

      // Aplicar limite de valor baseado nos direcionamentos do usuário
      if (userValueLimits) {
        console.log(`🔒 [STATS] Aplicando filtro de valor máximo para ${status}: R$ ${userValueLimits}`);
        query = query.lte("valor", userValueLimits);
      } else {
        console.log(`🔓 [STATS] Nenhum limite de valor aplicado para ${status}`);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`❌ [STATS] Erro ao buscar borderos ${status}:`, error);
        return [];
      }

      return data || [];
    };

    // Buscar estatísticas para cada status
    console.log("📊 [STATS] Iniciando busca de estatísticas...");

    const [novos, analise, assinados, pagos] = await Promise.all([
      getBorderosWithLimits("novo"),
      getBorderosWithLimits("analise"),
      getBorderosWithLimits("assinado"),
      getBorderosWithLimits("pago")
    ]);

    // Calcular valor total
    const todosBorderos = [...novos, ...analise, ...assinados, ...pagos];
    const valorTotal = todosBorderos.reduce((total, bordero) => total + (bordero.valor || 0), 0);

    const stats = {
      novos: novos.length,
      analise: analise.length,
      assinados: assinados.length,
      pagos: pagos.length,
      valorTotal
    };

    console.log("📊 [STATS] Estatísticas calculadas:", stats);

    // Log de verificação de limites
    if (userValueLimits && todosBorderos.length > 0) {
      const valores = todosBorderos.map(b => b.valor);
      const valorMaximo = Math.max(...valores);
      console.log(`💰 [STATS] Valor máximo nas estatísticas: R$ ${valorMaximo}`);
      console.log(`🔒 [STATS] Limite do usuário: R$ ${userValueLimits}`);
      
      if (valorMaximo > userValueLimits) {
        console.log(`⚠️ [STATS] PROBLEMA: Valor máximo (R$ ${valorMaximo}) excede limite (R$ ${userValueLimits})`);
      } else {
        console.log(`✅ [STATS] Todos os valores estão dentro do limite`);
      }
    }

    return NextResponse.json(stats);
  } catch (error) {
    console.error("💥 [STATS] Erro ao buscar estatísticas:", error);
    return NextResponse.json({ 
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
