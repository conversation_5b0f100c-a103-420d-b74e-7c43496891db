import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function PUT() {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const usuarioId = session.user.id

    const { error } = await supabase
      .from("notificacoes")
      .update({ lida: true })
      .eq("usuario_id", usuarioId)
      .eq("lida", false)

    if (error) {
      console.error("Erro ao marcar todas notificações como lidas:", error)
      return NextResponse.json({ error: "Erro ao marcar todas notificações como lidas" }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao marcar todas notificações como lidas:", error)
    return NextResponse.json({ error: "Erro ao marcar todas notificações como lidas" }, { status: 500 })
  }
}
