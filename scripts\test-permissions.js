const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function testPermissions() {
  try {
    console.log('🔍 Testando sistema de permissões...\n')

    // 1. Verificar níveis de acesso
    console.log('1. Verificando níveis de acesso:')
    const { data: niveisAcesso, error: niveisError } = await supabase
      .from('niveis_acesso')
      .select('*')
      .order('nome')

    if (niveisError) {
      console.error('❌ Erro ao buscar níveis de acesso:', niveisError.message)
      return
    }

    niveisAcesso.forEach(nivel => {
      console.log(`   - ${nivel.nome}: ${nivel.descricao}`)
      console.log(`     Permissões:`, JSON.stringify(nivel.permissoes, null, 6))
    })

    // 2. Verificar usuários e suas permissões
    console.log('\n2. Verificando usuários:')
    const { data: usuarios, error: usuariosError } = await supabase
      .from('usuarios')
      .select('*, niveis_acesso(nome, permissoes)')
      .order('nome')

    if (usuariosError) {
      console.error('❌ Erro ao buscar usuários:', usuariosError.message)
      return
    }

    usuarios.forEach(usuario => {
      console.log(`   - ${usuario.nome} (${usuario.email})`)
      console.log(`     Nível: ${usuario.niveis_acesso?.nome || 'Não definido'}`)
      if (usuario.niveis_acesso?.permissoes) {
        const permissoes = usuario.niveis_acesso.permissoes
        const permissoesAtivas = Object.entries(permissoes)
          .filter(([key, value]) => value === true)
          .map(([key]) => key)
        console.log(`     Permissões ativas: ${permissoesAtivas.join(', ')}`)
      }
    })

    // 3. Verificar se o admin tem todas as permissões
    console.log('\n3. Verificando permissões do administrador:')
    const { data: admin, error: adminError } = await supabase
      .from('usuarios')
      .select('*, niveis_acesso(nome, permissoes)')
      .eq('email', '<EMAIL>')
      .single()

    if (adminError) {
      console.error('❌ Erro ao buscar administrador:', adminError.message)
      return
    }

    if (admin) {
      console.log(`   ✅ Admin encontrado: ${admin.nome}`)
      console.log(`   ✅ Nível: ${admin.niveis_acesso?.nome}`)
      
      if (admin.niveis_acesso?.permissoes) {
        const permissoes = admin.niveis_acesso.permissoes
        const todasPermissoes = [
          'dashboard', 'borderos', 'secretarias', 'direcionamentos', 
          'tipos', 'usuarios', 'relatorios', 'configuracoes'
        ]
        
        const permissoesOK = todasPermissoes.every(perm => permissoes[perm] === true)
        
        if (permissoesOK) {
          console.log('   ✅ Admin tem todas as permissões necessárias')
        } else {
          console.log('   ❌ Admin não tem todas as permissões:')
          todasPermissoes.forEach(perm => {
            const status = permissoes[perm] ? '✅' : '❌'
            console.log(`      ${status} ${perm}`)
          })
        }
      }
    }

    console.log('\n🎉 Teste de permissões concluído!')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

testPermissions()
