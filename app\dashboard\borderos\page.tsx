"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { BorderoTableWithPagination } from "@/components/dashboard/bordero-table-with-pagination"
import FiltrosAvancados from "@/components/dashboard/filtros-avancados"
import { ProtectedRoute } from "@/components/dashboard/protected-route"
import { Plus, Upload } from "lucide-react"
import Link from "next/link"

export default function BorderosPage() {
  const [filtros, setFiltros] = useState({
    codigo: "",
    empresa: "",
    secretariaId: "",
    tipoId: "",
    status: "",
    valorMin: "",
    valorMax: "",
    dataInicio: "",
    dataFim: "",
  })

  const handleFiltrosChange = (novosFiltros: any) => {
    setFiltros(novosFiltros)
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Gerenciar Borderos</h1>
          <div className="flex gap-2">
            <Link href="/dashboard/borderos/importar">
              <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-50 dark:hover:bg-green-950">
                <Upload className="mr-2 h-4 w-4" />
                Importar Excel
              </Button>
            </Link>
            <Link href="/dashboard/borderos/novo">
              <Button className="bg-sky-600 hover:bg-sky-700">
                <Plus className="mr-2 h-4 w-4" />
                Novo Bordero
              </Button>
            </Link>
          </div>
        </div>

        <FiltrosAvancados
          onFiltrosChange={handleFiltrosChange}
          filtrosAtivos={filtros}
        />

        <Card>
          <CardHeader>
            <CardTitle>Todos os Borderos</CardTitle>
          </CardHeader>
          <CardContent>
            <BorderoTableWithPagination status="todos" filtros={filtros} />
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  )
}
