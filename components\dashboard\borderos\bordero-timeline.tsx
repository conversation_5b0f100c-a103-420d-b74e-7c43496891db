"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  MessageCircle,
  Edit,
  Trash2,
  PlusCircle,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Send,
  RotateCcw
} from "lucide-react"
import { safeFormatDistanceToNow } from "@/lib/date-utils"
import { toast } from "@/hooks/use-toast"

interface TimelineItem {
  id: string
  tipo: 'log'
  acao: string
  usuario: {
    id: string
    nome: string
    email: string
  }
  detalhes: any
  created_at: string
  data_hora?: string
}

interface BorderoTimelineProps {
  borderoId: string
  borderoCode: string
}

export function BorderoTimeline({ borderoId, borderoCode }: BorderoTimelineProps) {
  const [timeline, setTimeline] = useState<TimelineItem[]>([])
  const [loading, setLoading] = useState(true)

  const renderDetalhes = (detalhes: any): string => {
    if (typeof detalhes === 'string') {
      return detalhes
    }

    if (typeof detalhes === 'object' && detalhes !== null) {
      // Casos específicos para diferentes tipos de detalhes
      if (detalhes.comentario) {
        return `Comentário adicionado: "${detalhes.comentario.substring(0, 100)}${detalhes.comentario.length > 100 ? '...' : ''}"`
      }

      // Comentários editados/excluídos
      if (detalhes.comentario_anterior && detalhes.comentario_novo) {
        return `Comentário editado`
      }

      if (detalhes.comentario_excluido) {
        return `Comentário excluído`
      }

      if (detalhes.alteracoes && Array.isArray(detalhes.alteracoes)) {
        // Traduzir alterações para linguagem amigável
        const alteracoesAmigaveis = detalhes.alteracoes.map((alteracao: string) => {
          if (alteracao.includes('Código alterado')) return alteracao
          if (alteracao.includes('Valor alterado')) return alteracao
          if (alteracao.includes('Data alterada')) return alteracao
          if (alteracao.includes('Empresa alterada')) return alteracao
          if (alteracao.includes('Secretaria alterada')) return 'Secretaria foi alterada'
          if (alteracao.includes('Tipo alterado')) return 'Tipo foi alterado'
          if (alteracao.includes('Observação alterada')) return 'Observação foi alterada'
          return alteracao
        })
        return alteracoesAmigaveis.join('; ')
      }

      if (detalhes.status_anterior && detalhes.status_novo) {
        return `Status alterado de "${detalhes.status_anterior}" para "${detalhes.status_novo}"`
      }

      if (detalhes.bordero_cod && detalhes.alteracoes) {
        // Caso específico para edições com código do bordero
        const alteracoesAmigaveis = detalhes.alteracoes.map((alteracao: string) => {
          if (alteracao.includes('Observação alterada')) return 'Observação foi alterada'
          if (alteracao.includes('Secretaria alterada')) return 'Secretaria foi alterada'
          if (alteracao.includes('Tipo alterado')) return 'Tipo foi alterado'
          return alteracao
        })
        return `Bordero ${detalhes.bordero_cod} foi atualizado: ${alteracoesAmigaveis.join('; ')}`
      }

      if (detalhes.bordero_cod) {
        return `Bordero ${detalhes.bordero_cod} foi processado`
      }

      if (detalhes.message) {
        return detalhes.message
      }

      // Fallback mais amigável
      if (detalhes.motivo) {
        return `Motivo: ${detalhes.motivo}`
      }

      // Para objetos simples, tentar extrair informação útil
      const keys = Object.keys(detalhes)
      if (keys.length === 1 && keys[0] !== 'bordero_cod') {
        const key = keys[0]
        const value = detalhes[key]

        // Traduzir chaves comuns
        const keyTranslations: Record<string, string> = {
          'valor': 'Valor',
          'data': 'Data',
          'empresa': 'Empresa',
          'secretaria': 'Secretaria',
          'tipo': 'Tipo',
          'observacao': 'Observação',
          'status': 'Status'
        }

        const friendlyKey = keyTranslations[key] || key
        return `${friendlyKey}: ${value}`
      }

      // Se ainda tiver o formato JSON bruto, tentar melhorar
      if (keys.includes('alteracoes') && keys.includes('bordero_cod')) {
        return `Bordero ${detalhes.bordero_cod} foi atualizado`
      }

      return 'Bordero foi atualizado'
    }

    return 'Ação realizada'
  }

  useEffect(() => {
    fetchTimeline()
  }, [borderoId])

  const fetchTimeline = async () => {
    try {
      setLoading(true)

      // Buscar APENAS logs do bordero (sem comentários)
      const logsResponse = await fetch(`/api/borderos/${borderoId}/logs`)
      const logs = logsResponse.ok ? await logsResponse.json() : []

      // Filtrar apenas logs administrativos (não comentários)
      const timelineItems: TimelineItem[] = logs
        .filter((log: any) =>
          log.acao !== 'comentario' &&
          log.acao !== 'comentario-editado' &&
          log.acao !== 'comentario-excluido'
        )
        .map((log: any) => ({
          ...log,
          tipo: 'log' as const,
          created_at: log.data_hora || log.created_at
        }))
        .sort((a: any, b: any) => {
          const dateA = new Date(a.created_at)
          const dateB = new Date(b.created_at)
          return dateB.getTime() - dateA.getTime() // Mais recentes primeiro
        })

      setTimeline(timelineItems)
    } catch (error) {
      console.error("Erro ao carregar timeline:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar o histórico do bordero.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Função de comentário removida - comentários agora são gerenciados separadamente

  const renderIconeAcao = (acao: string) => {
    switch (acao) {
      case 'criar':
        return <PlusCircle className="h-4 w-4 text-green-600" />
      case 'editar':
        return <Edit className="h-4 w-4 text-blue-600" />
      case 'status':
        return <RotateCcw className="h-4 w-4 text-purple-600" />
      case 'cancelar':
      case 'excluir':
        return <Trash2 className="h-4 w-4 text-red-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getCorAcao = (acao: string) => {
    switch (acao) {
      case 'criar':
        return 'bg-green-100 border-green-200 dark:bg-green-900/20 dark:border-green-800'
      case 'editar':
        return 'bg-blue-100 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
      case 'status':
        return 'bg-purple-100 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800'
      case 'cancelar':
      case 'excluir':
        return 'bg-red-100 border-red-200 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'bg-gray-100 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
    }
  }

  const formatarDetalhesLog = (tipo: string, detalhes: any): string => {
    // Primeiro, verificar se é string
    if (typeof detalhes === 'string') {
      return detalhes
    }

    // Se é objeto, processar casos específicos
    if (typeof detalhes === 'object' && detalhes !== null) {
      // Caso específico: comentários
      if (detalhes.comentario) {
        const comentarioTexto = detalhes.comentario.length > 100
          ? `${detalhes.comentario.substring(0, 100)}...`
          : detalhes.comentario
        return `Comentário adicionado: "${comentarioTexto}"`
      }

      // Caso específico: comentários editados/excluídos
      if (detalhes.comentario_anterior && detalhes.comentario_novo) {
        return `Comentário editado`
      }

      if (detalhes.comentario_excluido) {
        return `Comentário excluído`
      }

      // Caso específico: alterações
      if (detalhes.alteracoes && Array.isArray(detalhes.alteracoes)) {
        const alteracoesAmigaveis = detalhes.alteracoes.map((alteracao: string) => {
          if (alteracao.includes('Código alterado')) return alteracao
          if (alteracao.includes('Valor alterado')) return alteracao
          if (alteracao.includes('Data alterada')) return alteracao
          if (alteracao.includes('Empresa alterada')) return alteracao
          if (alteracao.includes('Secretaria alterada')) return 'Secretaria foi alterada'
          if (alteracao.includes('Tipo alterado')) return 'Tipo foi alterado'
          if (alteracao.includes('Observação alterada')) return 'Observação foi alterada'
          return alteracao
        })
        return alteracoesAmigaveis.join('; ')
      }

      // Caso específico: mudança de status
      if (detalhes.status_anterior && detalhes.status_novo) {
        return `Status alterado de "${formatarStatus(detalhes.status_anterior)}" para "${formatarStatus(detalhes.status_novo)}"`
      }

      // Caso específico: motivo
      if (detalhes.motivo) {
        return `Motivo: ${detalhes.motivo}`
      }

      // Caso específico: bordero com código
      if (detalhes.bordero_cod && !detalhes.comentario && !detalhes.alteracoes) {
        return `Bordero ${detalhes.bordero_cod} foi processado`
      }
    }

    // Fallback baseado no tipo de ação
    switch (tipo) {
      case 'criar':
        if (detalhes?.bordero_cod) {
          return `Bordero ${detalhes.bordero_cod} foi criado`
        }
        return 'Bordero criado'

      case 'editar':
        if (detalhes?.bordero_cod) {
          return `Bordero ${detalhes.bordero_cod} foi editado`
        }
        return 'Bordero editado'

      case 'status':
        return 'Status alterado'

      case 'cancelar':
      case 'excluir':
        if (detalhes?.bordero_cod) {
          return `Bordero ${detalhes.bordero_cod} foi cancelado`
        }
        return 'Bordero cancelado'

      case 'comentario':
        return 'Comentário adicionado'

      default:
        return 'Ação realizada no bordero'
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  const formatarNomeCampo = (campo: string): string => {
    const nomes: Record<string, string> = {
      'bordero_cod': 'Código',
      'valor': 'Valor',
      'data': 'Data',
      'nome_empresa': 'Empresa',
      'secretaria_id': 'Secretaria',
      'tipo_id': 'Tipo',
      'observacao': 'Observação',
      'responsavel_id': 'Responsável',
      'status': 'Status'
    }
    return nomes[campo] || campo
  }

  const formatarStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'novo': 'Novo',
      'analise': 'Em Análise',
      'assinado': 'Assinado',
      'pago': 'Pago',
      'corrigir': 'Corrigir',
      'cancelado': 'Cancelado',
      'excluido': 'Excluído',
      'arquivado': 'Arquivado'
    }
    return statusMap[status] || status
  }

  const formatarMoeda = (valor: number | string): string => {
    const numericValue = typeof valor === 'string' ? parseFloat(valor) : valor

    if (isNaN(numericValue)) {
      return 'R$ 0,00'
    }

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numericValue)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Timeline do Bordero
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Timeline do Bordero {borderoCode}
        </CardTitle>
      </CardHeader>
      <CardContent>

        {/* Timeline */}
        <div className="space-y-4">
          {timeline.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma atividade registrada ainda.</p>
            </div>
          ) : (
            timeline.map((item, index) => (
              <div key={item.id} className="flex gap-4">
                {/* Linha vertical */}
                <div className="flex flex-col items-center">
                  <div className={`p-2 rounded-full border-2 ${getCorAcao(item.acao)}`}>
                    {renderIconeAcao(item.acao)}
                  </div>
                  {index < timeline.length - 1 && (
                    <div className="w-px h-16 bg-gray-200 dark:bg-gray-700 mt-2"></div>
                  )}
                </div>

                {/* Conteúdo */}
                <div className="flex-1 pb-8">
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs bg-slate-100 dark:bg-slate-800">
                        {item.usuario.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium text-sm dark:text-white">{item.usuario.nome}</span>
                    <Badge variant="outline" className="text-xs border-slate-200 dark:border-slate-700 dark:text-slate-300">
                      Sistema
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {safeFormatDistanceToNow(item.created_at)}
                    </span>
                  </div>

                  <div className="text-sm text-slate-700 dark:text-slate-100 bg-slate-50 dark:bg-slate-800/40 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
                    {formatarDetalhesLog(item.acao, item.detalhes)}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
