"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface EmailInputProps extends React.ComponentProps<"input"> {
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}

const EMAIL_SUGGESTIONS = [
  "gmail.com",
  "hotmail.com",
  "outlook.com",
  "yahoo.com",
  "uol.com.br",
  "terra.com.br",
  "bol.com.br",
  "ig.com.br",
  "globo.com",
  "r7.com",
  "live.com",
  "icloud.com"
]

export const EmailInput = React.forwardRef<HTMLInputElement, EmailInputProps>(
  ({ className, value, onChange, ...props }, ref) => {
    const [mounted, setMounted] = React.useState(false)
    const [suggestions, setSuggestions] = React.useState<string[]>([])
    const [showSuggestions, setShowSuggestions] = React.useState(false)
    const [selectedIndex, setSelectedIndex] = React.useState(-1)
    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useEffect(() => {
      setMounted(true)
    }, [])

    React.useImperativeHandle(ref, () => inputRef.current!)

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      onChange(e)

      // Só processar sugestões se o componente estiver montado
      if (!mounted) return

      // Verificar se contém @ e gerar sugestões
      if (inputValue.includes("@")) {
        const [localPart, domainPart] = inputValue.split("@")

        if (domainPart !== undefined) {
          const filteredSuggestions = EMAIL_SUGGESTIONS
            .filter(domain => domain.toLowerCase().startsWith(domainPart.toLowerCase()))
            .map(domain => `${localPart}@${domain}`)
            .slice(0, 5)

          setSuggestions(filteredSuggestions)
          setShowSuggestions(filteredSuggestions.length > 0 && domainPart !== "")
          setSelectedIndex(-1)
        }
      } else {
        setShowSuggestions(false)
        setSuggestions([])
      }
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!showSuggestions) return

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault()
          setSelectedIndex(prev =>
            prev < suggestions.length - 1 ? prev + 1 : 0
          )
          break
        case "ArrowUp":
          e.preventDefault()
          setSelectedIndex(prev =>
            prev > 0 ? prev - 1 : suggestions.length - 1
          )
          break
        case "Enter":
          e.preventDefault()
          if (selectedIndex >= 0) {
            selectSuggestion(suggestions[selectedIndex])
          }
          break
        case "Escape":
          setShowSuggestions(false)
          setSelectedIndex(-1)
          break
      }
    }

    const selectSuggestion = (suggestion: string) => {
      const syntheticEvent = {
        target: { value: suggestion }
      } as React.ChangeEvent<HTMLInputElement>

      onChange(syntheticEvent)
      setShowSuggestions(false)
      setSelectedIndex(-1)
      inputRef.current?.focus()
    }

    const handleBlur = () => {
      // Delay para permitir clique nas sugestões
      setTimeout(() => {
        setShowSuggestions(false)
        setSelectedIndex(-1)
      }, 200)
    }

    return (
      <div className="relative">
        <Input
          ref={inputRef}
          type="email"
          className={className}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={() => {
            if (mounted && suggestions.length > 0 && value.includes("@")) {
              setShowSuggestions(true)
            }
          }}
          {...props}
        />

        {mounted && showSuggestions && suggestions.length > 0 && (
          <div className="absolute z-[9999] w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
            {suggestions.map((suggestion, index) => (
              <div
                key={suggestion}
                className={cn(
                  "px-3 py-2 cursor-pointer text-sm",
                  index === selectedIndex
                    ? "bg-sky-100 dark:bg-sky-900 text-sky-900 dark:text-sky-100"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100"
                )}
                onClick={() => selectSuggestion(suggestion)}
              >
                {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }
)

EmailInput.displayName = "EmailInput"
