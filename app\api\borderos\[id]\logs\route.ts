import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar logs específicos do bordero
    const { data: borderoLogs, error: borderoError } = await supabase
      .from("bordero_logs")
      .select("*")
      .eq("bordero_id", id)
      .order("data_hora", { ascending: false })

    // Buscar logs gerais do bordero
    const { data: generalLogs, error: generalError } = await supabase
      .from("log_atividades")
      .select("id, acao, detalhes, created_at, usuario_id")
      .eq("entidade_id", id)
      .eq("entidade", "bordero")
      .order("created_at", { ascending: false })

    // Se ambas as consultas falharem, retornar logs simulados
    if (borderoError && generalError) {
      console.error("Erro ao buscar logs:", { borderoError, generalError })

      // Retornar logs simulados como fallback
      const mockLogs = [
        {
          id: "mock_1",
          acao: "criar",
          detalhes: "Bordero criado no sistema",
          data_hora: new Date().toISOString(),
          usuario: {
            nome: "Sistema",
            email: "<EMAIL>"
          }
        }
      ]
      return NextResponse.json(mockLogs)
    }

    // Combinar e formatar logs
    const allLogs = []

    // Adicionar logs específicos do bordero
    if (borderoLogs && !borderoError) {
      allLogs.push(...borderoLogs.map(log => ({
        id: log.id,
        acao: log.acao,
        detalhes: typeof log.detalhes === 'object' ?
          JSON.stringify(log.detalhes) :
          log.detalhes,
        data_hora: log.data_hora,
        usuario_id: log.usuario_id
      })))
    }

    // Adicionar logs gerais
    if (generalLogs && !generalError) {
      allLogs.push(...generalLogs.map(log => ({
        id: `general_${log.id}`,
        acao: log.acao,
        detalhes: typeof log.detalhes === 'object' ?
          JSON.stringify(log.detalhes) :
          log.detalhes,
        data_hora: log.created_at,
        usuario_id: log.usuario_id
      })))
    }

    // Buscar usuários para os logs
    const usuarioIds = [...new Set(allLogs.map(log => log.usuario_id).filter(Boolean))]
    let usuarios = []

    if (usuarioIds.length > 0) {
      const { data: usuariosData, error: usuariosError } = await supabase
        .from("usuarios")
        .select("id, nome, email")
        .in("id", usuarioIds)

      if (!usuariosError && usuariosData) {
        usuarios = usuariosData
      }
    }

    // Mapear usuários para os logs
    const usuariosMap = new Map(usuarios.map(u => [u.id, u]))
    const logsComUsuarios = allLogs.map(log => ({
      ...log,
      usuario: usuariosMap.get(log.usuario_id) || {
        nome: "Usuário Desconhecido",
        email: "<EMAIL>"
      }
    }))

    // Ordenar por data
    logsComUsuarios.sort((a, b) => new Date(b.data_hora).getTime() - new Date(a.data_hora).getTime())

    return NextResponse.json(logsComUsuarios)
  } catch (error) {
    console.error("Erro ao buscar logs:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
