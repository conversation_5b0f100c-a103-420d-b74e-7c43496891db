"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Upload, User } from "lucide-react"
import { useSupabase } from "@/lib/supabase-provider"

interface PerfilData {
  nome: string
  email: string
  avatar: string
  senhaAtual: string
  novaSenha: string
  confirmarSenha: string
}

export default function PerfilPage() {
  const { toast } = useToast()
  const { user } = useSupabase()
  const [loading, setLoading] = useState(false)
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [perfilData, setPerfilData] = useState<PerfilData>({
    nome: "",
    email: "",
    avatar: "",
    senhaAtual: "",
    novaSenha: "",
    confirmarSenha: "",
  })

  useEffect(() => {
    if (user) {
      fetchUserProfile()
    }
  }, [user])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch("/api/auth/profile")
      if (response.ok) {
        const data = await response.json()
        setPerfilData(prev => ({
          ...prev,
          nome: data.nome || "",
          email: data.email || "",
          avatar: data.avatar_url || "",
        }))
      }
    } catch (error) {
      console.error("Erro ao carregar perfil:", error)
    }
  }

  const handleInputChange = (field: keyof PerfilData, value: string) => {
    setPerfilData(prev => ({ ...prev, [field]: value }))
  }

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setAvatarFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setPerfilData(prev => ({ ...prev, avatar: e.target?.result as string }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validações
      if (perfilData.novaSenha && perfilData.novaSenha !== perfilData.confirmarSenha) {
        throw new Error("As senhas não coincidem")
      }

      if (perfilData.novaSenha && !perfilData.senhaAtual) {
        throw new Error("Senha atual é obrigatória para alterar a senha")
      }

      let avatarUrl = perfilData.avatar

      // Upload do avatar se houver arquivo
      if (avatarFile) {
        const formData = new FormData()
        formData.append("avatar", avatarFile)

        const uploadResponse = await fetch("/api/upload/avatar", {
          method: "POST",
          body: formData,
        })

        if (!uploadResponse.ok) {
          throw new Error("Erro ao fazer upload do avatar")
        }

        const uploadData = await uploadResponse.json()
        avatarUrl = uploadData.url
      }

      const response = await fetch("/api/auth/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          nome: perfilData.nome,
          avatar: avatarUrl,
          senhaAtual: perfilData.senhaAtual,
          novaSenha: perfilData.novaSenha,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao atualizar perfil")
      }

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso.",
      })

      // Limpar campos de senha
      setPerfilData(prev => ({
        ...prev,
        senhaAtual: "",
        novaSenha: "",
        confirmarSenha: "",
      }))
      setAvatarFile(null)
    } catch (error: any) {
      console.error("Erro ao atualizar perfil:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível atualizar o perfil.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <User className="h-8 w-8 text-sky-600" />
          <h1 className="text-3xl font-bold tracking-tight">Meu Perfil</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informações Pessoais</CardTitle>
          <CardDescription>Atualize suas informações pessoais e configurações de conta</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="perfil-avatar">Foto do Perfil</Label>
              <div className="flex items-center gap-4">
                {perfilData.avatar && (
                  <img
                    src={perfilData.avatar}
                    alt="Avatar atual"
                    className="w-16 h-16 rounded-full object-cover"
                  />
                )}
                <div className="flex flex-col gap-2">
                  <Input
                    id="perfil-avatar"
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="w-auto"
                  />
                  <p className="text-xs text-muted-foreground">
                    Formatos aceitos: JPG, PNG, GIF. Tamanho máximo: 2MB
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="perfil-nome">Nome Completo</Label>
                <Input
                  id="perfil-nome"
                  value={perfilData.nome}
                  onChange={(e) => handleInputChange("nome", e.target.value)}
                  placeholder="Seu nome completo"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-email">E-mail</Label>
                <Input
                  id="perfil-email"
                  type="email"
                  value={perfilData.email}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  O e-mail não pode ser alterado
                </p>
              </div>
            </div>

            <div className="border-t pt-6">
              <h3 className="text-lg font-medium mb-4">Alterar Senha</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="senha-atual">Senha Atual</Label>
                  <Input
                    id="senha-atual"
                    type="password"
                    value={perfilData.senhaAtual}
                    onChange={(e) => handleInputChange("senhaAtual", e.target.value)}
                    placeholder="Digite sua senha atual"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nova-senha">Nova Senha</Label>
                  <Input
                    id="nova-senha"
                    type="password"
                    value={perfilData.novaSenha}
                    onChange={(e) => handleInputChange("novaSenha", e.target.value)}
                    placeholder="Digite a nova senha"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmar-senha">Confirmar Nova Senha</Label>
                  <Input
                    id="confirmar-senha"
                    type="password"
                    value={perfilData.confirmarSenha}
                    onChange={(e) => handleInputChange("confirmarSenha", e.target.value)}
                    placeholder="Confirme a nova senha"
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Deixe em branco se não quiser alterar a senha
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={loading} className="bg-sky-600 hover:bg-sky-700">
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Salvar Alterações
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
