"use client"

import { useState } from "react"
import { useSupabase } from "@/lib/supabase-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Database,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  RefreshCw,
  Clock,
  Server,
  Shield,
  FileText,
  Settings,
  User
} from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface VerificacaoResult {
  status: string
  message: string
  details: any
  timestamp: string
}

export default function DebugPage() {
  const { user, userDetails, userPermissions, loading } = useSupabase()
  const [verificacao, setVerificacao] = useState<VerificacaoResult | null>(null)
  const [loadingVerificacao, setLoadingVerificacao] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const handleRefresh = () => {
    setRefreshing(true)
    window.location.reload()
  }

  const verificarConexao = async () => {
    setLoadingVerificacao(true)

    try {
      const response = await fetch('/api/debug/verificar-conexao')
      const data = await response.json()

      setVerificacao(data)

      if (data.status === 'ok') {
        toast({
          title: "✅ Conexão OK",
          description: "Todos os sistemas estão funcionando corretamente!"
        })
      } else {
        toast({
          title: "⚠️ Problemas Detectados",
          description: data.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Erro ao verificar conexão:", error)
      toast({
        title: "❌ Erro",
        description: "Não foi possível verificar a conexão.",
        variant: "destructive"
      })
    } finally {
      setLoadingVerificacao(false)
    }
  }

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    )
  }

  const getStatusBadge = (status: boolean, label: string) => {
    return (
      <Badge variant={status ? "default" : "destructive"} className="text-xs">
        {status ? "✅" : "❌"} {label}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Debug de Permissões</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Carregando informações...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Debug do Sistema</h1>
          <p className="text-gray-600">Verificação de conexão, permissões e diagnósticos</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={verificarConexao}
            disabled={loadingVerificacao}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loadingVerificacao ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verificando...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Verificar Conexão
              </>
            )}
          </Button>
          <Button onClick={handleRefresh} disabled={refreshing} variant="outline">
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Verificação de Conexão */}
      {verificacao && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Status da Conexão
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              {getStatusIcon(verificacao.status === 'ok')}
              <div>
                <p className="font-medium">{verificacao.message}</p>
                <p className="text-sm text-gray-600">
                  Verificado em: {new Date(verificacao.timestamp).toLocaleString('pt-BR')}
                </p>
              </div>
            </div>

            {verificacao.details.verificacao_sistema && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Tabelas do Sistema:</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {verificacao.details.verificacao_sistema.tabelas_principais.slice(0, 8).map((tabela: any) => (
                    <div key={tabela.tabela} className="flex items-center gap-1 text-sm">
                      {getStatusIcon(tabela.existe)}
                      <span className="font-mono">{tabela.tabela}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Separator />

      <div className="grid gap-6 md:grid-cols-2">
        {/* Usuário Autenticado */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <User className="h-4 w-4 mr-2" />
            <CardTitle className="text-sm font-medium">Usuário Autenticado</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={user ? "default" : "destructive"}>
                  {user ? "✅ Sim" : "❌ Não"}
                </Badge>
              </div>
              {user && (
                <div className="text-sm text-muted-foreground">
                  <p><strong>ID:</strong> {user.id}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Criado em:</strong> {new Date(user.created_at).toLocaleString('pt-BR')}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Detalhes do Usuário */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <Shield className="h-4 w-4 mr-2" />
            <CardTitle className="text-sm font-medium">Detalhes do Usuário</CardTitle>
          </CardHeader>
          <CardContent>
            {userDetails ? (
              <div className="space-y-2">
                <p><strong>Nome:</strong> {userDetails.nome}</p>
                <p><strong>Email:</strong> {userDetails.email}</p>
                <p><strong>Nível de Acesso:</strong> {userDetails.nivel_acesso}</p>
                <div className="flex items-center gap-2">
                  <span><strong>É Admin:</strong></span>
                  <Badge variant={isAdmin() ? "default" : "secondary"}>
                    {isAdmin() ? "✅ Sim" : "❌ Não"}
                  </Badge>
                </div>
              </div>
            ) : (
              <Badge variant="destructive">❌ Não carregado</Badge>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Permissões */}
      <Card>
        <CardHeader className="flex flex-row items-center space-y-0 pb-2">
          <Settings className="h-4 w-4 mr-2" />
          <CardTitle className="text-sm font-medium">Permissões</CardTitle>
        </CardHeader>
        <CardContent>
          {userPermissions ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(userPermissions).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <Badge variant={value ? "default" : "secondary"} className="w-full justify-center">
                    {key}: {value ? "✅" : "❌"}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <Badge variant="destructive">❌ Não carregadas</Badge>
          )}
        </CardContent>
      </Card>

      {/* Teste de Permissões */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Teste de Permissões</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[
              "dashboard",
              "borderos",
              "secretarias",
              "direcionamentos",
              "tipos",
              "usuarios",
              "relatorios",
              "configuracoes"
            ].map((permission) => (
              <div key={permission} className="flex items-center gap-2">
                <Badge
                  variant={hasPermission(permission as any) ? "default" : "secondary"}
                  className="w-full justify-center"
                >
                  {permission}: {hasPermission(permission as any) ? "✅" : "❌"}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Informações Técnicas */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Informações Técnicas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Loading State:</strong> {loading ? "Carregando..." : "Carregado"}</p>
            <p><strong>User Object:</strong> {user ? "Presente" : "Ausente"}</p>
            <p><strong>User Details:</strong> {userDetails ? "Presente" : "Ausente"}</p>
            <p><strong>User Permissions:</strong> {userPermissions ? "Presente" : "Ausente"}</p>
            <p><strong>Timestamp:</strong> {new Date().toLocaleString('pt-BR')}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
