import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("Configurando tabela configuracoes_sistema...")

    // Tentar inserir um registro para forçar a criação da tabela
    const { error } = await supabase
      .from('configuracoes_sistema')
      .insert({
        nome: 'CRM de Bordero',
        modo_escuro: false,
        notificacoes_email: true
      })

    if (error) {
      console.error("Erro ao criar configuracoes_sistema:", error)
      
      // Retornar SQL para criação manual
      const createTableSQL = `
-- Criar tabela configuracoes_sistema
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
  modo_escuro BOOLEAN DEFAULT false,
  notificacoes_email BOOLEAN DEFAULT true,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserir configuração padrão
INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
SELECT 'CRM de Bordero', false, true
WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);

-- Habilitar RLS (Row Level Security)
ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir acesso total (ajustar conforme necessário)
CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
FOR ALL USING (true);
      `

      return NextResponse.json({
        success: false,
        message: "Tabela configuracoes_sistema não existe. Execute o SQL abaixo no Supabase SQL Editor:",
        sql: createTableSQL,
        error: error.message
      }, { status: 500 })
    }

    // Verificar se a inserção foi bem-sucedida
    const { data: configs, error: selectError } = await supabase
      .from('configuracoes_sistema')
      .select('*')

    if (selectError) {
      return NextResponse.json({
        success: false,
        message: "Erro ao verificar configurações",
        error: selectError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: "Tabela configuracoes_sistema configurada com sucesso",
      data: configs
    })

  } catch (error) {
    console.error("Erro ao configurar configuracoes_sistema:", error)
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
