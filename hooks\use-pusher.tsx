"use client"

import { useState, useEffect, useRef } from "react"
import { createPusherClient } from "@/lib/pusher"
import type PusherClient from "pusher-js"

export function usePusher() {
  const [pusherClient, setPusherClient] = useState<PusherClient | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const retryCount = useRef(0)
  const maxRetries = 3

  useEffect(() => {
    let isMounted = true
    let timeoutId: NodeJS.Timeout

    const initializePusher = async () => {
      try {
        if (!isMounted) return

        setLoading(true)
        // Obter a chave e o cluster do Pusher via API
        const response = await fetch("/api/pusher-key")

        if (!response.ok) {
          throw new Error(`Erro ao obter chave do Pusher: ${response.status}`)
        }

        const data = await response.json()

        if (!data.key || !data.cluster) {
          throw new Error("Chave ou cluster do Pusher não encontrados")
        }

        // Criar o cliente Pusher
        const client = createPusherClient(data.key, data.cluster)

        if (isMounted) {
          setPusherClient(client)
          setError(null)
          retryCount.current = 0
        }
      } catch (err) {
        console.error("Erro ao inicializar o Pusher:", err)

        if (isMounted) {
          setError(err instanceof Error ? err : new Error(String(err)))

          // Tentar novamente se não excedeu o número máximo de tentativas
          if (retryCount.current < maxRetries) {
            retryCount.current += 1
            const delay = Math.pow(2, retryCount.current) * 1000 // Backoff exponencial
            console.log(`Tentando reconectar em ${delay}ms (tentativa ${retryCount.current})`)

            timeoutId = setTimeout(initializePusher, delay)
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    initializePusher()

    // Cleanup
    return () => {
      isMounted = false
      if (timeoutId) clearTimeout(timeoutId)

      if (pusherClient) {
        pusherClient.disconnect()
      }
    }
  }, [])

  return { pusherClient, loading, error }
}
