export type NivelAcesso = 'admin' | 'gerente' | 'operador' | 'visualizador'

export interface Permissoes {
  // Borderos
  borderos: {
    visualizar: boolean
    criar: boolean
    editar: boolean
    excluir: boolean
    comentar: boolean
    alterar_status: boolean
  }
  
  // Usuários
  usuarios: {
    visualizar: boolean
    criar: boolean
    editar: boolean
    excluir: boolean
    gerenciar_permissoes: boolean
  }
  
  // Secretarias
  secretarias: {
    visualizar: boolean
    criar: boolean
    editar: boolean
    excluir: boolean
  }
  
  // Tipos
  tipos: {
    visualizar: boolean
    criar: boolean
    editar: boolean
    excluir: boolean
  }
  
  // Direcionamentos
  direcionamentos: {
    visualizar: boolean
    criar: boolean
    editar: boolean
    excluir: boolean
  }
  
  // Relatórios
  relatorios: {
    visualizar: boolean
    exportar: boolean
    avancados: boolean
  }
  
  // Configurações
  configuracoes: {
    visualizar: boolean
    editar: boolean
    sistema: boolean
  }
  
  // Setup e Manutenção
  setup: {
    acessar: boolean
    executar: boolean
    logs: boolean
  }
  
  // Notificações
  notificacoes: {
    visualizar: boolean
    gerenciar: boolean
  }
}

export const PERMISSOES_POR_NIVEL: Record<NivelAcesso, Permissoes> = {
  admin: {
    borderos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: true,
      comentar: true,
      alterar_status: true
    },
    usuarios: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: true,
      gerenciar_permissoes: true
    },
    secretarias: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: true
    },
    tipos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: true
    },
    direcionamentos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: true
    },
    relatorios: {
      visualizar: true,
      exportar: true,
      avancados: true
    },
    configuracoes: {
      visualizar: true,
      editar: true,
      sistema: true
    },
    setup: {
      acessar: true,
      executar: true,
      logs: true
    },
    notificacoes: {
      visualizar: true,
      gerenciar: true
    }
  },
  
  gerente: {
    borderos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: false,
      comentar: true,
      alterar_status: true
    },
    usuarios: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false,
      gerenciar_permissoes: false
    },
    secretarias: {
      visualizar: true,
      criar: false,
      editar: true,
      excluir: false
    },
    tipos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: false
    },
    direcionamentos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    relatorios: {
      visualizar: true,
      exportar: true,
      avancados: true
    },
    configuracoes: {
      visualizar: true,
      editar: false,
      sistema: false
    },
    setup: {
      acessar: false,
      executar: false,
      logs: false
    },
    notificacoes: {
      visualizar: true,
      gerenciar: false
    }
  },
  
  operador: {
    borderos: {
      visualizar: true,
      criar: true,
      editar: true,
      excluir: false,
      comentar: true,
      alterar_status: true
    },
    usuarios: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false,
      gerenciar_permissoes: false
    },
    secretarias: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    tipos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    direcionamentos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    relatorios: {
      visualizar: true,
      exportar: false,
      avancados: false
    },
    configuracoes: {
      visualizar: false,
      editar: false,
      sistema: false
    },
    setup: {
      acessar: false,
      executar: false,
      logs: false
    },
    notificacoes: {
      visualizar: true,
      gerenciar: false
    }
  },
  
  visualizador: {
    borderos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false,
      comentar: true,
      alterar_status: false
    },
    usuarios: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false,
      gerenciar_permissoes: false
    },
    secretarias: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    tipos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    direcionamentos: {
      visualizar: true,
      criar: false,
      editar: false,
      excluir: false
    },
    relatorios: {
      visualizar: true,
      exportar: false,
      avancados: false
    },
    configuracoes: {
      visualizar: false,
      editar: false,
      sistema: false
    },
    setup: {
      acessar: false,
      executar: false,
      logs: false
    },
    notificacoes: {
      visualizar: true,
      gerenciar: false
    }
  }
}

export function getPermissoes(nivelAcesso: NivelAcesso): Permissoes {
  return PERMISSOES_POR_NIVEL[nivelAcesso] || PERMISSOES_POR_NIVEL.visualizador
}

export function hasPermission(
  nivelAcesso: NivelAcesso,
  modulo: keyof Permissoes,
  acao: string
): boolean {
  const permissoes = getPermissoes(nivelAcesso)
  const moduloPermissoes = permissoes[modulo] as any
  return moduloPermissoes?.[acao] || false
}

export function canAccessRoute(nivelAcesso: NivelAcesso, route: string): boolean {
  const permissoes = getPermissoes(nivelAcesso)
  
  // Mapear rotas para permissões
  const routePermissions: Record<string, boolean> = {
    '/dashboard': true, // Todos podem acessar o dashboard
    '/dashboard/borderos': permissoes.borderos.visualizar,
    '/dashboard/borderos/novo': permissoes.borderos.criar,
    '/dashboard/usuarios': permissoes.usuarios.visualizar,
    '/dashboard/secretarias': permissoes.secretarias.visualizar,
    '/dashboard/configuracoes': permissoes.configuracoes.visualizar,
    '/dashboard/setup': permissoes.setup.acessar,
    '/dashboard/relatorios': permissoes.relatorios.visualizar,
  }
  
  return routePermissions[route] ?? false
}

// Hook para usar permissões em componentes
export function usePermissions(nivelAcesso: NivelAcesso) {
  const permissoes = getPermissoes(nivelAcesso)
  
  return {
    permissoes,
    hasPermission: (modulo: keyof Permissoes, acao: string) => 
      hasPermission(nivelAcesso, modulo, acao),
    canAccessRoute: (route: string) => 
      canAccessRoute(nivelAcesso, route)
  }
}
