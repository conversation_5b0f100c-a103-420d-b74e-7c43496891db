import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { nome } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome da secretaria é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Gerar novo slug se o nome mudou
    const slug = nome
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim()

    // Verificar se o slug já existe (exceto para a própria secretaria)
    const { data: existingSecretaria, error: checkError } = await supabase
      .from("secretarias")
      .select("id")
      .eq("slug", slug)
      .neq("id", id)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar slug:", checkError)
      return NextResponse.json({ error: "Erro ao verificar slug" }, { status: 500 })
    }

    if (existingSecretaria) {
      return NextResponse.json({ error: "Já existe uma secretaria com este nome" }, { status: 400 })
    }

    const { data: secretaria, error } = await supabase
      .from("secretarias")
      .update({
        nome: nome.trim(),
        slug,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Erro ao atualizar secretaria:", error)
      return NextResponse.json({ error: "Erro ao atualizar secretaria" }, { status: 500 })
    }

    return NextResponse.json(secretaria)
  } catch (error) {
    console.error("Erro ao atualizar secretaria:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // Obter usuário autenticado
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar dados da secretaria antes de excluir
    const { data: secretaria, error: fetchError } = await supabase
      .from("secretarias")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError || !secretaria) {
      return NextResponse.json({ error: "Secretaria não encontrada" }, { status: 404 })
    }

    // Verificar se a secretaria tem borderos associados
    const { data: borderos, error: borderoError } = await supabase
      .from("borderos")
      .select("id")
      .eq("secretaria_id", id)
      .limit(1)

    if (borderoError) {
      console.error("Erro ao verificar borderos:", borderoError)
      return NextResponse.json({ error: "Erro ao verificar borderos" }, { status: 500 })
    }

    if (borderos && borderos.length > 0) {
      return NextResponse.json({
        error: "Não é possível excluir esta secretaria pois ela possui borderos associados"
      }, { status: 400 })
    }

    const { error } = await supabase
      .from("secretarias")
      .delete()
      .eq("id", id)

    if (error) {
      console.error("Erro ao excluir secretaria:", error)
      return NextResponse.json({ error: "Erro ao excluir secretaria" }, { status: 500 })
    }

    // Registrar log de exclusão
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: user.id,
          acao: "excluir",
          entidade: "secretaria",
          entidade_id: id,
          detalhes: {
            nome: secretaria.nome,
            descricao: secretaria.descricao
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exclusão:", logError)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao excluir secretaria:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
