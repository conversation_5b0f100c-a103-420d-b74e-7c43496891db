"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { getSupabaseClient } from "@/lib/supabase-client"
import { useRequireNoAuth } from "@/hooks/use-auth"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("Admin@123")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const supabase = getSupabaseClient()
  const { loading: authLoading, isAuthenticated } = useRequireNoAuth()
  const { toast } = useToast()

  // Se já estiver autenticado, não mostrar a página de login
  if (authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-sky-600" />
          <p className="text-sm text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    )
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    try {
      console.log("Tentando login com:", email)

      // Tentar login normal
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error("Erro de login:", error.message)
        setError(`Erro de login: ${error.message}`)
        setIsLoading(false)
        return
      }

      console.log("Login bem-sucedido:", data)

      // Verificar se o usuário foi autenticado
      if (data.user) {
        console.log("Usuário autenticado:", data.user.id)

        // Login bem-sucedido
        toast({
          title: "Login bem-sucedido",
          description: "Redirecionando para o dashboard...",
        })

        // Aguardar um pouco antes de redirecionar para garantir que o estado seja atualizado
        setTimeout(() => {
          console.log("Redirecionando para dashboard...")
          router.push("/dashboard")
          setIsLoading(false)
        }, 1000)
      } else {
        setError("Falha na autenticação - usuário não encontrado")
        setIsLoading(false)
      }
    } catch (err) {
      console.error("Erro inesperado:", err)
      setError("Ocorreu um erro inesperado durante o login")
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-sky-100 to-white dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="w-full max-w-md">
        <Card className="border-none shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">CRM de Bordero</CardTitle>
            <CardDescription>Entre com suas credenciais para acessar o sistema</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Senha</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>

              {error && <div className="p-3 text-sm text-red-600 bg-red-50 rounded-md">{error}</div>}

              <Button type="submit" className="w-full bg-sky-600 hover:bg-sky-700" disabled={isLoading}>
                {isLoading ? "Entrando..." : "Entrar"}
              </Button>

              <div className="text-center">
                <Link
                  href="/esqueci-senha"
                  className="text-sm text-muted-foreground hover:text-primary"
                >
                  Esqueci minha senha
                </Link>
              </div>

              <div className="text-center text-sm text-muted-foreground mt-4">
                <p>Credenciais de acesso:</p>
                <p>Email: <EMAIL></p>
                <p>Senha: Admin@123</p>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
