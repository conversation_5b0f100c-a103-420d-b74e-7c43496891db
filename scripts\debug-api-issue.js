const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY

async function debugApiIssue() {
  try {
    console.log('🔍 Debugando problema da API...')

    const borderoId = '3137f0c5-e2f8-405c-a797-a8bd6be55aef'

    // Teste 1: Cliente com service key (deve funcionar)
    console.log('\n1. Testando com service key...')
    const supabaseService = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    const { data: borderoService, error: errorService } = await supabaseService
      .from('borderos')
      .select('*')
      .eq('id', borderoId)
      .maybeSingle()

    if (errorService) {
      console.error('❌ Erro com service key:', errorService.message)
    } else if (borderoService) {
      console.log('✅ Service key funcionou:', borderoService.bordero_cod)
    } else {
      console.log('⚠️ Service key não encontrou bordero')
    }

    // Teste 2: Cliente com anon key (como a API usa)
    console.log('\n2. Testando com anon key...')
    const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    const { data: borderoAnon, error: errorAnon } = await supabaseAnon
      .from('borderos')
      .select('*')
      .eq('id', borderoId)
      .maybeSingle()

    if (errorAnon) {
      console.error('❌ Erro com anon key:', errorAnon.message)
    } else if (borderoAnon) {
      console.log('✅ Anon key funcionou:', borderoAnon.bordero_cod)
    } else {
      console.log('⚠️ Anon key não encontrou bordero')
    }

    // Teste 3: Verificar RLS
    console.log('\n3. Verificando políticas RLS...')
    const { data: policies, error: policiesError } = await supabaseService
      .rpc('get_table_policies', { table_name: 'borderos' })
      .catch(() => {
        // Se a função não existir, vamos tentar uma query direta
        return supabaseService
          .from('pg_policies')
          .select('*')
          .eq('tablename', 'borderos')
      })

    if (policiesError) {
      console.log('⚠️ Não foi possível verificar políticas RLS')
    } else {
      console.log('📋 Políticas encontradas:', policies?.length || 0)
    }

    // Teste 4: Verificar se a tabela existe e tem dados
    console.log('\n4. Verificando estrutura da tabela...')
    const { data: allBorderos, error: allError } = await supabaseService
      .from('borderos')
      .select('id, bordero_cod, status')
      .limit(5)

    if (allError) {
      console.error('❌ Erro ao buscar borderos:', allError.message)
    } else {
      console.log('✅ Borderos encontrados:', allBorderos?.length || 0)
      allBorderos?.forEach(b => {
        console.log(`   - ${b.bordero_cod} (${b.id}) - ${b.status}`)
      })
    }

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

debugApiIssue()
