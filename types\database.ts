export type Json = string | number | boolean | null | { [key: string]: <PERSON><PERSON> | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      usuarios: {
        Row: {
          id: string
          email: string
          nome: string
          nivel_acesso_id: string
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          nome: string
          nivel_acesso_id: string
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          nome?: string
          nivel_acesso_id?: string
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      niveis_acesso: {
        Row: {
          id: string
          nome: string
          descricao: string | null
          permissoes: J<PERSON>
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          nome: string
          descricao?: string | null
          permissoes: <PERSON><PERSON>
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          nome?: string
          descricao?: string | null
          permissoes?: <PERSON><PERSON>
          created_at?: string | null
          updated_at?: string | null
        }
      }
      // Outras tabelas podem ser adicionadas conforme necessário
    }
  }
}
