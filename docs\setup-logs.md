# Configuração da Tabela de Logs

## Problema
A tabela `log_atividades` não existe no banco de dados, causando erros na página de logs.

## Solução
Execute o seguinte SQL no Supabase SQL Editor para criar a tabela:

```sql
-- <PERSON><PERSON>r tabel<PERSON> de logs de atividade
CREATE TABLE IF NOT EXISTS log_atividades (
    id SERIAL PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    acao VARCHAR(255) NOT NULL,
    entidade VARCHAR(255) NOT NULL,
    entidade_id VARCHAR(255),
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

-- Desabilitar RLS temporariamente para teste
ALTER TABLE log_atividades DISABLE ROW LEVEL SECURITY;
```

## Como executar

1. Acesse o Supabase Dashboard
2. Vá para o SQL Editor
3. Cole o SQL acima
4. Execute o comando
5. Recarregue a página de logs no sistema

## Status Atual
- ✅ API de logs corrigida para não retornar erro 500
- ✅ Página de logs mostra mensagem informativa quando tabela não existe
- ✅ Sistema funciona sem a tabela (retorna dados vazios)
- ⚠️ Tabela precisa ser criada manualmente no Supabase

## Funcionalidades dos Logs
Após criar a tabela, o sistema registrará automaticamente:
- Criação, edição e exclusão de borderos
- Login e logout de usuários
- Visualização de páginas
- Alterações de status
- Exportação de relatórios
- Outras atividades do sistema
