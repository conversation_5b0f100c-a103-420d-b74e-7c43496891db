import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: niveisAcesso, error } = await supabase
      .from("niveis_acesso")
      .select("*")
      .order("nome")

    if (error) {
      console.error("Erro ao buscar níveis de acesso:", error)
      return NextResponse.json({ error: "Erro ao buscar níveis de acesso" }, { status: 500 })
    }

    return NextResponse.json(niveisAcesso || [])
  } catch (error) {
    console.error("Erro ao buscar níveis de acesso:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { nome, descricao, permissoes } = body

    if (!nome) {
      return NextResponse.json({ error: "Nome é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Permissões padrão se não fornecidas
    const defaultPermissoes = {
      dashboard: true,
      borderos: false,
      secretarias: false,
      direcionamentos: false,
      tipos: false,
      usuarios: false,
      relatorios: false,
      configuracoes: false
    }

    const { data: nivel, error } = await supabase
      .from("niveis_acesso")
      .insert({
        nome,
        descricao: descricao || null,
        permissoes: permissoes || defaultPermissoes
      })
      .select()
      .single()

    if (error) {
      console.error("Erro ao criar nível de acesso:", error)
      return NextResponse.json({ error: "Erro ao criar nível de acesso" }, { status: 500 })
    }

    return NextResponse.json(nivel)
  } catch (error) {
    console.error("Erro ao criar nível de acesso:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
