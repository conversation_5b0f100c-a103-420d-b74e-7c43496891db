import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request) {
  try {
    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session: userSession }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !userSession) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const url = new URL(request.url)
    const searchParams = url.searchParams
    const status = searchParams.get("status")
    const ignoreUserLimits = searchParams.get("ignoreUserLimits") === "true"

    if (!status) {
      return NextResponse.json({ error: "Status é obrigatório" }, { status: 400 })
    }

    // Verificar se o usuário tem direcionamentos (limites de valor)
    let userValueLimits = null;
    if (userSession && !ignoreUserLimits) {
      console.log(`🔍 [DASHBOARD-BORDEROS] Verificando direcionamentos do usuário para status ${status}:`, userSession.user.id);

      const { data: userDirecionamentos, error: direcionamentosError } = await supabase
        .from("direcionamento_usuarios")
        .select(`
          direcionamento:direcionamentos(
            id,
            nome,
            valor_minimo,
            valor_maximo
          )
        `)
        .eq("usuario_id", userSession.user.id);

      if (direcionamentosError) {
        console.error("❌ [DASHBOARD-BORDEROS] Erro ao buscar direcionamentos do usuário:", direcionamentosError);
      } else {
        console.log("📋 [DASHBOARD-BORDEROS] Direcionamentos encontrados:", userDirecionamentos);

        if (userDirecionamentos && userDirecionamentos.length > 0) {
          // Encontrar o valor máximo que o usuário pode ver
          const valoresMaximos = userDirecionamentos
            .filter(d => d.direcionamento && d.direcionamento.valor_maximo)
            .map(d => (d.direcionamento as any).valor_maximo);

          console.log("📋 [DASHBOARD-BORDEROS] Valores máximos encontrados:", valoresMaximos);

          if (valoresMaximos.length > 0) {
            const maxValue = Math.max(...valoresMaximos);

            if (maxValue > 0) {
              userValueLimits = maxValue;
              console.log(`💰 [DASHBOARD-BORDEROS] Usuário ${userSession.user.id} tem limite de valor: R$ ${userValueLimits}`);
            } else {
              console.log("⚠️ [DASHBOARD-BORDEROS] Valor máximo é 0 ou inválido");
            }
          } else {
            console.log("⚠️ [DASHBOARD-BORDEROS] Nenhum valor máximo válido encontrado nos direcionamentos");
          }
        } else {
          console.log("📝 [DASHBOARD-BORDEROS] Usuário não tem direcionamentos configurados - pode ver todos os valores");
        }
      }
    } else {
      console.log("🚫 [DASHBOARD-BORDEROS] Verificação de direcionamentos ignorada ou usuário não autenticado");
    }

    // Construir query
    let query = supabase.from("borderos").select(`
        *,
        secretaria:secretarias(*),
        tipo:tipos(*),
        responsavel:usuarios(*),
        devolucoes:bordero_devolucoes(
          usuario:usuarios(*)
        )
      `)
      .eq("status", status);

    // Aplicar limite de valor baseado nos direcionamentos do usuário
    if (userValueLimits) {
      console.log(`🔒 [DASHBOARD-BORDEROS] Aplicando filtro de valor máximo para ${status}: R$ ${userValueLimits}`);
      query = query.lte("valor", userValueLimits);
    } else {
      console.log(`🔓 [DASHBOARD-BORDEROS] Nenhum limite de valor aplicado para ${status}`);
    }

    // Ordenar por data de criação (mais recentes primeiro)
    query = query.order("created_at", { ascending: false });

    console.log(`📊 [DASHBOARD-BORDEROS] Executando query para status ${status}`);
    const { data: borderos, error } = await query;

    if (error) {
      console.error(`❌ [DASHBOARD-BORDEROS] Erro ao buscar borderos ${status}:`, error);
      return NextResponse.json({ error: error.message, details: error }, { status: 500 });
    }

    console.log(`📊 [DASHBOARD-BORDEROS] Encontrados ${borderos?.length || 0} borderos com status ${status}`);

    // Log detalhado para debug
    if (borderos && borderos.length > 0) {
      const valores = borderos.map((b: any) => b.valor);
      const valorMaximo = Math.max(...valores);
      const valorMinimo = Math.min(...valores);
      console.log(`💰 [DASHBOARD-BORDEROS] Valor máximo encontrado: R$ ${valorMaximo}`);
      console.log(`💰 [DASHBOARD-BORDEROS] Valor mínimo encontrado: R$ ${valorMinimo}`);

      if (userValueLimits) {
        const acimaDolimite = borderos.filter((b: any) => b.valor > userValueLimits);
        if (acimaDolimite.length > 0) {
          console.log(`⚠️ [DASHBOARD-BORDEROS] PROBLEMA: ${acimaDolimite.length} borderos acima do limite de R$ ${userValueLimits}:`);
          acimaDolimite.forEach((b: any) => {
            console.log(`  - ${b.bordero_cod}: R$ ${b.valor}`);
          });
        } else {
          console.log(`✅ [DASHBOARD-BORDEROS] Todos os borderos estão dentro do limite de R$ ${userValueLimits}`);
        }
      }
    }

    return NextResponse.json(Array.isArray(borderos) ? borderos : []);
  } catch (error) {
    console.error("💥 [DASHBOARD-BORDEROS] Erro ao buscar borderos:", error);
    return NextResponse.json({ 
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
