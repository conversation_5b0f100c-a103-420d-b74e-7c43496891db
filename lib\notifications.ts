import { toast } from "@/hooks/use-toast"

export interface NotificationOptions {
  title: string
  description: string
  variant?: "default" | "destructive"
  duration?: number
}

export class NotificationService {
  success(title: string, description: string, duration?: number) {
    toast({
      title,
      description,
      variant: "default",
      duration: duration || 5000
    })
  }

  error(title: string, description: string, duration?: number) {
    toast({
      title,
      description,
      variant: "destructive", 
      duration: duration || 7000
    })
  }

  info(title: string, description: string, duration?: number) {
    toast({
      title,
      description,
      variant: "default",
      duration: duration || 5000
    })
  }

  warning(title: string, description: string, duration?: number) {
    toast({
      title,
      description,
      variant: "destructive",
      duration: duration || 6000
    })
  }

  custom(options: NotificationOptions) {
    toast(options)
  }
}

// Instância singleton
export const notifications = new NotificationService()

// Hook para usar notificações
export function useNotifications() {
  return notifications
}
