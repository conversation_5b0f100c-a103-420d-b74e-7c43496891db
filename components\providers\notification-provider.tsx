"use client"

import { createContext, useContext, useState, ReactNode, useCallback } from "react"
import { NotificationToast, NotificationToastComponent } from "@/components/ui/notification-toast"

interface NotificationContextType {
  addNotification: (notification: Omit<NotificationToast, "id">) => void
  removeNotification: (id: string) => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function useNotification() {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error("useNotification must be used within a NotificationProvider")
  }
  return context
}

interface NotificationProviderProps {
  children: ReactNode
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<NotificationToast[]>([])

  const addNotification = useCallback((notification: Omit<NotificationToast, "id">) => {
    const id = `notification-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    const newNotification: NotificationToast = {
      ...notification,
      id,
      duration: notification.duration ?? 5000, // 5 segundos por padrão
    }

    setNotifications(prev => [...prev, newNotification])
  }, [])

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }, [])

  return (
    <NotificationContext.Provider value={{ addNotification, removeNotification }}>
      {children}

      {/* Container de notificações */}
      <div className="pointer-events-none fixed inset-0 z-50 flex items-end px-4 py-6 sm:items-start sm:p-6">
        <div className="flex w-full flex-col items-center space-y-4 sm:items-end">
          {notifications.map(notification => (
            <NotificationToastComponent
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
            />
          ))}
        </div>
      </div>
    </NotificationContext.Provider>
  )
}

// Hook para facilitar o uso
export function useNotify() {
  const { addNotification } = useNotification()

  return {
    success: (title: string, description?: string) =>
      addNotification({ title, description, type: "success" }),
    error: (title: string, description?: string) =>
      addNotification({ title, description, type: "error" }),
    warning: (title: string, description?: string) =>
      addNotification({ title, description, type: "warning" }),
    info: (title: string, description?: string) =>
      addNotification({ title, description, type: "info" }),
    custom: (notification: Omit<NotificationToast, "id">) =>
      addNotification(notification),
  }
}
