const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function testBorderoUpdate() {
  try {
    console.log('🔍 Testando atualização de status do bordero...')

    const borderoId = '3137f0c5-e2f8-405c-a797-a8bd6be55aef'

    // Primeiro, verificar se o bordero existe
    console.log('\n1. Verificando se o bordero existe...')
    const { data: borderoExistente, error: fetchError } = await supabase
      .from('borderos')
      .select('*')
      .eq('id', borderoId)
      .maybeSingle()

    if (fetchError) {
      console.error('❌ Erro ao buscar bordero:', fetchError.message)
      return
    }

    if (!borderoExistente) {
      console.error('❌ Bordero não encontrado com ID:', borderoId)
      return
    }

    console.log('✅ Bordero encontrado:')
    console.log(`   - ID: ${borderoExistente.id}`)
    console.log(`   - Código: ${borderoExistente.bordero_cod}`)
    console.log(`   - Status atual: ${borderoExistente.status}`)

    // Tentar atualizar o status
    console.log('\n2. Tentando atualizar o status...')
    const novoStatus = borderoExistente.status === 'novo' ? 'analise' : 'novo'
    
    const { data: borderoAtualizado, error: updateError } = await supabase
      .from('borderos')
      .update({
        status: novoStatus,
        dados_status: null,
        responsavel_id: null
      })
      .eq('id', borderoId)
      .select()
      .maybeSingle()

    if (updateError) {
      console.error('❌ Erro ao atualizar bordero:', updateError.message)
      console.error('Detalhes:', updateError)
      return
    }

    if (!borderoAtualizado) {
      console.error('❌ Bordero não foi atualizado (nenhum resultado retornado)')
      return
    }

    console.log('✅ Bordero atualizado com sucesso:')
    console.log(`   - Status anterior: ${borderoExistente.status}`)
    console.log(`   - Status novo: ${borderoAtualizado.status}`)

    // Verificar se a atualização foi persistida
    console.log('\n3. Verificando se a atualização foi persistida...')
    const { data: borderoVerificacao, error: verifyError } = await supabase
      .from('borderos')
      .select('*')
      .eq('id', borderoId)
      .single()

    if (verifyError) {
      console.error('❌ Erro ao verificar bordero:', verifyError.message)
      return
    }

    console.log('✅ Verificação concluída:')
    console.log(`   - Status final: ${borderoVerificacao.status}`)

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

testBorderoUpdate()
