"use client"

import { useEffect, useState } from "react"

interface HydrationBoundaryProps {
  children: React.ReactNode
}

export function HydrationBoundary({ children }: HydrationBoundaryProps) {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Aguardar um tick para garantir que a hidratação foi concluída
    const timer = setTimeout(() => {
      setIsHydrated(true)
    }, 0)

    return () => clearTimeout(timer)
  }, [])

  if (!isHydrated) {
    // Renderizar uma versão simplificada durante a hidratação
    return <div suppressHydrationWarning>{children}</div>
  }

  return <>{children}</>
}
