"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { EnhancedPasswordInput } from "@/components/ui/enhanced-password-input"
import { toast } from "@/hooks/use-toast"
import { Loader2, Plus, Save, Trash2 } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { PermissionGuard } from "@/components/auth/permission-guard"

interface NivelAcesso {
  id: string
  nome: string
  descricao: string
  permissoes: {
    dashboard: boolean
    borderos: boolean
    secretarias: boolean
    direcionamentos: boolean
    tipos: boolean
    usuarios: boolean
    relatorios: boolean
    configuracoes: boolean
  }
  created_at: string
  updated_at: string
}

export default function ConfiguracoesPage() {
  const [loading, setLoading] = useState(true)
  const [niveisAcesso, setNiveisAcesso] = useState<NivelAcesso[]>([])
  const [dialogOpen, setDialogOpen] = useState(false)
  const [sistemaConfig, setSistemaConfig] = useState({
    nome: "CRM de Bordero",
    modoEscuro: false,
    notificacoesEmail: true,
  })
  const [perfilData, setPerfilData] = useState({
    nome: "",
    email: "",
    avatar: "",
    senhaAtual: "",
    novaSenha: "",
    confirmarSenha: "",
  })
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [currentNivel, setCurrentNivel] = useState<NivelAcesso>({
    id: "",
    nome: "",
    descricao: "",
    permissoes: {
      dashboard: false,
      borderos: false,
      secretarias: false,
      direcionamentos: false,
      tipos: false,
      usuarios: false,
      relatorios: false,
      configuracoes: false,
    },
    created_at: "",
    updated_at: "",
  })

  useEffect(() => {
    fetchNiveisAcesso()
    fetchPerfilData()
  }, [])

  const fetchPerfilData = async () => {
    try {
      const response = await fetch("/api/auth/profile")
      if (!response.ok) throw new Error("Erro ao buscar dados do perfil")

      const data = await response.json()
      setPerfilData({
        nome: data.nome || "",
        email: data.email || "",
        avatar: data.avatar || "",
        senhaAtual: "",
        novaSenha: "",
        confirmarSenha: "",
      })
    } catch (error) {
      console.error("Erro ao buscar dados do perfil:", error)
    }
  }

  const fetchNiveisAcesso = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/niveis-acesso")
      if (!response.ok) throw new Error("Erro ao buscar níveis de acesso")

      const data = await response.json()
      setNiveisAcesso(data || [])
    } catch (error) {
      console.error("Erro ao buscar níveis de acesso:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os níveis de acesso.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveNivel = async () => {
    try {
      setLoading(true)

      if (!currentNivel.nome) {
        toast({
          title: "Erro",
          description: "O nome do nível de acesso é obrigatório.",
          variant: "destructive",
        })
        return
      }

      const url = currentNivel.id ? `/api/niveis-acesso/${currentNivel.id}` : "/api/niveis-acesso"
      const method = currentNivel.id ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          nome: currentNivel.nome,
          descricao: currentNivel.descricao,
          permissoes: currentNivel.permissoes,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao salvar nível de acesso")
      }

      toast({
        title: "Sucesso",
        description: currentNivel.id ? "Nível de acesso atualizado com sucesso." : "Nível de acesso criado com sucesso.",
      })

      setDialogOpen(false)
      fetchNiveisAcesso()
    } catch (error: any) {
      console.error("Erro ao salvar nível de acesso:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível salvar o nível de acesso.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteNivel = async (id: string) => {
    try {
      if (!confirm("Tem certeza que deseja excluir este nível de acesso?")) {
        return
      }

      setLoading(true)
      const response = await fetch(`/api/niveis-acesso/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao excluir nível de acesso")
      }

      toast({
        title: "Sucesso",
        description: "Nível de acesso excluído com sucesso.",
      })

      fetchNiveisAcesso()
    } catch (error: any) {
      console.error("Erro ao excluir nível de acesso:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir o nível de acesso.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEditNivel = (nivel: NivelAcesso) => {
    setCurrentNivel(nivel)
    setDialogOpen(true)
  }

  const handleNewNivel = () => {
    setCurrentNivel({
      id: "",
      nome: "",
      descricao: "",
      permissoes: {
        dashboard: false,
        borderos: false,
        secretarias: false,
        direcionamentos: false,
        tipos: false,
        usuarios: false,
        relatorios: false,
        configuracoes: false,
      },
      created_at: "",
      updated_at: "",
    })
    setDialogOpen(true)
  }

  const handleSaveSistemaConfig = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/configuracoes/sistema", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(sistemaConfig),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao salvar configurações")
      }

      toast({
        title: "Sucesso",
        description: "Configurações do sistema salvas com sucesso.",
      })
    } catch (error: any) {
      console.error("Erro ao salvar configurações:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível salvar as configurações.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSavePerfil = async () => {
    try {
      if (perfilData.novaSenha && perfilData.novaSenha !== perfilData.confirmarSenha) {
        toast({
          title: "Erro",
          description: "As senhas não coincidem.",
          variant: "destructive",
        })
        return
      }

      setLoading(true)

      // Upload do avatar se houver
      let avatarUrl = perfilData.avatar
      if (avatarFile) {
        const formData = new FormData()
        formData.append("avatar", avatarFile)

        const uploadResponse = await fetch("/api/upload/avatar", {
          method: "POST",
          body: formData,
        })

        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json()
          avatarUrl = uploadData.url
        }
      }

      const response = await fetch("/api/auth/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          nome: perfilData.nome,
          avatar: avatarUrl,
          senhaAtual: perfilData.senhaAtual,
          novaSenha: perfilData.novaSenha,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao atualizar perfil")
      }

      toast({
        title: "Sucesso",
        description: "Perfil atualizado com sucesso.",
      })

      // Limpar campos de senha
      setPerfilData(prev => ({
        ...prev,
        senhaAtual: "",
        novaSenha: "",
        confirmarSenha: "",
      }))
      setAvatarFile(null)
    } catch (error: any) {
      console.error("Erro ao atualizar perfil:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível atualizar o perfil.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Configurações</h1>
      </div>

      <Tabs defaultValue="niveis-acesso" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="niveis-acesso">Níveis de Acesso</TabsTrigger>
          <TabsTrigger value="sistema">Sistema</TabsTrigger>
          <TabsTrigger value="perfil">Meu Perfil</TabsTrigger>
        </TabsList>

        <TabsContent value="niveis-acesso" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Níveis de Acesso</CardTitle>
                <CardDescription>Gerencie os níveis de acesso e permissões do sistema</CardDescription>
              </div>
              <Button onClick={handleNewNivel}>
                <Plus className="mr-2 h-4 w-4" />
                Novo Nível
              </Button>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Data de Criação</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {niveisAcesso.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center">
                          Nenhum nível de acesso encontrado.
                        </TableCell>
                      </TableRow>
                    ) : (
                      niveisAcesso.map((nivel) => (
                        <TableRow key={nivel.id}>
                          <TableCell className="font-medium">{nivel.nome}</TableCell>
                          <TableCell>{nivel.descricao || "Sem descrição"}</TableCell>
                          <TableCell>{new Date(nivel.created_at).toLocaleDateString("pt-BR")}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="ghost" size="sm" onClick={() => handleEditNivel(nivel)}>
                                Editar
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:text-red-700"
                                onClick={() => handleDeleteNivel(nivel.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sistema" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configurações do Sistema</CardTitle>
              <CardDescription>Gerencie as configurações gerais do sistema</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sistema-nome">Nome do Sistema</Label>
                <Input
                  id="sistema-nome"
                  value={sistemaConfig.nome}
                  onChange={(e) => setSistemaConfig(prev => ({ ...prev, nome: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sistema-logo">Logo do Sistema</Label>
                <Input id="sistema-logo" type="file" />
              </div>
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="modo-escuro">Modo Escuro Padrão</Label>
                <Switch
                  id="modo-escuro"
                  checked={sistemaConfig.modoEscuro}
                  onCheckedChange={(checked) => setSistemaConfig(prev => ({ ...prev, modoEscuro: checked }))}
                />
              </div>
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="notificacoes-email">Notificações por Email</Label>
                <Switch
                  id="notificacoes-email"
                  checked={sistemaConfig.notificacoesEmail}
                  onCheckedChange={(checked) => setSistemaConfig(prev => ({ ...prev, notificacoesEmail: checked }))}
                />
              </div>
              <Button className="mt-4" onClick={handleSaveSistemaConfig} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Save className="mr-2 h-4 w-4" />
                Salvar Configurações
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="perfil" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Meu Perfil</CardTitle>
              <CardDescription>Atualize suas informações pessoais</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="perfil-avatar">Foto do Perfil</Label>
                <div className="flex items-center gap-4">
                  {perfilData.avatar && (
                    <img
                      src={perfilData.avatar}
                      alt="Avatar atual"
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  )}
                  <Input
                    id="perfil-avatar"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        setAvatarFile(file)
                        // Preview da imagem
                        const reader = new FileReader()
                        reader.onload = (e) => {
                          setPerfilData(prev => ({ ...prev, avatar: e.target?.result as string }))
                        }
                        reader.readAsDataURL(file)
                      }
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-nome">Nome</Label>
                <Input
                  id="perfil-nome"
                  value={perfilData.nome}
                  onChange={(e) => setPerfilData(prev => ({ ...prev, nome: e.target.value }))}
                  placeholder="Digite seu nome"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-email">Email</Label>
                <Input
                  id="perfil-email"
                  value={perfilData.email}
                  placeholder="Digite seu email"
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-senha-atual">Senha Atual</Label>
                <EnhancedPasswordInput
                  id="perfil-senha-atual"
                  value={perfilData.senhaAtual}
                  onChange={(e) => setPerfilData(prev => ({ ...prev, senhaAtual: e.target.value }))}
                  placeholder="Digite sua senha atual para alterar"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-nova-senha">Nova Senha</Label>
                <EnhancedPasswordInput
                  id="perfil-nova-senha"
                  value={perfilData.novaSenha}
                  onChange={(e) => setPerfilData(prev => ({ ...prev, novaSenha: e.target.value }))}
                  placeholder="Digite a nova senha"
                  showStrength={true}
                  showCriteria={true}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="perfil-confirmar-senha">Confirmar Nova Senha</Label>
                <EnhancedPasswordInput
                  id="perfil-confirmar-senha"
                  value={perfilData.confirmarSenha}
                  onChange={(e) => setPerfilData(prev => ({ ...prev, confirmarSenha: e.target.value }))}
                  placeholder="Confirme a nova senha"
                  confirmPassword={perfilData.novaSenha}
                />
              </div>
              <Button className="mt-4" onClick={handleSavePerfil} disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Save className="mr-2 h-4 w-4" />
                Atualizar Perfil
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{currentNivel.id ? "Editar Nível de Acesso" : "Novo Nível de Acesso"}</DialogTitle>
            <DialogDescription>
              {currentNivel.id
                ? "Atualize as informações do nível de acesso"
                : "Preencha as informações para criar um novo nível de acesso"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="nivel-nome">Nome</Label>
              <Input
                id="nivel-nome"
                value={currentNivel.nome}
                onChange={(e) => setCurrentNivel({ ...currentNivel, nome: e.target.value })}
                placeholder="Ex: Administrador, Operador, Visualizador"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nivel-descricao">Descrição</Label>
              <Input
                id="nivel-descricao"
                value={currentNivel.descricao}
                onChange={(e) => setCurrentNivel({ ...currentNivel, descricao: e.target.value })}
                placeholder="Descreva o nível de acesso"
              />
            </div>
            <div className="space-y-3">
              <Label>Permissões</Label>
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(currentNivel.permissoes).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <Switch
                      id={`perm-${key}`}
                      checked={value}
                      onCheckedChange={(checked) =>
                        setCurrentNivel({
                          ...currentNivel,
                          permissoes: {
                            ...currentNivel.permissoes,
                            [key]: checked
                          }
                        })
                      }
                    />
                    <Label htmlFor={`perm-${key}`} className="text-sm capitalize">
                      {key === 'dashboard' ? 'Dashboard' :
                       key === 'borderos' ? 'Borderos' :
                       key === 'secretarias' ? 'Secretarias' :
                       key === 'direcionamentos' ? 'Direcionamentos' :
                       key === 'tipos' ? 'Tipos' :
                       key === 'usuarios' ? 'Usuários' :
                       key === 'relatorios' ? 'Relatórios' :
                       key === 'configuracoes' ? 'Configurações' : key}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Selecione as páginas que este nível de acesso pode visualizar e gerenciar.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSaveNivel} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {currentNivel.id ? "Atualizar" : "Criar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
