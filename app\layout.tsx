import type React from "react"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { NotificationProvider } from "@/components/providers/notification-provider"
import Supabase<PERSON>rovider from "@/lib/supabase-provider"
import { HydrationBoundary } from "@/components/hydration-boundary"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "CRM de Bordero",
  description: "Sistema de gerenciamento de borderos",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevenir problemas de hidratação causados por extensões do navegador
              if (typeof window !== 'undefined') {
                window.__PREVENT_HYDRATION_MISMATCH__ = true;
              }
            `,
          }}
        />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <HydrationBoundary>
          <SupabaseProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <NotificationProvider>
                {children}
                <Toaster />
              </NotificationProvider>
            </ThemeProvider>
          </SupabaseProvider>
        </HydrationBoundary>
      </body>
    </html>
  )
}
