"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, CheckSquare, FileCheck, BanknoteIcon } from "lucide-react"

interface DashboardData {
  novos: number
  analise: number
  assinados: number
  pagos: number
  valorTotal: number
}

export function DashboardStats() {
  const [stats, setStats] = useState<DashboardData>({
    novos: 0,
    analise: 0,
    assinados: 0,
    pagos: 0,
    valorTotal: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)
      console.log("Iniciando busca de estatísticas do dashboard");

      // Tentar usar a nova API de estatísticas primeiro
      try {
        const statsResponse = await fetch("/api/dashboard/stats");
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          console.log("Estatísticas carregadas da nova API:", statsData);
          setStats(statsData);
          return;
        } else {
          console.warn("Nova API de estatísticas falhou, usando método antigo");
        }
      } catch (statsError) {
        console.warn("Erro na nova API de estatísticas, usando método antigo:", statsError);
      }

      // Fallback: Buscar dados para cada status individualmente
      const [novosRes, analiseRes, assinadosRes, pagosRes] = await Promise.all([
        fetch("/api/borderos?status=novo"),
        fetch("/api/borderos?status=analise"),
        fetch("/api/borderos?status=assinado"),
        fetch("/api/borderos?status=pago")
      ])

      // Log das respostas para debug
      console.log('Resposta novos status:', novosRes.status);
      console.log('Resposta analise status:', analiseRes.status);
      console.log('Resposta assinados status:', assinadosRes.status);
      console.log('Resposta pagos status:', pagosRes.status);

      function safeArray(data: any): any[] {
        return Array.isArray(data) ? data : [];
      }

      let novosData, analiseData, assinadosData, pagosData;
      
      try {
        novosData = await novosRes.json().then(safeArray);
        console.log("Dados novos carregados:", novosData.length);
      } catch (e) {
        console.error("Erro ao processar dados novos:", e);
        novosData = [];
      }
      
      try {
        analiseData = await analiseRes.json().then(safeArray);
        console.log("Dados análise carregados:", analiseData.length);
      } catch (e) {
        console.error("Erro ao processar dados análise:", e);
        analiseData = [];
      }
      
      try {
        assinadosData = await assinadosRes.json().then(safeArray);
        console.log("Dados assinados carregados:", assinadosData.length);
      } catch (e) {
        console.error("Erro ao processar dados assinados:", e);
        assinadosData = [];
      }
      
      try {
        pagosData = await pagosRes.json().then(safeArray);
        console.log("Dados pagos carregados:", pagosData.length);
      } catch (e) {
        console.error("Erro ao processar dados pagos:", e);
        pagosData = [];
      }

      // Verificar se há dados
      console.log("Dados carregados para estatísticas:");
      console.log("- Novos:", novosData.length);
      console.log("- Análise:", analiseData.length);
      console.log("- Assinados:", assinadosData.length);
      console.log("- Pagos:", pagosData.length);
      
      // Se não há dados, tentar buscar diretamente da API de relatórios
      if (novosData.length === 0 && analiseData.length === 0 &&
          assinadosData.length === 0 && pagosData.length === 0) {
        console.log("Tentando buscar dados da API de relatórios como fallback");
        
        try {
          const relatoriosRes = await fetch("/api/relatorios");
          if (relatoriosRes.ok) {
            const relatoriosData = await relatoriosRes.json();
            console.log("Dados de relatórios recebidos:", relatoriosData);
            
            if (relatoriosData.borderos && Array.isArray(relatoriosData.borderos)) {
              // Filtrar por status
              const novosFallback = relatoriosData.borderos.filter((b: any) => b.status === 'novo');
              const analiseFallback = relatoriosData.borderos.filter((b: any) => b.status === 'analise');
              const assinadosFallback = relatoriosData.borderos.filter((b: any) => b.status === 'assinado');
              const pagosFallback = relatoriosData.borderos.filter((b: any) => b.status === 'pago');
              
              console.log("Dados de fallback filtrados:");
              console.log("- Novos:", novosFallback.length);
              console.log("- Análise:", analiseFallback.length);
              console.log("- Assinados:", assinadosFallback.length);
              console.log("- Pagos:", pagosFallback.length);
              
              // Calcular estatísticas com dados de fallback
              const valorTotal = relatoriosData.borderos.reduce((total: number, bordero: any) =>
                total + (bordero.valor || 0), 0);
              
              setStats({
                novos: novosFallback.length,
                analise: analiseFallback.length,
                assinados: assinadosFallback.length,
                pagos: pagosFallback.length,
                valorTotal
              });
              
              return; // Sair da função após usar o fallback
            }
          }
        } catch (fallbackError) {
          console.error("Erro ao buscar dados de fallback:", fallbackError);
        }
      }
      
      // Calcular estatísticas com os dados originais
      const valorTotal = [
        ...novosData,
        ...analiseData,
        ...assinadosData,
        ...pagosData
      ].reduce((total, bordero) => total + (bordero.valor || 0), 0);
      
      setStats({
        novos: novosData.length,
        analise: analiseData.length,
        assinados: assinadosData.length,
        pagos: pagosData.length,
        valorTotal
      });
    } catch (error) {
      console.error("Erro ao buscar estatísticas:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value)
  }

  if (loading) {
    return (
      <div className="grid gap-3 grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs md:text-sm font-medium">Carregando...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg md:text-2xl font-bold">--</div>
              <p className="text-xs text-muted-foreground hidden md:block">Carregando dados...</p>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Novos Borderos</CardTitle>
          <FileText className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.novos}</div>
          <p className="text-xs text-muted-foreground">Aguardando análise</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Em Análise</CardTitle>
          <CheckSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.analise}</div>
          <p className="text-xs text-muted-foreground">Em processo de análise</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Assinados</CardTitle>
          <FileCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.assinados}</div>
          <p className="text-xs text-muted-foreground">Prontos para pagamento</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
          <BanknoteIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats.valorTotal)}</div>
          <p className="text-xs text-muted-foreground">Todos os borderos</p>
        </CardContent>
      </Card>
    </div>
  )
}
