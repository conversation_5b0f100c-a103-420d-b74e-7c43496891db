-- OPTIMIZE_DATABASE_PERFORMANCE.sql
-- Script to optimize database performance and fix slow loading issues

-- 1. Add indexes to improve query performance
-- Index for borderos by status (frequently queried)
CREATE INDEX IF NOT EXISTS idx_borderos_status ON borderos (status);

-- Index for borderos by secretaria_id (frequently filtered)
CREATE INDEX IF NOT EXISTS idx_borderos_secretaria_id ON borderos (secretaria_id);

-- Index for borderos by tipo_id (frequently filtered)
CREATE INDEX IF NOT EXISTS idx_borderos_tipo_id ON borderos (tipo_id);

-- Index for borderos by valor (for range queries)
CREATE INDEX IF NOT EXISTS idx_borderos_valor ON borderos (valor);

-- Index for borderos by data (for date range queries)
CREATE INDEX IF NOT EXISTS idx_borderos_data ON borderos (data);

-- 2. Create materialized views for dashboard statistics
-- This will significantly speed up dashboard loading

-- Create materialized view for bordero counts by status
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_bordero_counts_by_status AS
SELECT
  status,
  COUNT(*) as count
FROM borderos
GROUP BY status;

-- Criar um índice único na view materializada (necessário para refresh concurrent)
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_bordero_counts_status_unique ON mv_bordero_counts_by_status (status);

-- Criar índice normal para consultas
CREATE INDEX IF NOT EXISTS idx_mv_bordero_counts_status ON mv_bordero_counts_by_status (count);

-- Create materialized view for secretaria values
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_secretaria_values AS
SELECT
  s.id,
  s.nome,
  COALESCE(SUM(b.valor), 0) as total_value,
  COUNT(b.id) as bordero_count
FROM secretarias s
LEFT JOIN borderos b ON s.id = b.secretaria_id
GROUP BY s.id, s.nome;

-- Criar um índice único na view materializada (necessário para refresh concurrent)
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_secretaria_values_id_unique ON mv_secretaria_values (id);

-- Criar índice normal para consultas
CREATE INDEX IF NOT EXISTS idx_mv_secretaria_values_total ON mv_secretaria_values (total_value);

-- 3. Create function and trigger to refresh materialized views
-- Função para atualizar as views materializadas
-- Em vez de atualizar diretamente, insere um registro em uma tabela de notificação
-- que será processada por um job separado com permissões adequadas
CREATE TABLE IF NOT EXISTS dashboard_refresh_queue (
  id SERIAL PRIMARY KEY,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMP WITH TIME ZONE
);

CREATE OR REPLACE FUNCTION refresh_dashboard_views()
RETURNS TRIGGER AS $$
BEGIN
  -- Em vez de tentar atualizar as views diretamente (o que requer permissões de proprietário),
  -- apenas insere uma solicitação na fila de atualização
  INSERT INTO dashboard_refresh_queue (requested_at) VALUES (CURRENT_TIMESTAMP);
  
  -- Nota: Um job separado com permissões adequadas deve processar esta fila
  -- e atualizar as views materializadas
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Conceder permissões na tabela de fila para todos os usuários que podem inserir borderos
GRANT INSERT, SELECT ON dashboard_refresh_queue TO PUBLIC;

-- Criar uma função de administrador para processar a fila (deve ser executada por um usuário com permissões adequadas)
-- Esta função pode ser chamada por um job agendado
CREATE OR REPLACE FUNCTION process_dashboard_refresh_queue()
RETURNS INTEGER AS $$
DECLARE
  processed_count INTEGER := 0;
BEGIN
  -- Marcar todas as solicitações não processadas como processadas
  UPDATE dashboard_refresh_queue
  SET processed = TRUE, processed_at = CURRENT_TIMESTAMP
  WHERE processed = FALSE;
  
  -- Obter a contagem de solicitações processadas
  GET DIAGNOSTICS processed_count = ROW_COUNT;
  
  -- Se houver solicitações para processar, atualizar as views
  IF processed_count > 0 THEN
    BEGIN
      -- Tentar atualizar concorrentemente
      REFRESH MATERIALIZED VIEW CONCURRENTLY mv_bordero_counts_by_status;
      REFRESH MATERIALIZED VIEW CONCURRENTLY mv_secretaria_values;
    EXCEPTION WHEN OTHERS THEN
      -- Se falhar, usar o método normal (não concorrente)
      RAISE NOTICE 'Falha ao atualizar views concorrentemente, usando método normal';
      REFRESH MATERIALIZED VIEW mv_bordero_counts_by_status;
      REFRESH MATERIALIZED VIEW mv_secretaria_values;
    END;
  END IF;
  
  RETURN processed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Conceder permissão para executar a função de processamento apenas para usuários administradores
-- GRANT EXECUTE ON FUNCTION process_dashboard_refresh_queue() TO admin_role;

-- Criar um índice na coluna processed para consultas eficientes
CREATE INDEX IF NOT EXISTS idx_dashboard_refresh_queue_processed ON dashboard_refresh_queue (processed);

-- Create trigger to refresh views when borderos are modified
DROP TRIGGER IF EXISTS trg_refresh_dashboard_views ON borderos;
CREATE TRIGGER trg_refresh_dashboard_views
AFTER INSERT OR UPDATE OR DELETE ON borderos
FOR EACH STATEMENT
EXECUTE FUNCTION refresh_dashboard_views();

-- 4. Create function to update secretaria valores_total
CREATE OR REPLACE FUNCTION update_secretaria_valores_total()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment the secretaria's valores_total
    UPDATE secretarias
    SET valores_total = valores_total + NEW.valor
    WHERE id = NEW.secretaria_id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Update the secretaria's valores_total if secretaria_id or valor changed
    IF OLD.secretaria_id <> NEW.secretaria_id THEN
      -- Decrement old secretaria's valores_total
      UPDATE secretarias
      SET valores_total = valores_total - OLD.valor
      WHERE id = OLD.secretaria_id;
      
      -- Increment new secretaria's valores_total
      UPDATE secretarias
      SET valores_total = valores_total + NEW.valor
      WHERE id = NEW.secretaria_id;
    ELSIF OLD.valor <> NEW.valor THEN
      -- Update the secretaria's valores_total with the difference
      UPDATE secretarias
      SET valores_total = valores_total - OLD.valor + NEW.valor
      WHERE id = NEW.secretaria_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement the secretaria's valores_total
    UPDATE secretarias
    SET valores_total = valores_total - OLD.valor
    WHERE id = OLD.secretaria_id;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update secretaria valores_total when borderos are modified
DROP TRIGGER IF EXISTS trg_update_secretaria_valores_total ON borderos;
CREATE TRIGGER trg_update_secretaria_valores_total
AFTER INSERT OR UPDATE OR DELETE ON borderos
FOR EACH ROW
EXECUTE FUNCTION update_secretaria_valores_total();

-- 5. Create a cache table for user permissions
CREATE TABLE IF NOT EXISTS user_permissions_cache (
  user_id UUID PRIMARY KEY,
  permissions JSONB,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS idx_user_permissions_cache_user_id ON user_permissions_cache (user_id);

-- Create function to refresh user permissions cache
CREATE OR REPLACE FUNCTION refresh_user_permissions_cache(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Delete existing cache entry
  DELETE FROM user_permissions_cache WHERE user_id = p_user_id;
  
  -- Insert new cache entry with permissions
  INSERT INTO user_permissions_cache (user_id, permissions, last_updated)
  SELECT 
    p_user_id,
    jsonb_build_object(
      'direcionamentos', (
        SELECT jsonb_agg(d.*)
        FROM direcionamentos d
        JOIN direcionamento_usuarios du ON d.id = du.direcionamento_id
        WHERE du.usuario_id = p_user_id
      ),
      'roles', (
        SELECT jsonb_agg(r.*)
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = p_user_id
      )
    ),
    CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 6. Analyze tables to update statistics
-- Nota: Comandos VACUUM foram removidos pois não podem ser executados dentro de blocos de transação
-- Execute estes comandos separadamente no console SQL se necessário
ANALYZE borderos;
ANALYZE secretarias;
ANALYZE direcionamentos;
ANALYZE direcionamento_usuarios;
ANALYZE usuarios;

-- Comentário: Para executar VACUUM manualmente, use os seguintes comandos no console SQL:
-- VACUUM ANALYZE borderos;
-- VACUUM ANALYZE secretarias;
-- VACUUM ANALYZE direcionamentos;
-- VACUUM ANALYZE direcionamento_usuarios;
-- VACUUM ANALYZE usuarios;