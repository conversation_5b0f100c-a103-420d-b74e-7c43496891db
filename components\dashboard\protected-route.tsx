"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { type NivelAcesso, type Permisso<PERSON> } from "@/lib/permissions"
import { useAuth } from "@/hooks/use-auth"
import { useCachedPermissions } from "@/hooks/use-cached-permissions"
import { Loader2, AlertCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: {
    modulo: keyof Permissoes
    acao: string
  }
  fallback?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requiredPermission,
  fallback
}: ProtectedRouteProps) {
  const router = useRouter()
  const { loading: authLoading, isAuthenticated } = useAuth()
  const {
    loading: permissionsLoading,
    hasPermission,
    canAccessRoute
  } = useCachedPermissions()
  
  const [accessChecked, setAccessChecked] = useState(false)
  const [hasAccess, setHasAccess] = useState(false)
  
  const loading = authLoading || permissionsLoading
  
  useEffect(() => {
    if (loading) return

    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    
    // Verificar permissão específica se fornecida
    if (requiredPermission) {
      const access = hasPermission(requiredPermission.modulo, requiredPermission.acao)
      setHasAccess(access)
      setAccessChecked(true)
      
      if (!access) {
        // Redirecionar para dashboard com parâmetro de erro
        router.push('/dashboard?error=access_denied')
      }
    } else {
      setHasAccess(true)
      setAccessChecked(true)
    }
  }, [isAuthenticated, loading, router, requiredPermission, hasPermission, canAccessRoute])

  // Timeout para evitar loading infinito
  useEffect(() => {
    const timer = setTimeout(() => {
      if (loading || !accessChecked) {
        console.warn("Timeout na verificação de acesso, permitindo acesso")
        setHasAccess(true)
        setAccessChecked(true)
      }
    }, 10000) // 10 segundos timeout

    return () => clearTimeout(timer)
  }, [loading, accessChecked])

  // Loading state
  if (loading || !accessChecked) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Verificando permissões...</p>
        </div>
      </div>
    )
  }

  // Access denied
  if (!isAuthenticated || !hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
              <p className="text-gray-600 mb-4">
                Você não tem permissão para acessar esta página.
              </p>
              <div className="space-y-2">
                <Button
                  onClick={() => router.push('/dashboard')}
                  className="w-full"
                >
                  Voltar ao Dashboard
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  className="w-full"
                >
                  Página Anterior
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Access granted
  return <>{children}</>
}

// Componente para proteger elementos específicos
interface ProtectedElementProps {
  children: React.ReactNode
  modulo: keyof Permissoes
  acao: string
  fallback?: React.ReactNode
}

export function ProtectedElement({
  children,
  modulo,
  acao,
  fallback = null
}: ProtectedElementProps) {
  const { hasPermission } = useCachedPermissions()
  
  if (!hasPermission(modulo, acao)) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}
