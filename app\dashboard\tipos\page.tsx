"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { Plus, Edit, Loader2, Trash2 } from "lucide-react"

interface Tipo {
  id: string
  nome: string
  descricao: string
  created_at: string
  updated_at: string
}

export default function TiposPage() {
  const notify = useNotify()
  const [tipos, setTipos] = useState<Tipo[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingTipo, setEditingTipo] = useState<Tipo | null>(null)
  const [formData, setFormData] = useState({
    nome: "",
    descricao: ""
  })

  useEffect(() => {
    fetchTipos()
  }, [])

  const fetchTipos = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/tipos")
      if (!response.ok) throw new Error("Erro ao buscar tipos")

      const data = await response.json()
      setTipos(data)
    } catch (error) {
      console.error("Erro ao buscar tipos:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os tipos de bordero.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSalvar = async () => {
    if (!formData.nome.trim()) {
      toast({
        title: "Erro",
        description: "Nome do tipo é obrigatório.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      const url = editingTipo ? `/api/tipos/${editingTipo.id}` : "/api/tipos"
      const method = editingTipo ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Erro ao ${editingTipo ? 'atualizar' : 'criar'} tipo`)
      }

      const tipo = await response.json()

      notify.success(
        editingTipo ? "Tipo Atualizado" : "Tipo Cadastrado",
        `Tipo "${tipo.nome}" foi ${editingTipo ? 'atualizado' : 'cadastrado'} com sucesso!`
      )

      setDialogOpen(false)
      setEditingTipo(null)
      setFormData({ nome: "", descricao: "" })
      fetchTipos()
    } catch (error: any) {
      console.error(`Erro ao ${editingTipo ? 'atualizar' : 'criar'} tipo:`, error)
      toast({
        title: "Erro",
        description: error.message || `Não foi possível ${editingTipo ? 'atualizar' : 'criar'} o tipo.`,
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditar = (tipo: Tipo) => {
    setEditingTipo(tipo)
    setFormData({
      nome: tipo.nome,
      descricao: tipo.descricao
    })
    setDialogOpen(true)
  }

  const handleExcluir = async (tipo: Tipo) => {
    try {
      const response = await fetch(`/api/tipos/${tipo.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao excluir tipo")
      }

      notify.success(
        "Tipo Excluído",
        `Tipo "${tipo.nome}" foi excluído com sucesso!`
      )

      fetchTipos()
    } catch (error: any) {
      console.error("Erro ao excluir tipo:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir o tipo.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Tipos de Bordero</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sky-600 hover:bg-sky-700">
              <Plus className="mr-2 h-4 w-4" />
              Novo Tipo
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingTipo ? "Editar Tipo de Bordero" : "Adicionar Novo Tipo de Bordero"}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome do Tipo</Label>
                <Input
                  id="nome"
                  placeholder="Digite o nome do tipo"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição</Label>
                <Textarea
                  id="descricao"
                  placeholder="Digite uma descrição para este tipo de bordero"
                  className="min-h-[100px]"
                  value={formData.descricao}
                  onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setDialogOpen(false)
                  setEditingTipo(null)
                  setFormData({ nome: "", descricao: "" })
                }}
                disabled={saving}
              >
                Cancelar
              </Button>
              <Button
                className="bg-sky-600 hover:bg-sky-700"
                onClick={handleSalvar}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Tipos Cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Descrição</TableHead>
                  <TableHead>Data de Criação</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tipos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                      Nenhum tipo de bordero cadastrado.
                    </TableCell>
                  </TableRow>
                ) : (
                  tipos.map((tipo) => (
                    <TableRow key={tipo.id}>
                      <TableCell className="font-medium">{tipo.nome}</TableCell>
                      <TableCell>{tipo.descricao}</TableCell>
                      <TableCell>{new Date(tipo.created_at).toLocaleDateString("pt-BR")}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditar(tipo)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir o tipo "{tipo.nome}"?
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleExcluir(tipo)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Excluir
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
