"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DashboardStats } from "@/components/dashboard/stats"
import { BorderoTableWithPagination } from "@/components/dashboard/bordero-table-with-pagination"
import { DateRangePicker } from "@/components/dashboard/date-range-picker"
import { BorderoFilter } from "@/components/dashboard/bordero-filter"
import { PermissionGuard } from "@/components/auth/permission-guard"

import { useSearchParams } from "next/navigation"
import { AlertTriangle } from "lucide-react"

export default function DashboardPage() {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })

  const searchParams = useSearchParams()
  const [showAccessDenied, setShowAccessDenied] = useState(false)

  useEffect(() => {
    if (searchParams.get('error') === 'access_denied') {
      setShowAccessDenied(true)
      // Remover o parâmetro da URL após 5 segundos
      setTimeout(() => setShowAccessDenied(false), 5000)
    }
  }, [searchParams])

  return (
    <PermissionGuard permission="dashboard">
      <div className="space-y-6">
        {showAccessDenied && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Você não tem permissão para acessar a página solicitada.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <div className="flex items-center gap-2">
            <DateRangePicker
              date={dateRange}
              onDateChange={(newDate) => {
                // Função wrapper para converter o tipo DateRange para o tipo esperado pelo setDateRange
                if (newDate) {
                  setDateRange({
                    from: newDate.from,
                    to: newDate.to !== undefined ? newDate.to : undefined
                  });
                } else {
                  setDateRange({ from: undefined, to: undefined });
                }
              }}
            />
            <BorderoFilter />
          </div>
        </div>

        <DashboardStats />

      <Tabs defaultValue="novos" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 h-auto">
          <TabsTrigger value="novos" className="text-xs md:text-sm py-2 px-2 md:px-4">
            Novos
          </TabsTrigger>
          <TabsTrigger value="analise" className="text-xs md:text-sm py-2 px-2 md:px-4">
            Análise
          </TabsTrigger>
          <TabsTrigger value="assinados" className="text-xs md:text-sm py-2 px-2 md:px-4">
            Assinados
          </TabsTrigger>
          <TabsTrigger value="pagos" className="text-xs md:text-sm py-2 px-2 md:px-4">
            Pagos
          </TabsTrigger>
        </TabsList>
        <TabsContent value="novos">
          <Card>
            <CardHeader>
              <CardTitle>Novos Borderos</CardTitle>
            </CardHeader>
            <CardContent>
              <BorderoTableWithPagination status="novo" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="analise">
          <Card>
            <CardHeader>
              <CardTitle>Borderos em Análise</CardTitle>
            </CardHeader>
            <CardContent>
              <BorderoTableWithPagination status="analise" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="assinados">
          <Card>
            <CardHeader>
              <CardTitle>Borderos Assinados</CardTitle>
            </CardHeader>
            <CardContent>
              <BorderoTableWithPagination status="assinado" />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="pagos">
          <Card>
            <CardHeader>
              <CardTitle>Borderos Pagos</CardTitle>
            </CardHeader>
            <CardContent>
              <BorderoTableWithPagination status="pago" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </PermissionGuard>
  )
}
