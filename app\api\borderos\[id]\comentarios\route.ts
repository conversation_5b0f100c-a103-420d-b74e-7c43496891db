import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

// GET - Buscar comentários de um bordero
export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: comentarios, error } = await supabase
      .from("bordero_comentarios")
      .select(`
        *,
        usuario:usuarios(id, nome, email)
      `)
      .eq("bordero_id", id)
      .order("created_at", { ascending: true })

    if (error) {
      console.error("Erro ao buscar comentários:", error)
      return NextResponse.json({ error: "Erro ao buscar comentários" }, { status: 500 })
    }

    return NextResponse.json(comentarios)
  } catch (error) {
    console.error("Erro na API de comentários:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

// POST - Criar novo comentário
export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { comentario, mencoes } = body

    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o bordero existe
    const { data: bordero, error: borderoError } = await supabase
      .from("borderos")
      .select("id, bordero_cod")
      .eq("id", id)
      .single()

    if (borderoError || !bordero) {
      return NextResponse.json({ error: "Bordero não encontrado" }, { status: 404 })
    }

    // Criar comentário
    const { data: novoComentario, error: createError } = await supabase
      .from("bordero_comentarios")
      .insert({
        bordero_id: id,
        usuario_id: session.user.id,
        comentario,
        mencoes: mencoes || []
      })
      .select(`
        *,
        usuario:usuarios(id, nome, email)
      `)
      .single()

    if (createError) {
      console.error("Erro ao criar comentário:", createError)
      return NextResponse.json({ error: "Erro ao criar comentário" }, { status: 500 })
    }

    // Registrar no log de atividades
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "comentar",
          entidade: "bordero",
          entidade_id: id,
          detalhes: {
            bordero_cod: bordero.bordero_cod,
            comentario: comentario.substring(0, 100) + (comentario.length > 100 ? "..." : ""),
            mencoes
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    // Log de comentário removido - comentários não aparecem mais na timeline

    // Criar notificações para usuários mencionados
    if (mencoes && mencoes.length > 0) {
      try {
        const notificacoes = mencoes.map((usuarioId: string) => ({
          titulo: "Você foi mencionado",
          mensagem: `Você foi mencionado em um comentário no bordero ${bordero.bordero_cod}`,
          usuario_id: usuarioId
        }))

        await supabase
          .from("notificacoes")
          .insert(notificacoes)
      } catch (notifError) {
        console.error("Erro ao criar notificações:", notifError)
      }
    }

    return NextResponse.json(novoComentario, { status: 201 })
  } catch (error) {
    console.error("Erro na API de comentários:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
