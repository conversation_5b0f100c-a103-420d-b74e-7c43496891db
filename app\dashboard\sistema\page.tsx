"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Tabs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/hooks/use-toast"
import {
  Database,
  Settings,
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Terminal,
  Server,
  Shield,
  Mail,
  Send,
  Loader2,
  Eye,
  Filter,
  Calendar,
  Download,
  FileText,
  Users,
  Building,
  ArrowRight,
  FolderOpen,
  Stethoscope,
  Bug
} from "lucide-react"

interface TableStatus {
  name: string
  exists: boolean
  count: number
  error?: string
}

interface SmtpConfig {
  id?: string
  host: string
  port: number
  email_remetente: string
  senha: string
  secure: boolean
}

interface SistemaLog {
  id: string
  usuario_id: string
  acao: string
  entidade: string
  detalhes: any
  created_at: string
  usuario?: {
    nome: string
    email: string
  }
}

export default function SistemaPage() {
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')

  const [loading, setLoading] = useState(false)
  const [tableStatus, setTableStatus] = useState<TableStatus[]>([])
  const [terminalOutput, setTerminalOutput] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState(tabParam || "verificacao")

  // Estados SMTP
  const [smtpLoading, setSmtpLoading] = useState(false)
  const [smtpTesting, setSmtpTesting] = useState(false)
  const [smtpConfig, setSmtpConfig] = useState<SmtpConfig>({
    host: "",
    port: 587,
    email_remetente: "",
    senha: "",
    secure: false
  })
  const [testEmail, setTestEmail] = useState("")

  // Estados Logs
  const [logs, setLogs] = useState<SistemaLog[]>([])
  const [logsLoading, setLogsLoading] = useState(false)
  const [logFilter, setLogFilter] = useState({
    acao: "",
    entidade: "",
    usuario: ""
  })

  // Estados Exportação
  const [exportLoading, setExportLoading] = useState<string | null>(null)

  // Estados Diagnóstico
  const [diagnosticoLoading, setDiagnosticoLoading] = useState(false)
  const [diagnosticoResultados, setDiagnosticoResultados] = useState<any>(null)

  useEffect(() => {
    carregarSmtpConfig()
    carregarLogs()
  }, [])

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam)
    }
  }, [tabParam])

  const carregarSmtpConfig = async () => {
    try {
      const response = await fetch("/api/sistema/smtp")
      if (response.ok) {
        const data = await response.json()
        if (data) {
          setSmtpConfig({
            ...data,
            senha: "" // Não carregar senha por segurança
          })
        }
      }
    } catch (error) {
      console.error("Erro ao carregar config SMTP:", error)
    }
  }

  const carregarLogs = async () => {
    setLogsLoading(true)
    try {
      const response = await fetch("/api/sistema/logs")
      if (response.ok) {
        const data = await response.json()
        setLogs(data)
      }
    } catch (error) {
      console.error("Erro ao carregar logs:", error)
    } finally {
      setLogsLoading(false)
    }
  }

  const salvarSmtpConfig = async () => {
    if (!smtpConfig.host || !smtpConfig.email_remetente || !smtpConfig.senha) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios.",
        variant: "destructive"
      })
      return
    }

    setSmtpLoading(true)
    try {
      const response = await fetch("/api/sistema/smtp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(smtpConfig)
      })

      if (response.ok) {
        toast({
          title: "Sucesso",
          description: "Configurações SMTP salvas com sucesso!"
        })
      } else {
        const error = await response.json()
        throw new Error(error.error)
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao salvar configurações",
        variant: "destructive"
      })
    } finally {
      setSmtpLoading(false)
    }
  }

  const testarSmtp = async () => {
    if (!testEmail) {
      toast({
        title: "Erro",
        description: "Digite um email para teste.",
        variant: "destructive"
      })
      return
    }

    setSmtpTesting(true)
    try {
      const response = await fetch("/api/sistema/smtp/test", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email_destino: testEmail })
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Sucesso",
          description: data.message
        })
      } else {
        const error = await response.json()
        throw new Error(error.error)
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao testar SMTP",
        variant: "destructive"
      })
    } finally {
      setSmtpTesting(false)
    }
  }

  const conectarSmtp = async () => {
    if (!smtpConfig.host || !smtpConfig.email_remetente || !smtpConfig.senha) {
      toast({
        title: "Erro",
        description: "Preencha todos os campos obrigatórios.",
        variant: "destructive"
      })
      return
    }

    setSmtpLoading(true)
    try {
      const response = await fetch("/api/sistema/smtp/connect", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(smtpConfig)
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Sucesso",
          description: data.message
        })
      } else {
        const error = await response.json()
        toast({
          title: "Erro de Conexão",
          description: error.error,
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: "Erro ao conectar com o servidor SMTP",
        variant: "destructive"
      })
    } finally {
      setSmtpLoading(false)
    }
  }

  const verificarTabelas = async () => {
    setLoading(true)
    setTerminalOutput([])

    try {
      addTerminalOutput("🔍 Iniciando verificação das tabelas...")

      const response = await fetch("/api/sistema/verificar-tabelas")
      const data = await response.json()

      if (response.ok) {
        setTableStatus(data.tables)
        addTerminalOutput("✅ Verificação concluída com sucesso!")

        data.tables.forEach((table: TableStatus) => {
          if (table.exists) {
            addTerminalOutput(`✅ ${table.name}: ${table.count} registros`)
          } else {
            addTerminalOutput(`❌ ${table.name}: Tabela não encontrada`)
          }
        })

        toast({
          title: "Verificação concluída",
          description: "Status das tabelas atualizado.",
        })
      } else {
        addTerminalOutput(`❌ Erro: ${data.error}`)
        toast({
          title: "Erro na verificação",
          description: data.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Erro ao verificar tabelas:", error)
      addTerminalOutput(`❌ Erro de conexão: ${error}`)
      toast({
        title: "Erro",
        description: "Erro ao conectar com o servidor.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const addTerminalOutput = (message: string) => {
    const timestamp = new Date().toLocaleTimeString("pt-BR")
    setTerminalOutput(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const executarDiagnostico = async () => {
    setDiagnosticoLoading(true)
    setDiagnosticoResultados(null)

    try {
      const response = await fetch("/api/borderos/diagnostico")
      const data = await response.json()

      if (response.ok) {
        setDiagnosticoResultados(data)
        toast({
          title: "Diagnóstico concluído",
          description: "Verificação do sistema de importação finalizada.",
        })
      } else {
        throw new Error(data.error || "Erro ao executar diagnóstico")
      }
    } catch (error: any) {
      console.error("Erro ao executar diagnóstico:", error)
      toast({
        title: "Erro",
        description: error.message || "Erro ao executar diagnóstico.",
        variant: "destructive",
      })
    } finally {
      setDiagnosticoLoading(false)
    }
  }

  const exportarTabela = async (tabela: string, incluirDados: boolean = true) => {
    setExportLoading(tabela)
    try {
      const response = await fetch(`/api/sistema/export/${tabela}?dados=${incluirDados}`)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || "Erro ao exportar tabela")
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${tabela}_${incluirDados ? 'com_dados' : 'estrutura'}_${new Date().toISOString().split('T')[0]}.sql`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Sucesso",
        description: `Tabela ${tabela} exportada com sucesso!`
      })
    } catch (error: any) {
      console.error("Erro ao exportar tabela:", error)
      toast({
        title: "Erro",
        description: error.message || "Erro ao exportar tabela",
        variant: "destructive"
      })
    } finally {
      setExportLoading(null)
    }
  }

  const getTableStatusIcon = (table: TableStatus) => {
    if (!table.exists) return <XCircle className="h-4 w-4 text-red-500" />
    if (table.count === 0) return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getTableStatusBadge = (table: TableStatus) => {
    if (!table.exists) return <Badge variant="destructive">Não existe</Badge>
    if (table.count === 0) return <Badge variant="outline">Vazia</Badge>
    return <Badge variant="default">OK</Badge>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Sistema</h1>
        <p className="text-muted-foreground">
          Configurações, verificações e logs do sistema
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="verificacao" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Verificação
          </TabsTrigger>
          <TabsTrigger value="diagnostico" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Diagnóstico
          </TabsTrigger>
          <TabsTrigger value="configuracao" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configuração
          </TabsTrigger>
          <TabsTrigger value="smtp" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            SMTP
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Logs
          </TabsTrigger>
        </TabsList>

        {/* Tab de Verificação */}
        <TabsContent value="verificacao" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Verificação de Tabelas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={verificarTabelas}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Database className="h-4 w-4" />
                  )}
                  {loading ? "Verificando..." : "Verificar Tabelas"}
                </Button>
                <p className="text-sm text-muted-foreground">
                  Verifica se todas as tabelas do banco estão funcionando
                </p>
              </div>

              {tableStatus.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h4 className="font-medium">Status das Tabelas</h4>
                    <div className="grid gap-3">
                      {tableStatus.map((table) => (
                        <div key={table.name} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            {getTableStatusIcon(table)}
                            <span className="font-medium">{table.name}</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-sm text-muted-foreground">
                              {table.exists ? `${table.count} registros` : "Não encontrada"}
                            </span>
                            {getTableStatusBadge(table)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {terminalOutput.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Terminal className="h-4 w-4" />
                      Terminal
                    </h4>
                    <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
                      {terminalOutput.map((line, index) => (
                        <div key={index}>{line}</div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Diagnóstico */}
        <TabsContent value="diagnostico" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                Diagnóstico de Importação
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Verificação do sistema para identificar problemas de importação
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Button
                      onClick={executarDiagnostico}
                      disabled={diagnosticoLoading}
                      className="flex items-center gap-2"
                    >
                      {diagnosticoLoading ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Stethoscope className="h-4 w-4" />
                      )}
                      {diagnosticoLoading ? "Executando..." : "Executar Diagnóstico"}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Verifica estrutura do banco, dados de referência e sistema de importação
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Link href="/dashboard/debug/direcionamentos">
                      <Button variant="outline" className="flex items-center gap-2">
                        <Bug className="h-4 w-4" />
                        Debug Direcionamentos
                      </Button>
                    </Link>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Ferramenta para debugar problemas com direcionamentos de usuários
                  </p>
                </div>
              </div>

              {diagnosticoResultados && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-medium">Resultados do Diagnóstico</h4>

                    {/* Estrutura do Banco */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm">Estrutura do Banco</h5>
                      <div className="grid gap-2">
                        {diagnosticoResultados.estrutura?.map((item: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                              {item.status === 'ok' ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span className="font-medium">{item.nome}</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="text-sm text-muted-foreground">
                                {item.detalhes}
                              </span>
                              <Badge variant={item.status === 'ok' ? 'default' : 'destructive'}>
                                {item.status === 'ok' ? 'OK' : 'ERRO'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Dados de Referência */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm">Dados de Referência</h5>
                      <div className="grid gap-2">
                        {diagnosticoResultados.dados?.map((item: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                              {item.status === 'ok' ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : item.status === 'aviso' ? (
                                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span className="font-medium">{item.nome}</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="text-sm text-muted-foreground">
                                {item.detalhes}
                              </span>
                              <Badge variant={
                                item.status === 'ok' ? 'default' :
                                item.status === 'aviso' ? 'secondary' : 'destructive'
                              }>
                                {item.status === 'ok' ? 'OK' :
                                 item.status === 'aviso' ? 'AVISO' : 'ERRO'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Teste de Importação */}
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm">Teste de Importação</h5>
                      <div className="grid gap-2">
                        {diagnosticoResultados.teste?.map((item: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                              {item.status === 'ok' ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span className="font-medium">{item.nome}</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="text-sm text-muted-foreground">
                                {item.detalhes}
                              </span>
                              <Badge variant={item.status === 'ok' ? 'default' : 'destructive'}>
                                {item.status === 'ok' ? 'OK' : 'ERRO'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Soluções */}
                    {diagnosticoResultados.solucoes && diagnosticoResultados.solucoes.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">Soluções Recomendadas</h5>
                        <div className="bg-muted/50 rounded-lg p-4 space-y-3">
                          {diagnosticoResultados.solucoes.map((solucao: any, index: number) => (
                            <div key={index} className="space-y-2">
                              <div className="flex items-start gap-2">
                                <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
                                <div>
                                  <p className="font-medium text-sm">{solucao.problema}</p>
                                  <p className="text-sm text-muted-foreground">{solucao.solucao}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Configuração */}
        <TabsContent value="configuracao" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Exportação de Tabelas
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Exporte as tabelas do banco de dados com ou sem dados para backup ou migração
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Exportar Usuários */}
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-500" />
                    <h3 className="font-medium">Usuários</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Exportar tabela de usuários e níveis de acesso
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportarTabela("usuarios", false)}
                      disabled={exportLoading === "usuarios"}
                    >
                      {exportLoading === "usuarios" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <FileText className="mr-2 h-3 w-3" />
                      )}
                      Estrutura
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => exportarTabela("usuarios", true)}
                      disabled={exportLoading === "usuarios"}
                    >
                      {exportLoading === "usuarios" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <Download className="mr-2 h-3 w-3" />
                      )}
                      Com Dados
                    </Button>
                  </div>
                </div>

                {/* Exportar Borderos */}
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-green-500" />
                    <h3 className="font-medium">Borderos</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Exportar tabela de borderos e comentários
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportarTabela("borderos", false)}
                      disabled={exportLoading === "borderos"}
                    >
                      {exportLoading === "borderos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <FileText className="mr-2 h-3 w-3" />
                      )}
                      Estrutura
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => exportarTabela("borderos", true)}
                      disabled={exportLoading === "borderos"}
                    >
                      {exportLoading === "borderos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <Download className="mr-2 h-3 w-3" />
                      )}
                      Com Dados
                    </Button>
                  </div>
                </div>

                {/* Exportar Secretarias */}
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <Building className="h-5 w-5 text-purple-500" />
                    <h3 className="font-medium">Secretarias</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Exportar tabela de secretarias
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportarTabela("secretarias", false)}
                      disabled={exportLoading === "secretarias"}
                    >
                      {exportLoading === "secretarias" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <FileText className="mr-2 h-3 w-3" />
                      )}
                      Estrutura
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => exportarTabela("secretarias", true)}
                      disabled={exportLoading === "secretarias"}
                    >
                      {exportLoading === "secretarias" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <Download className="mr-2 h-3 w-3" />
                      )}
                      Com Dados
                    </Button>
                  </div>
                </div>

                {/* Exportar Tipos */}
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <FolderOpen className="h-5 w-5 text-orange-500" />
                    <h3 className="font-medium">Tipos</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Exportar tabela de tipos de bordero
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportarTabela("tipos", false)}
                      disabled={exportLoading === "tipos"}
                    >
                      {exportLoading === "tipos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <FileText className="mr-2 h-3 w-3" />
                      )}
                      Estrutura
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => exportarTabela("tipos", true)}
                      disabled={exportLoading === "tipos"}
                    >
                      {exportLoading === "tipos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <Download className="mr-2 h-3 w-3" />
                      )}
                      Com Dados
                    </Button>
                  </div>
                </div>

                {/* Exportar Direcionamentos */}
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <ArrowRight className="h-5 w-5 text-red-500" />
                    <h3 className="font-medium">Direcionamentos</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Exportar tabela de direcionamentos
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => exportarTabela("direcionamentos", false)}
                      disabled={exportLoading === "direcionamentos"}
                    >
                      {exportLoading === "direcionamentos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <FileText className="mr-2 h-3 w-3" />
                      )}
                      Estrutura
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => exportarTabela("direcionamentos", true)}
                      disabled={exportLoading === "direcionamentos"}
                    >
                      {exportLoading === "direcionamentos" ? (
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      ) : (
                        <Download className="mr-2 h-3 w-3" />
                      )}
                      Com Dados
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="bg-muted/50 rounded-lg p-4">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Informações Importantes
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Estrutura:</strong> Exporta apenas a estrutura da tabela (CREATE TABLE)</li>
                  <li>• <strong>Com Dados:</strong> Exporta estrutura + todos os dados (INSERT statements)</li>
                  <li>• Os arquivos são gerados em formato SQL compatível com PostgreSQL</li>
                  <li>• Use estes backups para migração ou restauração no Supabase</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de SMTP */}
        <TabsContent value="smtp" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Configurações SMTP
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="smtp-host">Servidor SMTP *</Label>
                  <Input
                    id="smtp-host"
                    placeholder="smtp.gmail.com"
                    value={smtpConfig.host}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, host: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtp-port">Porta *</Label>
                  <Input
                    id="smtp-port"
                    type="number"
                    placeholder="587"
                    value={smtpConfig.port}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, port: parseInt(e.target.value) || 587 }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtp-email">Email Remetente *</Label>
                  <Input
                    id="smtp-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={smtpConfig.email_remetente}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, email_remetente: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtp-senha">Senha *</Label>
                  <Input
                    id="smtp-senha"
                    type="password"
                    placeholder="Digite a senha"
                    value={smtpConfig.senha}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, senha: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtp-secure">Segurança</Label>
                  <Select
                    value={smtpConfig.secure.toString()}
                    onValueChange={(value) => setSmtpConfig(prev => ({ ...prev, secure: value === "true" }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="false">STARTTLS (587)</SelectItem>
                      <SelectItem value="true">SSL/TLS (465)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="flex gap-4">
                <Button onClick={salvarSmtpConfig} disabled={smtpLoading}>
                  {smtpLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Settings className="mr-2 h-4 w-4" />
                  Salvar Configurações
                </Button>
                <Button
                  variant="outline"
                  onClick={conectarSmtp}
                  disabled={smtpLoading || !smtpConfig.host || !smtpConfig.email_remetente || !smtpConfig.senha}
                >
                  {smtpLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Server className="mr-2 h-4 w-4" />
                  Conectar
                </Button>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Testar Envio de Email</h4>
                <div className="flex gap-4">
                  <Input
                    placeholder="Digite um email para teste"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={testarSmtp} disabled={smtpTesting || !testEmail}>
                    {smtpTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Send className="mr-2 h-4 w-4" />
                    Testar Envio
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Envie um email de teste para verificar se as configurações estão funcionando.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Logs */}
        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Logs do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex gap-4 items-end">
                <div className="flex-1 space-y-2">
                  <Label>Filtrar por Ação</Label>
                  <Select value={logFilter.acao || "all"} onValueChange={(value) => setLogFilter(prev => ({ ...prev, acao: value === "all" ? "" : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Todas as ações" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as ações</SelectItem>
                      <SelectItem value="create">Criar</SelectItem>
                      <SelectItem value="update">Atualizar</SelectItem>
                      <SelectItem value="delete">Excluir</SelectItem>
                      <SelectItem value="test">Teste</SelectItem>
                      <SelectItem value="error">Erro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1 space-y-2">
                  <Label>Filtrar por Entidade</Label>
                  <Select value={logFilter.entidade || "all"} onValueChange={(value) => setLogFilter(prev => ({ ...prev, entidade: value === "all" ? "" : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Todas as entidades" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as entidades</SelectItem>
                      <SelectItem value="bordero">Bordero</SelectItem>
                      <SelectItem value="usuario">Usuário</SelectItem>
                      <SelectItem value="perfil">Perfil</SelectItem>
                      <SelectItem value="smtp">SMTP</SelectItem>
                      <SelectItem value="smtp_config">Config SMTP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={carregarLogs} disabled={logsLoading}>
                  {logsLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Atualizar
                </Button>
              </div>

              <Separator />

              {logsLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin" />
                  <p className="mt-2 text-muted-foreground">Carregando logs...</p>
                </div>
              ) : logs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhum log encontrado.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {logs
                    .filter(log =>
                      (!logFilter.acao || log.acao === logFilter.acao) &&
                      (!logFilter.entidade || log.entidade === logFilter.entidade)
                    )
                    .map((log) => (
                      <div key={log.id} className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              log.acao === "error" ? "destructive" :
                              log.acao === "create" ? "default" :
                              log.acao === "update" ? "secondary" :
                              log.acao === "delete" ? "outline" :
                              "default"
                            }>
                              {log.acao}
                            </Badge>
                            <span className="font-medium">{log.entidade}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(log.created_at).toLocaleString("pt-BR")}
                          </span>
                        </div>
                        {log.usuario && (
                          <p className="text-sm text-muted-foreground">
                            Usuário: {log.usuario.nome} ({log.usuario.email})
                          </p>
                        )}
                        {log.detalhes && (
                          <details className="text-sm">
                            <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                              Ver detalhes
                            </summary>
                            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                              {JSON.stringify(log.detalhes, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
