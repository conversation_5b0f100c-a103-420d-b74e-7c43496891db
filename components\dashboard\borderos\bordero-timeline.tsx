"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  MessageCircle,
  Edit,
  Trash2,
  PlusCircle,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Send,
  RotateCcw
} from "lucide-react"
import { safeFormatDistanceToNow } from "@/lib/date-utils"
import { toast } from "@/hooks/use-toast"

interface TimelineItem {
  id: string
  tipo: 'log'
  acao: string
  usuario: {
    id: string
    nome: string
    email: string
  }
  detalhes: any
  created_at: string
  data_hora?: string
}

interface BorderoTimelineProps {
  borderoId: string
  borderoCode: string
}

export function BorderoTimeline({ borderoId, borderoCode }: BorderoTimelineProps) {
  const [timeline, setTimeline] = useState<TimelineItem[]>([])
  const [loading, setLoading] = useState(true)

  const renderDetalhes = (detalhes: any): string => {
    if (typeof detalhes === 'string') {
      return detalhes
    }

    if (typeof detalhes === 'object' && detalhes !== null) {
      // Casos específicos para diferentes tipos de detalhes
      if (detalhes.comentario) {
        return `Comentário adicionado: "${detalhes.comentario.substring(0, 100)}${detalhes.comentario.length > 100 ? '...' : ''}"`
      }

      // Comentários editados/excluídos
      if (detalhes.comentario_anterior && detalhes.comentario_novo) {
        return `Comentário editado`
      }

      if (detalhes.comentario_excluido) {
        return `Comentário excluído`
      }

      if (detalhes.alteracoes && Array.isArray(detalhes.alteracoes)) {
        return detalhes.alteracoes.join('; ')
      }

      if (detalhes.status_anterior && detalhes.status_novo) {
        return `Status alterado de "${detalhes.status_anterior}" para "${detalhes.status_novo}"`
      }

      if (detalhes.bordero_cod) {
        return `Bordero ${detalhes.bordero_cod} foi processado`
      }

      if (detalhes.message) {
        return detalhes.message
      }

      // Fallback para outros objetos
      const keys = Object.keys(detalhes)
      if (keys.length === 1) {
        return `${keys[0]}: ${detalhes[keys[0]]}`
      }

      return keys.map(key => `${key}: ${detalhes[key]}`).join(', ')
    }

    return 'Ação realizada'
  }

  useEffect(() => {
    fetchTimeline()
  }, [borderoId])

  const fetchTimeline = async () => {
    try {
      setLoading(true)

      // Buscar APENAS logs do bordero (sem comentários)
      const logsResponse = await fetch(`/api/borderos/${borderoId}/logs`)
      const logs = logsResponse.ok ? await logsResponse.json() : []

      // Filtrar apenas logs administrativos (não comentários)
      const timelineItems: TimelineItem[] = logs
        .filter((log: any) =>
          log.acao !== 'comentario' &&
          log.acao !== 'comentario-editado' &&
          log.acao !== 'comentario-excluido'
        )
        .map((log: any) => ({
          ...log,
          tipo: 'log' as const,
          created_at: log.data_hora || log.created_at
        }))
        .sort((a: any, b: any) => {
          const dateA = new Date(a.created_at)
          const dateB = new Date(b.created_at)
          return dateB.getTime() - dateA.getTime() // Mais recentes primeiro
        })

      setTimeline(timelineItems)
    } catch (error) {
      console.error("Erro ao carregar timeline:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar o histórico do bordero.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Função de comentário removida - comentários agora são gerenciados separadamente

  const renderIconeAcao = (acao: string) => {
    switch (acao) {
      case 'criar':
        return <PlusCircle className="h-4 w-4 text-green-600" />
      case 'editar':
        return <Edit className="h-4 w-4 text-blue-600" />
      case 'status':
        return <RotateCcw className="h-4 w-4 text-purple-600" />
      case 'cancelar':
      case 'excluir':
        return <Trash2 className="h-4 w-4 text-red-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getCorAcao = (acao: string) => {
    switch (acao) {
      case 'criar':
        return 'bg-green-100 border-green-200 dark:bg-green-900/20 dark:border-green-800'
      case 'editar':
        return 'bg-blue-100 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
      case 'status':
        return 'bg-purple-100 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800'
      case 'cancelar':
      case 'excluir':
        return 'bg-red-100 border-red-200 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'bg-gray-100 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
    }
  }

  const formatarDetalhesLog = (tipo: string, detalhes: any): string => {
    switch (tipo) {
      case 'criar':
        // Formato: Bordero BR-0001 criado em 16/01/2024 às 14:26 por Rafael
        if (detalhes?.tipo === 'criacao') {
          const dataFormatada = detalhes.criado_em ? formatDateTime(detalhes.criado_em) : ''
          return `Bordero ${detalhes.bordero_cod} criado${dataFormatada ? ` em ${dataFormatada}` : ''} por ${detalhes.criado_por}`
        }
        return `Bordero ${detalhes.bordero_cod || 'criado'}${detalhes.valor ? ` no valor de ${formatarMoeda(detalhes.valor)}` : ''}${detalhes.nome_empresa ? ` para ${detalhes.nome_empresa}` : ''}`

      case 'editar':
        // Se há alterações específicas, mostrar elas
        if (typeof detalhes === 'string') {
          return detalhes
        }
        if (detalhes?.alteracoes && Array.isArray(detalhes.alteracoes)) {
          return detalhes.alteracoes.join('; ')
        }
        if (detalhes.campos_modificados && detalhes.campos_modificados.length > 0) {
          const campos = detalhes.campos_modificados.map((campo: string) => {
            const valorAntigo = detalhes.valores_antigos?.[campo]
            const valorNovo = detalhes.valores_novos?.[campo]

            if (valorAntigo && valorNovo) {
              // Formatação especial para campo valor
              if (campo === 'valor') {
                const valorAntigoFormatado = formatarMoeda(valorAntigo)
                const valorNovoFormatado = formatarMoeda(valorNovo)
                return `${formatarNomeCampo(campo)} alterado de ${valorAntigoFormatado} para ${valorNovoFormatado}`
              }
              return `${formatarNomeCampo(campo)} alterado de "${valorAntigo}" para "${valorNovo}"`
            }
            return `${formatarNomeCampo(campo)} alterado`
          })

          if (campos.length === 1) {
            return campos[0]
          }
          return `Campos alterados: ${campos.join(', ')}`
        }
        return 'Bordero editado'

      case 'status':
        if (detalhes.status_anterior && detalhes.status_novo) {
          return `Status alterado de "${formatarStatus(detalhes.status_anterior)}" para "${formatarStatus(detalhes.status_novo)}"`
        }
        return 'Status alterado'

      case 'cancelar':
      case 'excluir':
        if (typeof detalhes === 'string') {
          return detalhes
        }
        return `Bordero ${detalhes.bordero_cod || 'excluído'}${detalhes.motivo ? ` - Motivo: ${detalhes.motivo}` : ''}`

      default:
        if (typeof detalhes === 'string') {
          return detalhes
        }
        return 'Ação realizada'
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  const formatarNomeCampo = (campo: string): string => {
    const nomes: Record<string, string> = {
      'bordero_cod': 'Código',
      'valor': 'Valor',
      'data': 'Data',
      'nome_empresa': 'Empresa',
      'secretaria_id': 'Secretaria',
      'tipo_id': 'Tipo',
      'observacao': 'Observação',
      'responsavel_id': 'Responsável',
      'status': 'Status'
    }
    return nomes[campo] || campo
  }

  const formatarStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'novo': 'Novo',
      'analise': 'Em Análise',
      'assinado': 'Assinado',
      'pago': 'Pago',
      'corrigir': 'Corrigir',
      'cancelado': 'Cancelado',
      'excluido': 'Excluído',
      'arquivado': 'Arquivado'
    }
    return statusMap[status] || status
  }

  const formatarMoeda = (valor: number | string): string => {
    const numericValue = typeof valor === 'string' ? parseFloat(valor) : valor

    if (isNaN(numericValue)) {
      return 'R$ 0,00'
    }

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numericValue)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Timeline do Bordero
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Timeline do Bordero {borderoCode}
        </CardTitle>
      </CardHeader>
      <CardContent>

        {/* Timeline */}
        <div className="space-y-4">
          {timeline.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma atividade registrada ainda.</p>
            </div>
          ) : (
            timeline.map((item, index) => (
              <div key={item.id} className="flex gap-4">
                {/* Linha vertical */}
                <div className="flex flex-col items-center">
                  <div className={`p-2 rounded-full border-2 ${getCorAcao(item.acao)}`}>
                    {renderIconeAcao(item.acao)}
                  </div>
                  {index < timeline.length - 1 && (
                    <div className="w-px h-16 bg-gray-200 dark:bg-gray-700 mt-2"></div>
                  )}
                </div>

                {/* Conteúdo */}
                <div className="flex-1 pb-8">
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs bg-slate-100 dark:bg-slate-800">
                        {item.usuario.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium text-sm dark:text-white">{item.usuario.nome}</span>
                    <Badge variant="outline" className="text-xs border-slate-200 dark:border-slate-700 dark:text-slate-300">
                      Sistema
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {safeFormatDistanceToNow(item.created_at)}
                    </span>
                  </div>

                  <div className="text-sm text-slate-700 dark:text-slate-100 bg-slate-50 dark:bg-slate-800/40 rounded-lg p-3 border border-slate-200 dark:border-slate-700">
                    {formatarDetalhesLog(item.acao, item.detalhes)}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
