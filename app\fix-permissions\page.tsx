"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useSupabase } from "@/lib/supabase-provider"
import { usePermissions } from "@/hooks/use-permissions"

export default function FixPermissionsPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const { userDetails, userPermissions } = useSupabase()
  const { hasPermission, isAdmin } = usePermissions()

  const fixPermissions = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/debug-permissions", {
        method: "POST",
      })
      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error("Erro:", error)
      setResult({ error: "Erro ao corrigir permissões" })
    } finally {
      setLoading(false)
    }
  }

  const checkPermissions = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/debug-permissions")
      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error("Erro:", error)
      setResult({ error: "Erro ao verificar permissões" })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Debug de Permissões do Sistema</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Usuário Atual</h3>
              <div className="text-sm space-y-1">
                <p><strong>Nome:</strong> {userDetails?.nome || "Carregando..."}</p>
                <p><strong>Email:</strong> {userDetails?.email || "Carregando..."}</p>
                <p><strong>Nível:</strong> {userDetails?.nivel_acesso || "Carregando..."}</p>
                <p><strong>É Admin:</strong> {isAdmin() ? "Sim" : "Não"}</p>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Permissões Carregadas</h3>
              <div className="text-sm space-y-1">
                <p><strong>Dashboard:</strong> {hasPermission("dashboard") ? "✅" : "❌"}</p>
                <p><strong>Borderos:</strong> {hasPermission("borderos") ? "✅" : "❌"}</p>
                <p><strong>Secretarias:</strong> {hasPermission("secretarias") ? "✅" : "❌"}</p>
                <p><strong>Direcionamentos:</strong> {hasPermission("direcionamentos") ? "✅" : "❌"}</p>
                <p><strong>Tipos:</strong> {hasPermission("tipos") ? "✅" : "❌"}</p>
                <p><strong>Usuários:</strong> {hasPermission("usuarios") ? "✅" : "❌"}</p>
                <p><strong>Relatórios:</strong> {hasPermission("relatorios") ? "✅" : "❌"}</p>
                <p><strong>Configurações:</strong> {hasPermission("configuracoes") ? "✅" : "❌"}</p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Permissões Raw</h3>
            <pre className="bg-gray-100 p-2 rounded text-xs">
              {JSON.stringify(userPermissions, null, 2)}
            </pre>
          </div>

          <div className="flex gap-4">
            <Button onClick={checkPermissions} disabled={loading}>
              Verificar Permissões no Banco
            </Button>
            <Button onClick={fixPermissions} disabled={loading} variant="destructive">
              Corrigir Permissões do Cadastrador
            </Button>
          </div>

          {result && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Resultado da API</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
