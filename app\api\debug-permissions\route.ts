import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar todos os níveis de acesso com suas permissões
    const { data: niveisAcesso, error: niveisError } = await supabase
      .from("niveis_acesso")
      .select("*")
      .order("nome")

    if (niveisError) {
      console.error("Erro ao buscar níveis de acesso:", niveisError)
      return NextResponse.json({ error: "Erro ao buscar níveis de acesso" }, { status: 500 })
    }

    // Buscar todos os usuários com seus níveis
    const { data: usuarios, error: usuariosError } = await supabase
      .from("usuarios")
      .select(`
        id,
        nome,
        email,
        nivel_acesso_id
      `)
      .order("nome")

    if (usuariosError) {
      console.error("Erro ao buscar usuários:", usuariosError)
      return NextResponse.json({ error: "Erro ao buscar usuários" }, { status: 500 })
    }

    // Buscar detalhes dos níveis para cada usuário
    const usuariosComNiveis = await Promise.all(
      usuarios.map(async (usuario) => {
        if (usuario.nivel_acesso_id) {
          const { data: nivel } = await supabase
            .from("niveis_acesso")
            .select("nome, permissoes")
            .eq("id", usuario.nivel_acesso_id)
            .single()

          return {
            ...usuario,
            nivel_acesso: nivel
          }
        }
        return usuario
      })
    )

    return NextResponse.json({
      niveisAcesso,
      usuarios: usuariosComNiveis,
      debug: {
        timestamp: new Date().toISOString(),
        message: "Debug de permissões"
      }
    })
  } catch (error) {
    console.error("Erro no debug de permissões:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Atualizar permissões do nível "Cadastrador"
    const { data: cadastradorUpdate, error: updateError } = await supabase
      .from("niveis_acesso")
      .update({
        permissoes: {
          dashboard: true,
          borderos: true,
          secretarias: false,
          direcionamentos: false,
          tipos: false,
          usuarios: false,
          relatorios: false,
          configuracoes: false
        }
      })
      .eq("nome", "Cadastrador")
      .select()

    if (updateError) {
      console.error("Erro ao atualizar permissões:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar permissões" }, { status: 500 })
    }

    return NextResponse.json({
      message: "Permissões atualizadas com sucesso",
      updated: cadastradorUpdate
    })
  } catch (error) {
    console.error("Erro ao atualizar permissões:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
