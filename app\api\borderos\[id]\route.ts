import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { registrarLogCliente } from "@/lib/server/log-service"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: bordero, error } = await supabase
      .from("borderos")
      .select(`
        *,
        secretaria:secretarias(*),
        tipo:tipos(*),
        responsavel:usuarios(*)
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Erro ao buscar bordero:", error)
      return NextResponse.json({ error: "Bordero não encontrado" }, { status: 404 })
    }

    return NextResponse.json(bordero)
  } catch (error) {
    console.error("Erro ao buscar bordero:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { bordero_cod, valor, data, nome_empresa, secretaria_id, tipo_id, observacao } = body

    // Obter usuário autenticado
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar bordero atual para comparação
    const { data: borderoAtual, error: fetchError } = await supabase
      .from("borderos")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError || !borderoAtual) {
      return NextResponse.json({ error: "Bordero não encontrado" }, { status: 404 })
    }

    // Atualizar bordero
    const { data: borderoAtualizado, error: updateError } = await supabase
      .from("borderos")
      .update({
        bordero_cod,
        valor,
        data,
        nome_empresa,
        secretaria_id,
        tipo_id,
        observacao,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (updateError) {
      console.error("Erro ao atualizar bordero:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar bordero" }, { status: 500 })
    }

    // Registrar log de alteração
    const alteracoes = []
    if (borderoAtual.bordero_cod !== bordero_cod) {
      alteracoes.push(`Código alterado de "${borderoAtual.bordero_cod}" para "${bordero_cod}"`)
    }
    if (borderoAtual.valor !== valor) {
      alteracoes.push(`Valor alterado de R$ ${borderoAtual.valor} para R$ ${valor}`)
    }
    if (borderoAtual.data !== data) {
      alteracoes.push(`Data alterada de ${borderoAtual.data} para ${data}`)
    }
    if (borderoAtual.nome_empresa !== nome_empresa) {
      alteracoes.push(`Empresa alterada de "${borderoAtual.nome_empresa}" para "${nome_empresa}"`)
    }
    if (borderoAtual.secretaria_id !== secretaria_id) {
      alteracoes.push(`Secretaria alterada`)
    }
    if (borderoAtual.tipo_id !== tipo_id) {
      alteracoes.push(`Tipo alterado`)
    }
    if (borderoAtual.observacao !== observacao) {
      alteracoes.push(`Observação alterada`)
    }

    if (alteracoes.length > 0) {
      try {
        // Registrar no log geral
        await supabase
          .from("log_atividades")
          .insert({
            usuario_id: user.id,
            acao: "atualizar",
            entidade: "bordero",
            entidade_id: id,
            detalhes: {
              bordero_cod: borderoAtualizado.bordero_cod,
              alteracoes: alteracoes
            },
            ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
            user_agent: request.headers.get("user-agent") || "desconhecido"
          })

        // Registrar no log específico do bordero
        await supabase
          .from("bordero_logs")
          .insert({
            bordero_id: id,
            usuario_id: user.id,
            acao: "editar",
            detalhes: alteracoes.join("; "),
            data_hora: new Date().toISOString()
          })
      } catch (logError) {
        console.error("Erro ao registrar log de edição:", logError)
      }
    }

    return NextResponse.json(borderoAtualizado)
  } catch (error) {
    console.error("Erro ao atualizar bordero:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    console.log("🗑️ Iniciando exclusão de bordero...")
    const { id } = await params
    console.log("📋 ID do bordero:", id)

    const body = await request.json()
    const { motivo } = body
    console.log("📝 Motivo da exclusão:", motivo)

    if (!motivo || !motivo.trim()) {
      console.log("❌ Motivo da exclusão não fornecido")
      return NextResponse.json({ error: "Motivo da exclusão é obrigatório" }, { status: 400 })
    }

    // Obter usuário autenticado
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o bordero existe
    const { data: bordero, error: fetchError } = await supabase
      .from("borderos")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError || !bordero) {
      return NextResponse.json({ error: "Bordero não encontrado" }, { status: 404 })
    }

    // Verificar se o bordero já foi cancelado
    if (bordero.status === "cancelado") {
      return NextResponse.json({ error: "Bordero já foi cancelado" }, { status: 400 })
    }

    // Fazer exclusão lógica - alterar status para "cancelado" e adicionar motivo
    const { data: borderoAtualizado, error: updateError } = await supabase
      .from("borderos")
      .update({
        status: "cancelado",
        dados_status: motivo,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (updateError) {
      console.error("Erro ao excluir bordero:", updateError)
      return NextResponse.json({ error: "Erro ao excluir bordero" }, { status: 500 })
    }

    // Registrar log de exclusão
    try {
      // Registrar no log geral
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: user.id,
          acao: "cancelar",
          entidade: "bordero",
          entidade_id: id,
          detalhes: {
            bordero_cod: bordero.bordero_cod,
            motivo: motivo
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })

      // Registrar no log específico do bordero
      await supabase
        .from("bordero_logs")
        .insert({
          bordero_id: id,
          usuario_id: user.id,
          acao: "cancelar",
          detalhes: `Bordero ${bordero.bordero_cod} cancelado. Motivo: ${motivo}`,
          data_hora: new Date().toISOString()
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exclusão:", logError)
    }

    console.log("✅ Bordero cancelado com sucesso:", borderoAtualizado.id)
    return NextResponse.json({
      message: "Bordero cancelado com sucesso",
      bordero: borderoAtualizado
    })
  } catch (error: any) {
    console.error("💥 Erro crítico ao excluir bordero:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error.message
    }, { status: 500 })
  }
}
