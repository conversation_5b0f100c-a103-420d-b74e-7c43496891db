generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("NEON_DATABASE_URL")
}

model Usuario {
  id            Int            @id @default(autoincrement())
  nome          String
  email         String         @unique
  senha         String
  nivelAcesso   String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  secretarias   SecretariaUsuario[]
  borderos      Bordero[]
  notificacoes  Notificacao[]
  direcionamentos DirecionamentoUsuario[]
  logAtividades LogAtividade[]
  devolucoes    BorderoDevolucao[]
}

model Secretaria {
  id            Int            @id @default(autoincrement())
  nome          String
  slug          String         @unique
  valoresTotal  Float          @default(0)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  usuarios      SecretariaUsuario[]
  borderos      Bordero[]
}

model SecretariaUsuario {
  id            Int            @id @default(autoincrement())
  secretariaId  Int
  usuarioId     Int
  secretaria    Secretaria     @relation(fields: [secretariaId], references: [id])
  usuario       Usuario        @relation(fields: [usuarioId], references: [id])

  @@unique([secretariaId, usuarioId])
}

model Tipo {
  id            Int            @id @default(autoincrement())
  nome          String
  descricao     String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  borderos      Bordero[]
}

model Direcionamento {
  id            Int            @id @default(autoincrement())
  nome          String
  valorMinimo   Float
  valorMaximo   Float
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  usuarios      DirecionamentoUsuario[]
}

model DirecionamentoUsuario {
  id               Int            @id @default(autoincrement())
  direcionamentoId Int
  usuarioId        Int
  direcionamento   Direcionamento @relation(fields: [direcionamentoId], references: [id])
  usuario          Usuario        @relation(fields: [usuarioId], references: [id])

  @@unique([direcionamentoId, usuarioId])
}

model Bordero {
  id            Int            @id @default(autoincrement())
  borderoCod    String         @unique
  valor         Float
  data          DateTime
  nomeEmpresa   String
  secretariaId  Int
  tipoId        Int
  observacao    String?
  status        String         @default("novo")
  dadosStatus   String?
  dataAlteracao DateTime       @updatedAt
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  secretaria    Secretaria     @relation(fields: [secretariaId], references: [id])
  tipo          Tipo           @relation(fields: [tipoId], references: [id])
  devolucoes    BorderoDevolucao[]
  responsavelId Int?
  responsavel   Usuario?       @relation(fields: [responsavelId], references: [id])
}

model BorderoDevolucao {
  id            Int            @id @default(autoincrement())
  borderoId     Int
  usuarioId     Int
  bordero       Bordero        @relation(fields: [borderoId], references: [id])
  usuario       Usuario        @relation(fields: [usuarioId], references: [id], onDelete: Cascade)

  @@unique([borderoId, usuarioId])
}

model Notificacao {
  id            Int            @id @default(autoincrement())
  titulo        String
  mensagem      String
  lida          Boolean        @default(false)
  usuarioId     Int
  createdAt     DateTime       @default(now())
  usuario       Usuario        @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
}

model LogAtividade {
  id            Int            @id @default(autoincrement())
  usuarioId     Int
  acao          String
  entidade      String
  entidadeId    String?
  detalhes      Json?
  ip            String?
  userAgent     String?
  createdAt     DateTime       @default(now())
  usuario       Usuario        @relation(fields: [usuarioId], references: [id], onDelete: Cascade)
}
