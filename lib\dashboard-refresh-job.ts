/**
 * Dashboard Refresh Job
 * 
 * Este módulo implementa um job agendado para processar a fila de atualização
 * das materialized views do dashboard.
 */

import { createClient } from "@supabase/supabase-js";

// Intervalo de processamento em milissegundos (5 minutos)
const REFRESH_INTERVAL = 5 * 60 * 1000;

let jobRunning = false;
let jobInterval: NodeJS.Timeout | null = null;

/**
 * Processa a fila de atualização do dashboard
 */
export async function processDashboardQueue() {
  // Evitar execuções concorrentes
  if (jobRunning) {
    console.log('🔄 Job de atualização do dashboard já está em execução');
    return;
  }

  jobRunning = true;
  console.log('🔄 Iniciando processamento da fila de atualização do dashboard');

  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Processar a fila de atualização
    const { data: result, error } = await supabase
      .rpc('process_dashboard_refresh_queue');

    if (error) {
      console.error('❌ Erro ao processar fila de atualização:', error);
    } else {
      console.log(`✅ Fila de atualização processada com sucesso. Itens processados: ${result}`);
    }
  } catch (error) {
    console.error('💥 Erro crítico no job de atualização do dashboard:', error);
  } finally {
    jobRunning = false;
  }
}

/**
 * Inicia o job agendado para processar a fila de atualização
 */
export function startDashboardRefreshJob() {
  if (jobInterval) {
    console.log('⚠️ Job de atualização do dashboard já está iniciado');
    return;
  }

  console.log(`🚀 Iniciando job de atualização do dashboard (intervalo: ${REFRESH_INTERVAL / 1000}s)`);
  
  // Executar imediatamente na inicialização
  processDashboardQueue();
  
  // Agendar execuções periódicas
  jobInterval = setInterval(processDashboardQueue, REFRESH_INTERVAL);
}

/**
 * Para o job agendado
 */
export function stopDashboardRefreshJob() {
  if (!jobInterval) {
    console.log('⚠️ Job de atualização do dashboard não está em execução');
    return;
  }

  clearInterval(jobInterval);
  jobInterval = null;
  console.log('🛑 Job de atualização do dashboard parado');
}