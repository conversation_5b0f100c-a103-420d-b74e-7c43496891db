"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import { BorderoTimeline } from "@/components/dashboard/borderos/bordero-timeline"
import { ComentariosBordero } from "@/components/dashboard/borderos/comentarios-bordero"
import { ArrowLeft, Edit, Clock, User } from "lucide-react"
import Link from "next/link"
import { safeFormatDateTime, safeFormatDate } from "@/lib/date-utils"

interface BorderoLog {
  id: string
  acao: string
  detalhes: string
  data_hora: string
  usuario: {
    nome: string
    email: string
  }
}

interface Bordero {
  id: string
  bordero_cod: string
  valor: number
  data: string
  nome_empresa: string
  observacao: string
  status: string
  created_at: string
  updated_at: string
  secretaria: {
    id: string
    nome: string
  }
  tipo: {
    id: string
    nome: string
  }
  responsavel?: {
    id: string
    nome: string
    email: string
  }
  logs: BorderoLog[]
}

export default function DetalhesBorderoPage() {
  const params = useParams()
  const router = useRouter()
  const borderoId = params.id as string

  const [bordero, setBordero] = useState<Bordero | null>(null)
  const [loading, setLoading] = useState(true)
  const [timelineRefresh, setTimelineRefresh] = useState(0)

  useEffect(() => {
    fetchBordero()
  }, [borderoId])

  const fetchBordero = async () => {
    try {
      const response = await fetch(`/api/borderos/${borderoId}`)
      if (!response.ok) {
        throw new Error("Bordero não encontrado")
      }

      const data = await response.json()
      setBordero(data)
    } catch (error) {
      console.error("Erro ao buscar bordero:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar o bordero.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "novo":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            Novo
          </Badge>
        )
      case "analise":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
            Em Análise
          </Badge>
        )
      case "assinado":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            Assinado
          </Badge>
        )
      case "pago":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">
            Pago
          </Badge>
        )
      case "corrigir":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
            Corrigir
          </Badge>
        )
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  // Removido - usando safeFormatDateTime do utils

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-sky-600 rounded-full border-t-transparent"></div>
      </div>
    )
  }

  if (!bordero) {
    return (
      <div className="text-center py-8">
        <p>Bordero não encontrado.</p>
        <Link href="/dashboard/borderos">
          <Button className="mt-4">Voltar</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/borderos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detalhes do Bordero</h1>
            <p className="text-muted-foreground">{bordero.bordero_cod}</p>
          </div>
        </div>
        <Link href={`/dashboard/borderos/${bordero.id}/editar`}>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Editar
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informações Principais */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informações Gerais</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Código</label>
                  <p className="text-lg font-semibold">{bordero.bordero_cod}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">{renderStatusBadge(bordero.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Empresa</label>
                  <p className="text-sm">{bordero.nome_empresa}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Valor</label>
                  <p className="text-lg font-semibold text-green-600">
                    {new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL"
                    }).format(bordero.valor)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Data</label>
                  <p className="text-sm">{safeFormatDate(bordero.data)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Secretaria</label>
                  <p className="text-sm">{bordero.secretaria.nome}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tipo</label>
                  <p className="text-sm">{bordero.tipo.nome}</p>
                </div>
                {bordero.responsavel && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Responsável</label>
                    <p className="text-sm">{bordero.responsavel.nome}</p>
                  </div>
                )}
              </div>

              {bordero.observacao && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Observação</label>
                    <p className="text-sm mt-1">{bordero.observacao}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Comentários do Bordero */}
          <ComentariosBordero borderoId={bordero.id} borderoCode={bordero.bordero_cod} />
        </div>

        {/* Informações Laterais */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Datas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Criado em</label>
                <p className="text-sm">{safeFormatDateTime(bordero.created_at)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Última atualização</label>
                <p className="text-sm">{safeFormatDateTime(bordero.updated_at)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Timeline do Bordero */}
          <BorderoTimeline borderoId={bordero.id} borderoCode={bordero.bordero_cod} />
        </div>
      </div>
    </div>
  )
}
