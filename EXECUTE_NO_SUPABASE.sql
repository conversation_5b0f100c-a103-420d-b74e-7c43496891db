-- =====================================================
-- EXECUTE ESTE SQL NO SUPABASE SQL EDITOR
-- =====================================================
-- Copie e cole este código completo no SQL Editor do Supabase Dashboard
-- e clique em "Run" para configurar todas as tabelas necessárias

-- 1. CRIAR TABELA CONFIGURACOES_SISTEMA
-- =====================================================
DO $$
BEGIN
  -- Criar tabela se não existir
  CREATE TABLE IF NOT EXISTS configuracoes_sistema (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
    modo_escuro BOOLEAN DEFAULT false,
    notificacoes_email BOOLEAN DEFAULT true,
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Habilitar RLS
  ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;

  -- Criar política
  DROP POLICY IF EXISTS "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema;
  CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
  FOR ALL USING (true);

  -- Inserir configuração padrão
  INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
  SELECT 'CRM de Bordero', false, true
  WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);

  RAISE NOTICE 'Tabela configuracoes_sistema configurada com sucesso';
END
$$;

-- 2. CRIAR TABELAS DE LOG
-- =====================================================
DO $$
BEGIN
  -- Criar tabela log_atividades
  CREATE TABLE IF NOT EXISTS log_atividades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id),
    acao VARCHAR(100) NOT NULL,
    entidade VARCHAR(100) NOT NULL,
    entidade_id UUID,
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Criar tabela bordero_logs
  CREATE TABLE IF NOT EXISTS bordero_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bordero_id UUID REFERENCES borderos(id),
    usuario_id UUID REFERENCES usuarios(id),
    acao VARCHAR(100) NOT NULL,
    detalhes JSONB,
    data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Habilitar RLS
  ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;
  ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;

  -- Criar políticas
  DROP POLICY IF EXISTS "Permitir acesso total a log_atividades" ON log_atividades;
  CREATE POLICY "Permitir acesso total a log_atividades" ON log_atividades
  FOR ALL USING (true);

  DROP POLICY IF EXISTS "Permitir acesso total a bordero_logs" ON bordero_logs;
  CREATE POLICY "Permitir acesso total a bordero_logs" ON bordero_logs
  FOR ALL USING (true);

  RAISE NOTICE 'Tabelas de log configuradas com sucesso';
END
$$;

-- 3. CRIAR TABELA BORDERO_COMENTARIOS
-- =====================================================
DO $$
BEGIN
  -- Criar tabela bordero_comentarios
  CREATE TABLE IF NOT EXISTS bordero_comentarios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bordero_id UUID REFERENCES borderos(id) ON DELETE CASCADE,
    usuario_id UUID REFERENCES usuarios(id),
    comentario TEXT NOT NULL,
    mencoes JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Habilitar RLS
  ALTER TABLE bordero_comentarios ENABLE ROW LEVEL SECURITY;

  -- Criar política
  DROP POLICY IF EXISTS "Permitir acesso total a bordero_comentarios" ON bordero_comentarios;
  CREATE POLICY "Permitir acesso total a bordero_comentarios" ON bordero_comentarios
  FOR ALL USING (true);

  RAISE NOTICE 'Tabela bordero_comentarios configurada com sucesso';
END
$$;

-- 4. CRIAR TABELAS PARA REDEFINIÇÃO DE SENHA
-- =====================================================
DO $$
BEGIN
  -- Criar tabela codigo_verificacao
  CREATE TABLE IF NOT EXISTS codigo_verificacao (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    codigo VARCHAR(6) NOT NULL,
    tipo VARCHAR(50) NOT NULL DEFAULT 'reset_password',
    expira_em TIMESTAMP WITH TIME ZONE NOT NULL,
    usado BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Criar tabela reset_tokens
  CREATE TABLE IF NOT EXISTS reset_tokens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expira_em TIMESTAMP WITH TIME ZONE NOT NULL,
    usado BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Habilitar RLS
  ALTER TABLE codigo_verificacao ENABLE ROW LEVEL SECURITY;
  ALTER TABLE reset_tokens ENABLE ROW LEVEL SECURITY;

  -- Criar políticas
  DROP POLICY IF EXISTS "Permitir acesso total a codigo_verificacao" ON codigo_verificacao;
  CREATE POLICY "Permitir acesso total a codigo_verificacao" ON codigo_verificacao
  FOR ALL USING (true);

  DROP POLICY IF EXISTS "Permitir acesso total a reset_tokens" ON reset_tokens;
  CREATE POLICY "Permitir acesso total a reset_tokens" ON reset_tokens
  FOR ALL USING (true);

  RAISE NOTICE 'Tabelas de redefinição de senha configuradas com sucesso';
END
$$;

-- 5. CORRIGIR POLÍTICAS RLS CRÍTICAS
-- =====================================================
DO $$
BEGIN
  -- Desabilitar RLS temporariamente para borderos
  ALTER TABLE borderos DISABLE ROW LEVEL SECURITY;

  -- Reabilitar com políticas corretas
  ALTER TABLE borderos ENABLE ROW LEVEL SECURITY;

  -- Remover todas as políticas existentes
  DROP POLICY IF EXISTS "Permitir acesso total a borderos" ON borderos;
  DROP POLICY IF EXISTS "borderos_policy" ON borderos;
  DROP POLICY IF EXISTS "Enable all for authenticated users" ON borderos;

  -- Criar política simples e funcional
  CREATE POLICY "borderos_all_access" ON borderos
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  -- Corrigir política da tabela usuarios
  ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a usuarios" ON usuarios;
  DROP POLICY IF EXISTS "usuarios_policy" ON usuarios;

  CREATE POLICY "usuarios_all_access" ON usuarios
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  -- Corrigir política da tabela secretarias
  ALTER TABLE secretarias ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a secretarias" ON secretarias;
  DROP POLICY IF EXISTS "secretarias_policy" ON secretarias;

  CREATE POLICY "secretarias_all_access" ON secretarias
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  -- Corrigir política da tabela tipos
  ALTER TABLE tipos ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a tipos" ON tipos;
  DROP POLICY IF EXISTS "tipos_policy" ON tipos;

  CREATE POLICY "tipos_all_access" ON tipos
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  -- Corrigir política da tabela bordero_comentarios
  ALTER TABLE bordero_comentarios ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a bordero_comentarios" ON bordero_comentarios;

  CREATE POLICY "bordero_comentarios_all_access" ON bordero_comentarios
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  RAISE NOTICE 'Políticas RLS corrigidas com sucesso';
END
$$;

-- 6. CRIAR TABELA SMTP_CONFIG
-- =====================================================
DO $$
BEGIN
  CREATE TABLE IF NOT EXISTS smtp_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL DEFAULT 587,
    email_remetente VARCHAR(255) NOT NULL,
    senha VARCHAR(255) NOT NULL,
    secure BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  ALTER TABLE smtp_config ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a smtp_config" ON smtp_config;
  CREATE POLICY "Permitir acesso total a smtp_config" ON smtp_config
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  RAISE NOTICE 'Tabela smtp_config criada com sucesso';
END
$$;

-- 7. CRIAR TABELA SISTEMA_LOGS
-- =====================================================
DO $$
BEGIN
  CREATE TABLE IF NOT EXISTS sistema_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id),
    acao VARCHAR(50) NOT NULL,
    entidade TEXT NOT NULL,
    detalhes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  ALTER TABLE sistema_logs ENABLE ROW LEVEL SECURITY;

  DROP POLICY IF EXISTS "Permitir acesso total a sistema_logs" ON sistema_logs;
  CREATE POLICY "Permitir acesso total a sistema_logs" ON sistema_logs
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

  RAISE NOTICE 'Tabela sistema_logs criada com sucesso';
END
$$;

-- 8. VERIFICAR CONFIGURAÇÃO FINAL
-- =====================================================
-- Verificar se as tabelas foram criadas
SELECT 'configuracoes_sistema' as tabela, COUNT(*) as registros FROM configuracoes_sistema
UNION ALL
SELECT 'niveis_acesso' as tabela, COUNT(*) as registros FROM niveis_acesso
UNION ALL
SELECT 'log_atividades' as tabela, COUNT(*) as registros FROM log_atividades
UNION ALL
SELECT 'bordero_logs' as tabela, COUNT(*) as registros FROM bordero_logs
UNION ALL
SELECT 'bordero_comentarios' as tabela, COUNT(*) as registros FROM bordero_comentarios
UNION ALL
SELECT 'codigo_verificacao' as tabela, COUNT(*) as registros FROM codigo_verificacao
UNION ALL
SELECT 'reset_tokens' as tabela, COUNT(*) as registros FROM reset_tokens
UNION ALL
SELECT 'smtp_config' as tabela, COUNT(*) as registros FROM smtp_config
UNION ALL
SELECT 'sistema_logs' as tabela, COUNT(*) as registros FROM sistema_logs;

-- Verificar permissões dos níveis de acesso
SELECT nome, permissoes FROM niveis_acesso ORDER BY nome;
