# 🧪 TESTE DE ALTERAÇÃO DE SENHA

## ✅ **FUNCIONALIDADES IMPLEMENTADAS**

### 1. **Campo de Senha no Modal de Edição de Usuário**
- ✅ Campo "Nova Senha" aparece apenas para administradores
- ✅ Campo só é exibido ao editar usuários (não ao criar)
- ✅ Usa o componente `EnhancedPasswordInput` com validação
- ✅ Campo é opcional (deixar em branco mantém senha atual)

### 2. **API de Alteração de Senha**
- ✅ Verifica se usuário atual é administrador
- ✅ Busca usuário no Supabase Auth pelo email
- ✅ Atualiza senha usando `auth.admin.updateUserById`
- ✅ Registra log de alteração de senha
- ✅ Retorna mensagens de sucesso/warning apropriadas

### 3. **Botões de Exportação de Tabelas**
- ✅ Seção "Exportação de Tabelas" na aba Configuração do Sistema
- ✅ Botões para exportar: Usuários, Borderos, Secretarias, Tipos, Direcionamentos
- ✅ Opções: "Estrutura" (apenas CREATE TABLE) ou "Com Dados" (CREATE + INSERT)
- ✅ API `/api/sistema/export/[tabela]` implementada
- ✅ Verificação de permissão de administrador
- ✅ Geração de arquivos SQL compatíveis com PostgreSQL

## 🔧 **COMO TESTAR**

### Teste 1: Alteração de Senha
1. Faça login como administrador
2. Vá para `/dashboard/usuarios`
3. Clique em "Editar" em qualquer usuário
4. Observe que o campo "Nova Senha" aparece
5. Digite uma nova senha
6. Clique em "Salvar"
7. Verifique se aparece mensagem de sucesso
8. Teste fazer login com o usuário usando a nova senha

### Teste 2: Exportação de Tabelas
1. Faça login como administrador
2. Vá para `/dashboard/sistema`
3. Clique na aba "Configuração"
4. Teste os botões de exportação:
   - Clique em "Estrutura" para baixar apenas a estrutura
   - Clique em "Com Dados" para baixar estrutura + dados
5. Verifique se os arquivos SQL são baixados corretamente

## 🐛 **POSSÍVEIS PROBLEMAS E SOLUÇÕES**

### Problema: "Invalid login credentials"
**Causa:** Senha não foi atualizada no Supabase Auth
**Solução:** 
- Verificar se o usuário existe no Supabase Auth
- Verificar se o email está correto
- Verificar logs da API para erros específicos

### Problema: Campo de senha não aparece
**Causa:** Usuário não é administrador
**Solução:**
- Verificar se o usuário logado tem nível "Administrador"
- Verificar função `isAdmin()` no hook `usePermissions`

### Problema: Exportação não funciona
**Causa:** Permissões ou erro na API
**Solução:**
- Verificar se usuário é administrador
- Verificar logs do servidor para erros específicos
- Verificar se as tabelas existem no banco

## 📋 **CHECKLIST DE VALIDAÇÃO**

### Alteração de Senha:
- [ ] Campo aparece apenas para administradores
- [ ] Campo só aparece ao editar (não ao criar)
- [ ] Validação de força da senha funciona
- [ ] Mensagem de sucesso é exibida
- [ ] Login com nova senha funciona
- [ ] Log de alteração é registrado

### Exportação de Tabelas:
- [ ] Botões aparecem apenas para administradores
- [ ] Exportação "Estrutura" gera arquivo SQL válido
- [ ] Exportação "Com Dados" inclui INSERTs
- [ ] Download do arquivo funciona
- [ ] Arquivo contém estrutura correta das tabelas
- [ ] Log de exportação é registrado

## 🔍 **LOGS PARA VERIFICAR**

### No Console do Navegador:
```javascript
// Verificar se não há erros de JavaScript
// Verificar se as requisições retornam 200
```

### No Terminal do Servidor:
```bash
# Verificar logs da API
# Procurar por erros de autenticação
# Verificar se as queries SQL estão corretas
```

### No Supabase:
```sql
-- Verificar se a senha foi atualizada
-- Verificar logs de atividade
SELECT * FROM log_atividades WHERE acao = 'alterar_senha';

-- Verificar usuários no Auth
-- (via dashboard do Supabase)
```

## ✨ **MELHORIAS FUTURAS**

1. **Notificação por Email:** Enviar email ao usuário quando senha for alterada
2. **Histórico de Senhas:** Impedir reutilização de senhas recentes
3. **Exportação Agendada:** Permitir agendamento de backups automáticos
4. **Compressão:** Comprimir arquivos de exportação grandes
5. **Seleção de Colunas:** Permitir escolher quais colunas exportar
