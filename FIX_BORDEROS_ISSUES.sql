-- FIX_BORDEROS_ISSUES.sql
-- <PERSON><PERSON>t to fix issues with borderos, secretarias, and direcionamentos

-- 1. Fix borderos status - ensure all imported borderos have "novo" status
UPDATE borderos
SET status = 'novo'
WHERE status IS NULL OR status = '';

-- 2. Fix secretarias valores_total - recalculate based on actual borderos
-- First, reset all valores_total to 0
UPDATE secretarias
SET valores_total = 0;

-- Then, update with the sum of borderos values for each secretaria
UPDATE secretarias s
SET valores_total = (
  SELECT COALESCE(SUM(valor), 0)
  FROM borderos b
  WHERE b.secretaria_id = s.id
);

-- 3. Fix direcionamentos for finance users (set max value to 30,000)
-- First, create a finance direcionamento if it doesn't exist
INSERT INTO direcionamentos (nome, valor_minimo, valor_maximo)
SELECT 'Financeiro', 0, 30000
WHERE NOT EXISTS (
  SELECT 1 FROM direcionamentos WHERE nome = 'Financeiro'
);

-- Get the ID of the finance direcionamento
DO $$
DECLARE
  finance_direcionamento_id UUID;
BEGIN
  SELECT id INTO finance_direcionamento_id FROM direcionamentos WHERE nome = 'Financeiro';
  
  -- Associate finance users with this direcionamento
  -- This assumes users with 'finance' role have that value in a 'role' column
  -- Adjust the query based on your actual user role implementation
  INSERT INTO direcionamento_usuarios (direcionamento_id, usuario_id)
  SELECT finance_direcionamento_id, u.id
  FROM usuarios u
  WHERE u.role = 'finance' -- Adjust this condition based on your schema
  AND NOT EXISTS (
    SELECT 1 FROM direcionamento_usuarios 
    WHERE direcionamento_id = finance_direcionamento_id AND usuario_id = u.id
  );
END $$;

-- 4. Fix duplicate borderos handling
-- Create a unique index on bordero_cod if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes WHERE indexname = 'idx_borderos_bordero_cod_unique'
  ) THEN
    CREATE UNIQUE INDEX idx_borderos_bordero_cod_unique ON borderos (bordero_cod);
  END IF;
END $$;

-- 5. Fix any NULL values in important fields
-- Corrigir valores nulos em campos importantes (separando cada UPDATE)
UPDATE borderos SET valor = 0 WHERE valor IS NULL;
UPDATE borderos SET data = CURRENT_DATE WHERE data IS NULL;
UPDATE borderos SET status = 'novo' WHERE status IS NULL;