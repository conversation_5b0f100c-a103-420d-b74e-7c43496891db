import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: usuario, error } = await supabase
      .from("usuarios")
      .select(`
        *,
        nivel_acesso:niveis_acesso(*)
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Erro ao buscar usuário:", error)
      return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 })
    }

    return NextResponse.json(usuario)
  } catch (error) {
    console.error("Erro ao buscar usuário:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { nome, email, nivel_acesso_id, nova_senha } = body

    if (!nome || !email || !nivel_acesso_id) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 })
    }

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar service key para operações administrativas
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o usuário atual é administrador (para alteração de senha)
    let isAdmin = false
    if (nova_senha) {
      console.log("Nova senha fornecida, verificando permissões do usuário:", session.user.id)

      const { data: currentUser, error: userError } = await supabaseAdmin
        .from("usuarios")
        .select(`
          nivel_acesso:niveis_acesso(nome)
        `)
        .eq("id", session.user.id)
        .single()

      if (userError) {
        console.error("Erro ao buscar usuário atual:", userError)
        return NextResponse.json({ error: "Erro ao verificar permissões" }, { status: 500 })
      }

      isAdmin = currentUser?.nivel_acesso?.nome === "Administrador"
      console.log("Usuário atual é admin:", isAdmin, "Nível:", currentUser?.nivel_acesso?.nome)

      if (!isAdmin) {
        return NextResponse.json({ error: "Apenas administradores podem alterar senhas de outros usuários" }, { status: 403 })
      }
    }

    // Verificar se o email já existe em outro usuário
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from("usuarios")
      .select("id")
      .eq("email", email)
      .neq("id", id)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar usuário existente:", checkError)
      return NextResponse.json({ error: "Erro ao verificar usuário" }, { status: 500 })
    }

    if (existingUser) {
      return NextResponse.json({ error: "Email já está em uso" }, { status: 400 })
    }

    // Atualizar usuário na tabela usuarios
    const { data: usuario, error: updateError } = await supabaseAdmin
      .from("usuarios")
      .update({
        nome,
        email,
        nivel_acesso_id,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (updateError) {
      console.error("Erro ao atualizar usuário:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar usuário" }, { status: 500 })
    }

    // Atualizar senha no Supabase Auth se fornecida e usuário for admin
    if (nova_senha && isAdmin) {
      try {
        console.log("Iniciando atualização de senha para email:", email)

        // Primeiro, buscar o usuário no Auth pelo email
        const { data: authUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers()

        if (listError) {
          console.error("Erro ao listar usuários do Auth:", listError)
          return NextResponse.json({
            ...usuario,
            warning: "Usuário atualizado, mas houve erro ao buscar dados de autenticação"
          })
        }

        console.log("Total de usuários no Auth:", authUsers.users.length)

        // Encontrar o usuário pelo email
        let authUser = authUsers.users.find(u => u.email === email)

        if (!authUser) {
          console.log("Usuário não encontrado no Auth, criando usuário:", email)

          // Criar usuário no Auth se não existir
          const { data: newAuthUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
            email: email,
            password: nova_senha,
            email_confirm: true
          })

          if (createError) {
            console.error("Erro ao criar usuário no Auth:", createError)
            return NextResponse.json({
              ...usuario,
              warning: "Usuário atualizado, mas houve erro ao criar conta de autenticação: " + createError.message
            })
          }

          authUser = newAuthUser.user
          console.log("Usuário criado no Auth com sucesso:", authUser.id, authUser.email)
        } else {
          console.log("Usuário encontrado no Auth:", authUser.id, authUser.email)

          // Atualizar a senha usando o ID do Auth
          const { error: passwordError } = await supabaseAdmin.auth.admin.updateUserById(
            authUser.id,
            { password: nova_senha }
          )

          if (passwordError) {
            console.error("Erro ao atualizar senha:", passwordError)
            return NextResponse.json({
              ...usuario,
              warning: "Usuário atualizado, mas houve erro ao alterar a senha: " + passwordError.message
            })
          }
        }

        console.log("Senha atualizada/criada com sucesso para usuário:", authUser.email)

        // Registrar log de alteração de senha
        try {
          await supabaseAdmin
            .from("log_atividades")
            .insert({
              usuario_id: session.user.id,
              acao: "alterar_senha",
              entidade: "usuario",
              entidade_id: id,
              detalhes: {
                usuario_alterado: nome,
                alterado_por_admin: true
              },
              ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
              user_agent: request.headers.get("user-agent") || "desconhecido"
            })
        } catch (logError) {
          console.error("Erro ao registrar log de alteração de senha:", logError)
        }

        // Retornar sucesso com mensagem de senha alterada
        return NextResponse.json({
          ...usuario,
          success: "Usuário e senha atualizados com sucesso"
        })

      } catch (authError) {
        console.error("Erro ao atualizar senha no Auth:", authError)
        return NextResponse.json({
          ...usuario,
          warning: "Usuário atualizado, mas houve erro ao alterar a senha: " + (authError as any).message
        })
      }
    }

    return NextResponse.json(usuario)
  } catch (error) {
    console.error("Erro ao atualizar usuário:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar service key para operações administrativas
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o usuário existe
    const { data: usuario, error: fetchError } = await supabaseAdmin
      .from("usuarios")
      .select("id, nome")
      .eq("id", id)
      .single()

    if (fetchError || !usuario) {
      return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 })
    }

    // Excluir usuário
    const { error: deleteError } = await supabaseAdmin
      .from("usuarios")
      .delete()
      .eq("id", id)

    if (deleteError) {
      console.error("Erro ao excluir usuário:", deleteError)
      return NextResponse.json({ error: "Erro ao excluir usuário" }, { status: 500 })
    }

    // Registrar log de exclusão
    try {
      await supabaseAdmin
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "excluir",
          entidade: "usuario",
          entidade_id: id,
          detalhes: {
            nome: usuario.nome,
            usuario_excluido: usuario.nome
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exclusão:", logError)
    }

    return NextResponse.json({ message: "Usuário excluído com sucesso" })
  } catch (error) {
    console.error("Erro ao excluir usuário:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
