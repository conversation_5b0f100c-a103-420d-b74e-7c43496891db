import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()
    const { nome, descricao } = body

    if (!nome) {
      return NextResponse.json({ error: "Nome é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: nivel, error } = await supabase
      .from("niveis_acesso")
      .update({
        nome,
        descricao: descricao || null
      })
      .eq("id", params.id)
      .select()
      .single()

    if (error) {
      console.error("Erro ao atualizar nível de acesso:", error)
      return NextResponse.json({ error: "Erro ao atualizar nível de acesso" }, { status: 500 })
    }

    return NextResponse.json(nivel)
  } catch (error) {
    console.error("Erro ao atualizar nível de acesso:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se há usuários usando este nível de acesso
    const { data: usuarios, error: checkError } = await supabase
      .from("usuarios")
      .select("id")
      .eq("nivel_acesso_id", id)
      .limit(1)

    if (checkError) {
      console.error("Erro ao verificar usuários:", checkError)
      return NextResponse.json({ error: "Erro ao verificar usuários" }, { status: 500 })
    }

    if (usuarios && usuarios.length > 0) {
      return NextResponse.json({ error: "Não é possível excluir um nível de acesso que está sendo usado por usuários" }, { status: 400 })
    }

    const { error } = await supabase
      .from("niveis_acesso")
      .delete()
      .eq("id", id)

    if (error) {
      console.error("Erro ao excluir nível de acesso:", error)
      return NextResponse.json({ error: "Erro ao excluir nível de acesso" }, { status: 500 })
    }

    return NextResponse.json({ message: "Nível de acesso excluído com sucesso" })
  } catch (error) {
    console.error("Erro ao excluir nível de acesso:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
