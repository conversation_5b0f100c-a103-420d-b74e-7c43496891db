-- FIX_PERMISSION_ERROR.sql
-- <PERSON><PERSON><PERSON> para corrigir o erro de permissão na atualização das materialized views

-- 1. Remover o trigger existente que está causando o erro
DROP TRIGGER IF EXISTS trg_refresh_dashboard_views ON borderos;

-- 2. Verificar se a tabela de fila existe, se não, criá-la
CREATE TABLE IF NOT EXISTS dashboard_refresh_queue (
  id SERIAL PRIMARY KEY,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMP WITH TIME ZONE
);

-- 3. <PERSON>riar um novo trigger que apenas insere na fila em vez de tentar atualizar as views diretamente
CREATE OR REPLACE FUNCTION queue_dashboard_refresh()
RETURNS TRIGGER AS $$
BEGIN
  -- Apenas insere uma solicitação na fila de atualização
  INSERT INTO dashboard_refresh_queue (requested_at) 
  VALUES (CURRENT_TIMESTAMP);
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. <PERSON><PERSON>r o novo trigger
CREATE TRIGGER trg_queue_dashboard_refresh
AFTER INSERT OR UPDATE OR DELETE ON borderos
FOR EACH STATEMENT
EXECUTE FUNCTION queue_dashboard_refresh();

-- 5. Conceder permissões na tabela de fila para todos os usuários
GRANT INSERT, SELECT ON dashboard_refresh_queue TO PUBLIC;
GRANT USAGE, SELECT ON SEQUENCE dashboard_refresh_queue_id_seq TO PUBLIC;

-- 6. Criar uma função para processar a fila (deve ser executada por um usuário com permissões adequadas)
CREATE OR REPLACE FUNCTION process_dashboard_refresh_queue()
RETURNS INTEGER AS $$
DECLARE
  processed_count INTEGER := 0;
BEGIN
  -- Marcar todas as solicitações não processadas como processadas
  UPDATE dashboard_refresh_queue
  SET processed = TRUE, processed_at = CURRENT_TIMESTAMP
  WHERE processed = FALSE;
  
  -- Obter a contagem de solicitações processadas
  GET DIAGNOSTICS processed_count = ROW_COUNT;
  
  -- Se houver solicitações para processar, atualizar as views
  IF processed_count > 0 THEN
    BEGIN
      -- Tentar atualizar concorrentemente
      REFRESH MATERIALIZED VIEW CONCURRENTLY mv_bordero_counts_by_status;
      REFRESH MATERIALIZED VIEW CONCURRENTLY mv_secretaria_values;
    EXCEPTION WHEN OTHERS THEN
      -- Se falhar, usar o método normal (não concorrente)
      RAISE NOTICE 'Falha ao atualizar views concorrentemente, usando método normal';
      REFRESH MATERIALIZED VIEW mv_bordero_counts_by_status;
      REFRESH MATERIALIZED VIEW mv_secretaria_values;
    END;
  END IF;
  
  RETURN processed_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Criar um índice na coluna processed para consultas eficientes
CREATE INDEX IF NOT EXISTS idx_dashboard_refresh_queue_processed ON dashboard_refresh_queue (processed);

-- 8. Limpar a fila existente para evitar processamento desnecessário
DELETE FROM dashboard_refresh_queue WHERE processed = FALSE;