// Script para limpar cache e testar performance
console.log("🧹 Limpando cache do sistema...")

// Limpar localStorage
if (typeof window !== 'undefined') {
  localStorage.clear()
  sessionStorage.clear()
  console.log("✅ Cache do navegador limpo")
}

// Função para medir tempo de carregamento
function measureLoadTime(label, fn) {
  const start = performance.now()
  return fn().finally(() => {
    const end = performance.now()
    console.log(`⏱️ ${label}: ${(end - start).toFixed(2)}ms`)
  })
}

// Exportar para uso global
if (typeof window !== 'undefined') {
  window.measureLoadTime = measureLoadTime
  window.clearAppCache = () => {
    localStorage.clear()
    sessionStorage.clear()
    location.reload()
  }
}
