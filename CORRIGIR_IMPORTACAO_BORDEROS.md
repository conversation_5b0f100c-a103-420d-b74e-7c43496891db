# 🔧 Como Corrigir Problemas de Importação de Borderos

## 🚨 Problema Identificado

Você está enfrentando **665 falhas** na importação de borderos. Este guia vai ajudar a identificar e corrigir o problema.

## 🔍 Diagnóstico Rápido

### 1. **Acesse a Página de Diagnóstico**
- Vá para: `/dashboard/borderos/diagnostico`
- Clique em "Executar Diagnóstico"
- Verifique os resultados

### 2. **Principais Causas Possíveis**

#### ❌ **Estrutura da Tabela Borderos**
- Tabela não existe ou está mal configurada
- Colunas obrigatórias ausentes
- Políticas RLS (Row Level Security) incorretas

#### ❌ **Dados de Referência Ausentes**
- Nenhuma secretaria cadastrada
- Nenhum tipo de bordero cadastrado
- Usuários não configurados

#### ❌ **Problemas de Validação**
- Dados da planilha em formato incorreto
- Campos obrigatórios vazios
- Valores inválidos

## 🛠️ Soluções Passo a Passo

### **Solução 1: Corrigir Estrutura do Banco**

1. **Execute o Script de Correção**
   ```sql
   -- Copie e cole no Supabase SQL Editor
   -- Arquivo: scripts/fix-borderos-import.sql
   ```

2. **Ou execute manualmente:**
   ```sql
   -- Verificar se a tabela existe
   SELECT EXISTS (
       SELECT FROM information_schema.tables 
       WHERE table_name = 'borderos'
   );
   
   -- Se não existir, criar a tabela
   CREATE TABLE IF NOT EXISTS borderos (
       id SERIAL PRIMARY KEY,
       bordero_cod VARCHAR(255) UNIQUE NOT NULL,
       valor DECIMAL(15,2) NOT NULL,
       data TIMESTAMP WITH TIME ZONE NOT NULL,
       nome_empresa VARCHAR(255) NOT NULL,
       secretaria_id INTEGER REFERENCES secretarias(id),
       tipo_id INTEGER REFERENCES tipos(id),
       observacao TEXT,
       status VARCHAR(50) DEFAULT 'novo',
       dados_status TEXT,
       data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       responsavel_id UUID REFERENCES usuarios(id),
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   
   -- Habilitar RLS e criar política
   ALTER TABLE borderos ENABLE ROW LEVEL SECURITY;
   CREATE POLICY "borderos_full_access" ON borderos
   FOR ALL TO authenticated USING (true) WITH CHECK (true);
   ```

### **Solução 2: Cadastrar Dados Básicos**

1. **Cadastrar pelo menos uma Secretaria:**
   - Vá para: `/dashboard/secretarias`
   - Clique em "Nova Secretaria"
   - Preencha: Nome = "Secretaria Padrão", Slug = "secretaria-padrao"

2. **Cadastrar pelo menos um Tipo:**
   - Vá para: `/dashboard/configuracoes` → Aba "Tipos"
   - Clique em "Novo Tipo"
   - Preencha: Nome = "Tipo Padrão", Descrição = "Tipo padrão para borderos"

### **Solução 3: Verificar Formato da Planilha**

1. **Estrutura Recomendada da Planilha:**
   ```
   | Codigo     | Empresa        | Valor    | Data       | Secretaria | Tipo    | Observacao |
   |------------|----------------|----------|------------|------------|---------|------------|
   | BOR-001    | Empresa ABC    | 1500.50  | 2025-01-15 | Secretaria | Tipo    | Obs...     |
   | BOR-002    | Empresa XYZ    | 2300.00  | 2025-01-16 | Secretaria | Tipo    | Obs...     |
   ```

2. **Campos Obrigatórios:**
   - ✅ **Codigo** (único, não pode repetir)
   - ✅ **Empresa** (nome da empresa)
   - ✅ **Valor** (número positivo)

3. **Campos Opcionais:**
   - 📅 Data (se não informada, usa data atual)
   - 🏛️ Secretaria (se não informada, fica vazio)
   - 📋 Tipo (se não informado, fica vazio)
   - 📝 Observacao (comentários)

## 🔧 Script de Correção Automática

Execute este SQL no **Supabase SQL Editor**:

```sql
-- =====================================================
-- CORREÇÃO AUTOMÁTICA DE PROBLEMAS DE IMPORTAÇÃO
-- =====================================================

-- 1. Verificar e criar tabela borderos se necessário
CREATE TABLE IF NOT EXISTS borderos (
    id SERIAL PRIMARY KEY,
    bordero_cod VARCHAR(255) UNIQUE NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data TIMESTAMP WITH TIME ZONE NOT NULL,
    nome_empresa VARCHAR(255) NOT NULL,
    secretaria_id INTEGER REFERENCES secretarias(id),
    tipo_id INTEGER REFERENCES tipos(id),
    observacao TEXT,
    status VARCHAR(50) DEFAULT 'novo',
    dados_status TEXT,
    data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responsavel_id UUID REFERENCES usuarios(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Corrigir RLS
ALTER TABLE borderos ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "borderos_full_access" ON borderos;
CREATE POLICY "borderos_full_access" ON borderos
FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- 3. Inserir dados básicos se não existirem
INSERT INTO secretarias (nome, slug, valores_total)
SELECT 'Secretaria Padrão', 'secretaria-padrao', 0
WHERE NOT EXISTS (SELECT 1 FROM secretarias);

INSERT INTO tipos (nome, descricao)
SELECT 'Tipo Padrão', 'Tipo padrão para borderos'
WHERE NOT EXISTS (SELECT 1 FROM tipos);

-- 4. Verificar resultado
SELECT 'Correção concluída - Sistema pronto para importação' as status;
```

## 📊 Teste de Importação

Após aplicar as correções:

1. **Crie uma planilha de teste:**
   ```
   Codigo,Empresa,Valor
   TEST-001,Empresa Teste,1000.50
   TEST-002,Outra Empresa,2500.00
   ```

2. **Faça o upload da planilha**
3. **Mapeie os campos corretamente**
4. **Execute a importação**

## 🆘 Se o Problema Persistir

1. **Verifique os logs do navegador:**
   - Pressione F12
   - Vá para a aba "Console"
   - Procure por erros em vermelho

2. **Verifique os logs do servidor:**
   - Os logs detalhados agora aparecem no console
   - Procure por mensagens com ❌ ou 💥

3. **Execute o diagnóstico completo:**
   - Acesse `/dashboard/borderos/diagnostico`
   - Clique em "Executar Diagnóstico"
   - Verifique todos os itens

## 📞 Suporte Adicional

Se ainda houver problemas:

1. **Capture os logs de erro**
2. **Tire um print da tela de diagnóstico**
3. **Anote exatamente qual erro aparece**
4. **Verifique se todas as tabelas existem no Supabase**

---

**✅ Após seguir este guia, a importação deve funcionar corretamente!**
