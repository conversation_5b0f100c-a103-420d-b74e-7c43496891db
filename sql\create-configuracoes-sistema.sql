-- Criar tabela configuracoes_sistema
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
  modo_escuro BOOLEAN DEFAULT false,
  notificacoes_email BOOLEAN DEFAULT true,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserir configuração padrão se não existir
INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
SELECT 'CRM de Bordero', false, true
WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);

-- Habilitar RLS (Row Level Security)
ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir acesso total (ajustar conforme necessário)
CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
FOR ALL USING (true);

-- Verificar se a tabela foi criada corretamente
SELECT * FROM configuracoes_sistema;
