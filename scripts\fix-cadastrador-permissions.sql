-- <PERSON><PERSON>t para corrigir as permissões do nível "Cadastrador"
-- Execute este script no Supabase SQL Editor

-- 1. Verificar níveis de acesso existentes
SELECT id, nome, permissoes FROM niveis_acesso ORDER BY nome;

-- 2. Verificar usuários e seus níveis
SELECT 
  u.id,
  u.nome,
  u.email,
  na.nome as nivel_acesso,
  na.permissoes
FROM usuarios u
LEFT JOIN niveis_acesso na ON u.nivel_acesso_id = na.id
ORDER BY u.nome;

-- 3. Atualizar permissões do nível "Cadastrador"
UPDATE niveis_acesso 
SET permissoes = '{
  "dashboard": true,
  "borderos": true,
  "secretarias": false,
  "direcionamentos": false,
  "tipos": false,
  "usuarios": false,
  "relatorios": false,
  "configuracoes": false
}'::jsonb
WHERE nome = 'Cadastrador';

-- 4. Verificar se a atualização foi aplicada
SELECT id, nome, permissoes FROM niveis_acesso WHERE nome = 'Cadastrador';

-- 5. Verificar usuários com nível "Cadastrador"
SELECT 
  u.id,
  u.nome,
  u.email,
  na.nome as nivel_acesso,
  na.permissoes
FROM usuarios u
JOIN niveis_acesso na ON u.nivel_acesso_id = na.id
WHERE na.nome = 'Cadastrador';
