"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { BorderoTable } from "@/components/dashboard/bordero-table"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function BorderosCanceladosPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/borderos">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Borderos Cancelados</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Borderos Cancelados</CardTitle>
        </CardHeader>
        <CardContent>
          <BorderoTable status="cancelado" />
        </CardContent>
      </Card>
    </div>
  )
}
