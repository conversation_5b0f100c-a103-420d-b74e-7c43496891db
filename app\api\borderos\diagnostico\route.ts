import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET() {
  try {
    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    console.log("🩺 Iniciando diagnóstico do sistema de importação...")

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // 1. Verificar estrutura do banco
    console.log("🏗️ Verificando estrutura do banco...")
    const estrutura = []

    // Verificar tabela borderos
    const { data: borderos, error: borderosError } = await supabase
      .from("borderos")
      .select("id")
      .limit(1)

    if (borderosError) {
      estrutura.push({
        nome: "Tabela borderos",
        status: "erro",
        detalhes: `Erro: ${borderosError.message}`
      })
    } else {
      estrutura.push({
        nome: "Tabela borderos",
        status: "ok",
        detalhes: "Tabela existe e acessível"
      })
    }

    // Verificar políticas RLS
    try {
      const { data: testRLS, error: rlsError } = await supabase
        .from("borderos")
        .select("count")
        .limit(1)

      if (rlsError) {
        estrutura.push({
          nome: "Políticas RLS",
          status: "erro",
          detalhes: `Erro de permissão: ${rlsError.message}`
        })
      } else {
        estrutura.push({
          nome: "Políticas RLS",
          status: "ok",
          detalhes: "Políticas configuradas corretamente"
        })
      }
    } catch (error: any) {
      estrutura.push({
        nome: "Políticas RLS",
        status: "erro",
        detalhes: `Erro: ${error.message}`
      })
    }

    // 2. Verificar dados de referência
    console.log("📋 Verificando dados de referência...")
    const dados = []

    // Verificar secretarias
    const { data: secretarias, error: secretariasError } = await supabase
      .from("secretarias")
      .select("id, nome")

    if (secretariasError) {
      dados.push({
        nome: "Secretarias",
        status: "erro",
        detalhes: `Erro: ${secretariasError.message}`
      })
    } else if (secretarias.length === 0) {
      dados.push({
        nome: "Secretarias",
        status: "aviso",
        detalhes: "Nenhuma secretaria cadastrada"
      })
    } else {
      dados.push({
        nome: "Secretarias",
        status: "ok",
        detalhes: `${secretarias.length} secretarias cadastradas`
      })
    }

    // Verificar tipos
    const { data: tipos, error: tiposError } = await supabase
      .from("tipos")
      .select("id, nome")

    if (tiposError) {
      dados.push({
        nome: "Tipos de Bordero",
        status: "erro",
        detalhes: `Erro: ${tiposError.message}`
      })
    } else if (tipos.length === 0) {
      dados.push({
        nome: "Tipos de Bordero",
        status: "aviso",
        detalhes: "Nenhum tipo cadastrado"
      })
    } else {
      dados.push({
        nome: "Tipos de Bordero",
        status: "ok",
        detalhes: `${tipos.length} tipos cadastrados`
      })
    }

    // Verificar usuários
    const { data: usuarios, error: usuariosError } = await supabase
      .from("usuarios")
      .select("id, nome")

    if (usuariosError) {
      dados.push({
        nome: "Usuários",
        status: "erro",
        detalhes: `Erro: ${usuariosError.message}`
      })
    } else if (usuarios.length < 2) {
      dados.push({
        nome: "Usuários",
        status: "aviso",
        detalhes: `Apenas ${usuarios.length} usuário(s) cadastrado(s)`
      })
    } else {
      dados.push({
        nome: "Usuários",
        status: "ok",
        detalhes: `${usuarios.length} usuários cadastrados`
      })
    }

    // 3. Teste de importação
    console.log("🧪 Executando teste de importação...")
    const teste = []

    try {
      // Teste de inserção de bordero com data atual
      const testBordero = {
        bordero_cod: 'TEST-FINAL-' + Date.now(),
        nome_empresa: 'Empresa Teste Final',
        valor: 1000.00,
        data: new Date().toISOString(),
        status: 'novo',
        secretaria_id: secretarias && secretarias.length > 0 ? secretarias[0].id : null,
        tipo_id: tipos && tipos.length > 0 ? tipos[0].id : null,
        observacao: 'Teste de importação automático',
        responsavel_id: session.user.id
      }

      const { data: novoBordero, error: insertError } = await supabase
        .from("borderos")
        .insert(testBordero)
        .select()
        .single()

      if (insertError) {
        teste.push({
          nome: "Inserção de bordero",
          status: "erro",
          detalhes: `Erro: ${insertError.message}`
        })
      } else {
        teste.push({
          nome: "Inserção de bordero",
          status: "ok",
          detalhes: `Bordero teste criado com ID ${novoBordero.id}`
        })

        // Limpar teste
        await supabase
          .from("borderos")
          .delete()
          .eq("id", novoBordero.id)
      }

      // Teste específico de data do Excel
      const testDateBordero = {
        bordero_cod: 'TEST-DATE-' + Date.now(),
        nome_empresa: 'Teste Data Excel',
        valor: 500.00,
        data: '2024-01-15T00:00:00.000Z', // Data fixa para teste
        status: 'novo',
        secretaria_id: secretarias && secretarias.length > 0 ? secretarias[0].id : null,
        tipo_id: tipos && tipos.length > 0 ? tipos[0].id : null,
        observacao: 'Teste de data do Excel',
        responsavel_id: session.user.id
      }

      const { data: dateTestBordero, error: dateError } = await supabase
        .from("borderos")
        .insert(testDateBordero)
        .select()
        .single()

      if (dateError) {
        teste.push({
          nome: "Teste de data",
          status: "erro",
          detalhes: `Erro na data: ${dateError.message}`
        })
      } else {
        teste.push({
          nome: "Teste de data",
          status: "ok",
          detalhes: "Processamento de datas funcionando"
        })

        // Limpar teste
        await supabase
          .from("borderos")
          .delete()
          .eq("id", dateTestBordero.id)
      }
    } catch (error: any) {
      teste.push({
        nome: "Teste de importação",
        status: "erro",
        detalhes: `Erro inesperado: ${error.message}`
      })
    }

    // 4. Gerar soluções baseadas nos problemas encontrados
    const solucoes = []

    // Verificar se há problemas e sugerir soluções
    const temErros = [...estrutura, ...dados, ...teste].some(item => item.status === 'erro')
    const temAvisos = [...estrutura, ...dados, ...teste].some(item => item.status === 'aviso')

    if (estrutura.some(item => item.nome === 'Tabela borderos' && item.status === 'erro')) {
      solucoes.push({
        problema: "Tabela borderos não encontrada",
        solucao: "Execute o script SQL de criação da tabela no Supabase SQL Editor"
      })
    }

    if (estrutura.some(item => item.nome === 'Políticas RLS' && item.status === 'erro')) {
      solucoes.push({
        problema: "Erro de permissão RLS",
        solucao: "Configure as políticas RLS para permitir acesso aos usuários autenticados"
      })
    }

    if (dados.some(item => item.nome === 'Secretarias' && item.status === 'aviso')) {
      solucoes.push({
        problema: "Poucas secretarias cadastradas",
        solucao: "Cadastre secretarias em /dashboard/secretarias para melhor organização"
      })
    }

    if (dados.some(item => item.nome === 'Tipos de Bordero' && item.status === 'aviso')) {
      solucoes.push({
        problema: "Poucos tipos cadastrados",
        solucao: "Cadastre tipos de bordero em /dashboard/tipos para categorização"
      })
    }

    if (teste.some(item => item.status === 'erro')) {
      solucoes.push({
        problema: "Falha no teste de importação",
        solucao: "Verifique as configurações do banco e execute o script de correção"
      })
    }

    console.log("✅ Diagnóstico concluído")

    return NextResponse.json({
      estrutura,
      dados,
      teste,
      solucoes,
      resumo: {
        total_verificacoes: estrutura.length + dados.length + teste.length,
        erros: [...estrutura, ...dados, ...teste].filter(item => item.status === 'erro').length,
        avisos: [...estrutura, ...dados, ...teste].filter(item => item.status === 'aviso').length,
        ok: [...estrutura, ...dados, ...teste].filter(item => item.status === 'ok').length
      }
    })
  } catch (error: any) {
    console.error("💥 Erro crítico no diagnóstico:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error.message
    }, { status: 500 })
  }
}
