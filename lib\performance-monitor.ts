"use client"

// Monitor de performance para identificar gargalos
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  startTimer(label: string): () => void {
    const start = performance.now()
    
    return () => {
      const end = performance.now()
      const duration = end - start
      
      if (!this.metrics.has(label)) {
        this.metrics.set(label, [])
      }
      
      this.metrics.get(label)!.push(duration)
      
      // Log se demorar mais que 2 segundos
      if (duration > 2000) {
        console.warn(`🐌 Operação lenta detectada: ${label} - ${duration.toFixed(2)}ms`)
      }
      
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`)
    }
  }

  getAverageTime(label: string): number {
    const times = this.metrics.get(label)
    if (!times || times.length === 0) return 0
    
    return times.reduce((sum, time) => sum + time, 0) / times.length
  }

  getReport(): Record<string, { avg: number, count: number, max: number }> {
    const report: Record<string, { avg: number, count: number, max: number }> = {}
    
    for (const [label, times] of this.metrics.entries()) {
      if (times.length > 0) {
        report[label] = {
          avg: this.getAverageTime(label),
          count: times.length,
          max: Math.max(...times)
        }
      }
    }
    
    return report
  }

  logReport(): void {
    const report = this.getReport()
    console.table(report)
  }

  clear(): void {
    this.metrics.clear()
  }
}

// Hook para usar o monitor
export function usePerformanceMonitor() {
  const monitor = PerformanceMonitor.getInstance()
  
  return {
    startTimer: monitor.startTimer.bind(monitor),
    getReport: monitor.getReport.bind(monitor),
    logReport: monitor.logReport.bind(monitor),
    clear: monitor.clear.bind(monitor)
  }
}

// Função para medir tempo de execução de promises
export async function measureAsync<T>(
  label: string, 
  promise: Promise<T>
): Promise<T> {
  const monitor = PerformanceMonitor.getInstance()
  const stopTimer = monitor.startTimer(label)
  
  try {
    const result = await promise
    return result
  } finally {
    stopTimer()
  }
}

// Função para medir tempo de execução de funções síncronas
export function measureSync<T>(
  label: string, 
  fn: () => T
): T {
  const monitor = PerformanceMonitor.getInstance()
  const stopTimer = monitor.startTimer(label)
  
  try {
    return fn()
  } finally {
    stopTimer()
  }
}
