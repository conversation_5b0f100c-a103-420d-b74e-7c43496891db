// Correção para o botão "Ações" no componente BorderoTableWithPagination
// Este arquivo contém as alterações necessárias para implementar o menu de ações

// 1. Primeiro, precisamos adicionar as importações necessárias no topo do arquivo
// Adicionar após a linha 7 em components/dashboard/bordero-table-with-pagination.tsx:
/*
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, Edit, CheckCircle, AlertCircle, Archive } from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
*/

// 2. Adicionar estados e funções necessárias para o componente
// Adicionar após a linha 27 em components/dashboard/bordero-table-with-pagination.tsx:
/*
  const router = useRouter()
  const notify = useNotify()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogType, setDialogType] = useState<"visualizar" | "correcao">("visualizar")
  const [selectedBordero, setSelectedBordero] = useState<any | null>(null)
  const [correcaoMotivo, setCorrecaoMotivo] = useState("")
*/

// 3. Adicionar a função para atualizar o status do bordero
// Adicionar após a função fetchBorderos em components/dashboard/bordero-table-with-pagination.tsx:
/*
  // Função para atualizar o status do bordero
  const updateBorderoStatus = async (id: number, newStatus: string, dadosStatus?: string) => {
    try {
      const response = await fetch(`/api/borderos/${id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          dadosStatus,
          usuarioId: 1, // Simulando usuário logado
        }),
      })

      if (!response.ok) {
        throw new Error("Erro ao atualizar status")
      }

      // Mapear status para mensagens mais amigáveis
      const statusMessages: { [key: string]: string } = {
        analise: "Em Análise",
        assinado: "Assinado",
        pago: "Pago",
        arquivado: "Arquivado",
        corrigir: "Correção Solicitada"
      }

      const statusMessage = statusMessages[newStatus] || newStatus

      notify.success(
        "Status Atualizado",
        `Bordero ${borderos.find(b => b.id === id)?.bordero_cod} foi marcado como ${statusMessage}.`
      )

      // Atualizar a lista de borderos
      fetchBorderos()

      // Fechar o diálogo se estiver aberto
      setDialogOpen(false)
      setCorrecaoMotivo("")
    } catch (error) {
      console.error("Erro ao atualizar status:", error)
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status do bordero.",
        variant: "destructive",
      })
    }
  }

  const handleSolicitarCorrecao = () => {
    if (!selectedBordero) return

    if (!correcaoMotivo.trim()) {
      toast({
        title: "Erro",
        description: "Informe o motivo da correção.",
        variant: "destructive",
      })
      return
    }

    updateBorderoStatus(selectedBordero.id, "corrigir", correcaoMotivo)
  }

  // Função para renderizar o badge de status
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "novo":
        return (
          <Badge className="bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800">
            Novo
          </Badge>
        )
      case "analise":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">
            Em Análise
          </Badge>
        )
      case "assinado":
        return (
          <Badge className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
            Assinado
          </Badge>
        )
      case "pago":
        return (
          <Badge className="bg-purple-50 text-purple-600 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800">
            Pago
          </Badge>
        )
      case "corrigir":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">
            Corrigir
          </Badge>
        )
      case "cancelado":
        return (
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Cancelado
          </Badge>
        )
      case "excluido":
        return (
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Excluído
          </Badge>
        )
      case "arquivado":
        return (
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Arquivado
          </Badge>
        )
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }
*/

// 4. Substituir o botão "Ações" por um DropdownMenu
// Substituir as linhas 249-254 em components/dashboard/bordero-table-with-pagination.tsx:
/*
<td className="py-3 px-4 text-right">
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost" size="sm">
        <MoreHorizontal className="h-4 w-4" />
        <span className="sr-only">Abrir menu</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuLabel>Ações</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem onClick={() => {
        router.push(`/dashboard/borderos/${bordero.id}`)
      }}>
        <Eye className="mr-2 h-4 w-4" />
        <span>Visualizar</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => {
        router.push(`/dashboard/borderos/${bordero.id}/editar`)
      }}>
        <Edit className="mr-2 h-4 w-4" />
        <span>Editar</span>
      </DropdownMenuItem>
      {bordero.status === "novo" && (
        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>
          <CheckCircle className="mr-2 h-4 w-4" />
          <span>Marcar em Análise</span>
        </DropdownMenuItem>
      )}
      {bordero.status === "analise" && (
        <>
          <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>
            <CheckCircle className="mr-2 h-4 w-4" />
            <span>Aprovar</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              setSelectedBordero(bordero)
              setDialogType("correcao")
              setDialogOpen(true)
            }}
          >
            <AlertCircle className="mr-2 h-4 w-4" />
            <span>Solicitar Correção</span>
          </DropdownMenuItem>
        </>
      )}
      {bordero.status === "assinado" && (
        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>
          <CheckCircle className="mr-2 h-4 w-4" />
          <span>Marcar como Pago</span>
        </DropdownMenuItem>
      )}
      {bordero.status === "pago" && (
        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>
          <Archive className="mr-2 h-4 w-4" />
          <span>Arquivar</span>
        </DropdownMenuItem>
      )}
    </DropdownMenuContent>
  </DropdownMenu>
</td>
*/

// 5. Também substituir o botão de ações no layout mobile
// Substituir as linhas 284-286 em components/dashboard/bordero-table-with-pagination.tsx:
/*
<button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0">
  •••
</button>
*/

// Por:
/*
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost" size="sm">
      <MoreHorizontal className="h-4 w-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuLabel>Ações</DropdownMenuLabel>
    <DropdownMenuSeparator />
    <DropdownMenuItem onClick={() => {
      router.push(`/dashboard/borderos/${bordero.id}`)
    }}>
      <Eye className="mr-2 h-4 w-4" />
      <span>Visualizar</span>
    </DropdownMenuItem>
    <DropdownMenuItem onClick={() => {
      router.push(`/dashboard/borderos/${bordero.id}/editar`)
    }}>
      <Edit className="mr-2 h-4 w-4" />
      <span>Editar</span>
    </DropdownMenuItem>
    {bordero.status === "novo" && (
      <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>
        <CheckCircle className="mr-2 h-4 w-4" />
        <span>Marcar em Análise</span>
      </DropdownMenuItem>
    )}
    {bordero.status === "analise" && (
      <>
        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>
          <CheckCircle className="mr-2 h-4 w-4" />
          <span>Aprovar</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            setSelectedBordero(bordero)
            setDialogType("correcao")
            setDialogOpen(true)
          }}
        >
          <AlertCircle className="mr-2 h-4 w-4" />
          <span>Solicitar Correção</span>
        </DropdownMenuItem>
      </>
    )}
    {bordero.status === "assinado" && (
      <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>
        <CheckCircle className="mr-2 h-4 w-4" />
        <span>Marcar como Pago</span>
      </DropdownMenuItem>
    )}
    {bordero.status === "pago" && (
      <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>
        <Archive className="mr-2 h-4 w-4" />
        <span>Arquivar</span>
      </DropdownMenuItem>
    )}
  </DropdownMenuContent>
</DropdownMenu>
*/

// 6. Adicionar o componente de diálogo no final do componente
// Adicionar antes do último </div> em components/dashboard/bordero-table-with-pagination.tsx:
/*
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {dialogType === "visualizar" ? "Detalhes do Bordero" : "Solicitar Correção"}
            </DialogTitle>
            <DialogDescription>
              {dialogType === "visualizar"
                ? `Informações detalhadas do bordero ${selectedBordero?.bordero_cod}`
                : `Informe o motivo da correção para o bordero ${selectedBordero?.bordero_cod}.`
              }
            </DialogDescription>
          </DialogHeader>

          {dialogType === "visualizar" ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Código</label>
                  <p className="text-sm">{selectedBordero?.bordero_cod}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <p className="text-sm">{renderStatusBadge(selectedBordero?.status || "")}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Empresa</label>
                  <p className="text-sm">{selectedBordero?.nome_empresa}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Valor</label>
                  <p className="text-sm">
                    {selectedBordero?.valor && new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL"
                    }).format(selectedBordero.valor)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Data</label>
                  <p className="text-sm">
                    {selectedBordero?.data && new Date(selectedBordero.data).toLocaleDateString("pt-BR")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Secretaria</label>
                  <p className="text-sm">{selectedBordero?.secretaria?.nome}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tipo</label>
                  <p className="text-sm">{selectedBordero?.tipo?.nome}</p>
                </div>
              </div>
            </div>
          ) : (
            <Textarea
              placeholder="Descreva o motivo da correção..."
              value={correcaoMotivo}
              onChange={(e) => setCorrecaoMotivo(e.target.value)}
              className="min-h-[100px]"
            />
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              {dialogType === "visualizar" ? "Fechar" : "Cancelar"}
            </Button>
            {dialogType === "correcao" && (
              <Button onClick={handleSolicitarCorrecao}>Enviar Solicitação</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
*/