import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs"

// Cache simples para reduzir chamadas ao Supabase
const sessionCache = new Map<string, { session: any; timestamp: number }>()
const CACHE_DURATION = 30000 // 30 segundos

// Mapeamento de rotas para permissões necessárias
const ROUTE_PERMISSIONS: Record<string, string> = {
  "/dashboard": "dashboard",
  "/dashboard/borderos": "borderos",
  "/dashboard/meus-borderos": "borderos",
  "/dashboard/borderos/excluidos": "borderos",
  "/dashboard/secretarias": "secretarias",
  "/dashboard/direcionamentos": "direcionamentos",
  "/dashboard/tipos": "tipos",
  "/dashboard/usuarios": "usuarios",
  "/dashboard/logs": "configuracoes",
  "/dashboard/relatorios": "relatorios",
  "/dashboard/configuracoes": "configuracoes",
}

export async function middleware(req: NextRequest) {
  // Criar uma resposta que podemos modificar
  const res = NextResponse.next()

  // Apenas verificar autenticação básica - sem verificação de permissões por enquanto
  if (req.nextUrl.pathname.startsWith("/dashboard")) {
    try {
      // Verificar se temos uma sessão em cache
      const authToken = req.cookies.get('sb-access-token')?.value ||
                       req.cookies.get('sb-gegollhdovoqgyxomdwv-auth-token')?.value ||
                       'anonymous'

      const cached = sessionCache.get(authToken)
      const now = Date.now()

      // Se temos cache válido, usar ele
      if (cached && (now - cached.timestamp) < CACHE_DURATION) {
        if (!cached.session) {
          const redirectUrl = req.nextUrl.clone()
          redirectUrl.pathname = "/"
          redirectUrl.searchParams.set("redirectedFrom", req.nextUrl.pathname)
          return NextResponse.redirect(redirectUrl)
        }
        return res
      }

      // Criar o cliente Supabase para o middleware
      const supabase = createMiddlewareClient({ req, res })

      // Verificar se o usuário tem uma sessão válida
      const {
        data: { session },
        error: sessionError
      } = await supabase.auth.getSession()

      // Armazenar no cache
      sessionCache.set(authToken, { session, timestamp: now })

      if (sessionError) {
        console.error("Erro ao obter sessão no middleware:", sessionError)
        // Em caso de erro de rate limit, permitir acesso temporariamente
        if (sessionError.message?.includes('rate limit')) {
          return res
        }
        return res
      }

      // Se o usuário não estiver autenticado, redirecionar para login
      if (!session) {
        const redirectUrl = req.nextUrl.clone()
        redirectUrl.pathname = "/"
        redirectUrl.searchParams.set("redirectedFrom", req.nextUrl.pathname)
        return NextResponse.redirect(redirectUrl)
      }

    } catch (error) {
      console.error("Erro no middleware:", error)
      // Em caso de erro, permitir acesso para evitar quebrar o sistema
    }
  }

  return res
}

export const config = {
  matcher: ["/dashboard/:path*"], // Proteger todas as rotas do dashboard
}
