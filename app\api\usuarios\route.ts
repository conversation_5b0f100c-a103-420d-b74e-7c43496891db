import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: usuarios, error } = await supabase
      .from("usuarios")
      .select("*")
      .order("nome")

    if (error) {
      console.error("Erro ao buscar usuários:", error)
      return NextResponse.json({ error: "Erro ao buscar usuários" }, { status: 500 })
    }

    return NextResponse.json(usuarios || [])
  } catch (error) {
    console.error("Erro ao buscar usuários:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { nome, email, nivel_acesso_id, senha } = body

    console.log("Dados recebidos:", { nome, email, nivel_acesso_id, senha: senha ? "***" : "não fornecida" })

    if (!nome || !email || !nivel_acesso_id) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 })
    }

    // Senha padrão se não fornecida
    const senhaFinal = senha || "TempPass123!"

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar service key para operações administrativas
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o email já existe
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from("usuarios")
      .select("id")
      .eq("email", email)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar usuário existente:", checkError)
      return NextResponse.json({ error: "Erro ao verificar usuário" }, { status: 500 })
    }

    if (existingUser) {
      return NextResponse.json({ error: "Email já está em uso" }, { status: 400 })
    }

    // Verificar se o nível de acesso existe
    const { data: nivelAcesso, error: nivelError } = await supabaseAdmin
      .from("niveis_acesso")
      .select("id")
      .eq("id", nivel_acesso_id)
      .maybeSingle()

    if (nivelError) {
      console.error("Erro ao verificar nível de acesso:", nivelError)
      return NextResponse.json({ error: "Erro ao verificar nível de acesso" }, { status: 500 })
    }

    if (!nivelAcesso) {
      return NextResponse.json({ error: "Nível de acesso inválido" }, { status: 400 })
    }

    // Primeiro, criar usuário no Supabase Auth
    console.log("Criando usuário no Supabase Auth:", email)
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: email,
      password: senhaFinal,
      email_confirm: true,
      user_metadata: {
        nome: nome
      }
    })

    if (authError) {
      console.error("Erro ao criar usuário no Auth:", authError)
      return NextResponse.json({
        error: "Erro ao criar conta de autenticação",
        message: authError.message
      }, { status: 500 })
    }

    if (!authUser.user) {
      return NextResponse.json({ error: "Falha ao criar usuário no sistema de autenticação" }, { status: 500 })
    }

    console.log("Usuário criado no Auth com sucesso:", authUser.user.id, authUser.user.email)

    // Agora criar usuário na tabela usando o ID do Auth
    const { data: usuario, error: insertError } = await supabaseAdmin
      .from("usuarios")
      .insert({
        id: authUser.user.id, // Usar o mesmo ID do Auth
        nome,
        email,
        nivel_acesso_id,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (insertError) {
      console.error("Erro ao criar usuário na tabela:", insertError)
      console.error("Código do erro:", insertError.code)
      console.error("Detalhes do erro:", insertError.details)
      console.error("Hint do erro:", insertError.hint)

      // Se falhou ao criar na tabela, remover do Auth para manter consistência
      try {
        console.log("Removendo usuário do Auth devido ao erro na tabela...")
        await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
        console.log("Usuário removido do Auth com sucesso")
      } catch (cleanupError) {
        console.error("Erro ao limpar usuário do Auth:", cleanupError)
      }

      // Tratar erros específicos do PostgreSQL
      if (insertError.code === '23505') {
        return NextResponse.json({
          error: "Email já cadastrado",
          message: "Este email já está sendo usado por outro usuário."
        }, { status: 400 })
      }

      if (insertError.code === '23503') {
        return NextResponse.json({
          error: "Nível de acesso inválido",
          message: "O nível de acesso selecionado não existe."
        }, { status: 400 })
      }

      if (insertError.code === '23502') {
        return NextResponse.json({
          error: "Campos obrigatórios ausentes",
          message: "Todos os campos são obrigatórios."
        }, { status: 400 })
      }

      if (insertError.code === '42501') {
        return NextResponse.json({
          error: "Permissão negada",
          message: "Você não tem permissão para criar usuários."
        }, { status: 403 })
      }

      // Erro genérico com mais detalhes
      return NextResponse.json({
        error: "Erro ao criar usuário",
        message: insertError.message || "Erro interno do servidor",
        code: insertError.code,
        details: insertError.details
      }, { status: 500 })
    }

    if (!usuario) {
      return NextResponse.json({ error: "Usuário não foi criado" }, { status: 500 })
    }

    // Registrar log de criação
    try {
      await supabaseAdmin
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "create",
          entidade: "usuario",
          entidade_id: usuario.id,
          detalhes: {
            usuario_criado_id: usuario.id,
            nome: usuario.nome,
            email: usuario.email,
            auth_user_id: authUser.user.id,
            criado_com_auth: true
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    console.log("Usuário criado com sucesso:", {
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      auth_id: authUser.user.id
    })

    return NextResponse.json({
      ...usuario,
      success: "Usuário criado com sucesso! Senha temporária: " + senhaFinal
    })
  } catch (error: any) {
    console.error("Erro ao criar usuário:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error.message
    }, { status: 500 })
  }
}
