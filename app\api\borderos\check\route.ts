import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET(request: Request) {
  try {
    console.log("🔍 API borderos/check - Recebida requisição GET");
    const { searchParams } = new URL(request.url)
    const codigo = searchParams.get("codigo")
    
    console.log(`🔍 Verificando bordero com código: "${codigo}"`);
    
    if (!codigo) {
      console.log("❌ Erro: Código do bordero não fornecido");
      return NextResponse.json({ error: "Código do bordero é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o bordero já existe
    const { data, error } = await supabase
      .from("borderos")
      .select("id")
      .eq("bordero_cod", codigo)
      .maybeSingle()

    if (error) {
      console.error("❌ Erro ao verificar bordero:", error)
      return NextResponse.json({ error: "Erro ao verificar bordero" }, { status: 500 })
    }

    const exists = !!data;
    console.log(`✅ Verificação concluída para "${codigo}": ${exists ? "EXISTE" : "NÃO EXISTE"}`);
    
    // Adiciona headers para evitar problemas de cache
    return NextResponse.json({ exists }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store'
      }
    })
  } catch (error) {
    console.error("Erro ao verificar bordero:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}