"use client"

import { useState, useEffect } from "react"
import { Bell, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { usePusher } from "@/hooks/use-pusher"

interface Notification {
  id: number | string
  titulo: string
  mensagem: string
  lida: boolean
  createdAt?: string | Date
  created_at?: string | Date
}

export function Notifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const { pusherClient, loading: pusherLoading, error: pusherError } = usePusher()

  useEffect(() => {
    // Carregar notificações iniciais
    const fetchNotifications = async () => {
      try {
        setLoading(true)
        const res = await fetch("/api/notificacoes")

        if (!res.ok) {
          throw new Error(`Erro ao carregar notificações: ${res.status}`)
        }

        const data = await res.json()

        // Garantir que data é um array
        const notificacoesArray = Array.isArray(data) ? data : []

        // Normalizar os dados para garantir consistência
        const normalizedNotifications = notificacoesArray.map((n: any) => ({
          id: n.id,
          titulo: n.titulo || "Notificação",
          mensagem: n.mensagem || "",
          lida: !!n.lida,
          createdAt: n.created_at || n.createdAt || new Date().toISOString(),
        }))

        setNotifications(normalizedNotifications)
        setUnreadCount(normalizedNotifications.filter((n) => !n.lida).length)
      } catch (err) {
        console.error("Erro ao carregar notificações:", err)
        // Definir um array vazio em caso de erro
        setNotifications([])
      } finally {
        setLoading(false)
      }
    }

    fetchNotifications()
  }, [])

  useEffect(() => {
    if (!pusherClient || pusherLoading) return

    // Inscrever-se no canal de notificações
    try {
      const channel = pusherClient.subscribe("notificacoes")

      channel.bind("nova-notificacao", (data: any) => {
        // Normalizar a notificação recebida
        const newNotification: Notification = {
          id: data.id,
          titulo: data.titulo || "Notificação",
          mensagem: data.mensagem || "",
          lida: !!data.lida,
          createdAt: data.created_at || data.createdAt || new Date().toISOString(),
        }

        setNotifications((prev) => [newNotification, ...prev])
        setUnreadCount((prev) => prev + 1)
      })

      return () => {
        try {
          pusherClient.unsubscribe("notificacoes")
        } catch (e) {
          console.error("Erro ao desinscrever do canal:", e)
        }
      }
    } catch (e) {
      console.error("Erro ao se inscrever no canal Pusher:", e)
    }
  }, [pusherClient, pusherLoading])

  const markAsRead = async (id: number | string) => {
    try {
      console.log(`Tentando marcar notificação ${id} como lida...`)

      const res = await fetch(`/api/notificacoes/${id}/lida`, {
        method: "PATCH",
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        console.error("Erro na resposta:", { status: res.status, statusText: res.statusText, errorData })
        throw new Error(`Erro ao marcar notificação como lida: ${res.status} - ${errorData.error || res.statusText}`)
      }

      const result = await res.json()
      console.log("Notificação marcada como lida:", result)

      setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, lida: true } : n)))
      setUnreadCount((prev) => Math.max(0, prev - 1))
    } catch (error) {
      console.error("Erro ao marcar notificação como lida:", error)
      // Mostrar toast de erro para o usuário
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('show-toast', {
          detail: {
            title: 'Erro',
            description: 'Não foi possível marcar a notificação como lida',
            variant: 'destructive'
          }
        }))
      }
    }
  }

  const markAllAsRead = async () => {
    try {
      console.log("Tentando marcar todas as notificações como lidas...")

      const res = await fetch("/api/notificacoes/ler-todas", {
        method: "PUT",
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}))
        console.error("Erro na resposta:", { status: res.status, statusText: res.statusText, errorData })
        throw new Error(`Erro ao marcar todas notificações como lidas: ${res.status} - ${errorData.error || res.statusText}`)
      }

      const result = await res.json()
      console.log("Todas as notificações marcadas como lidas:", result)

      setNotifications((prev) => prev.map((n) => ({ ...n, lida: true })))
      setUnreadCount(0)
    } catch (error) {
      console.error("Erro ao marcar todas notificações como lidas:", error)
      // Mostrar toast de erro para o usuário
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('show-toast', {
          detail: {
            title: 'Erro',
            description: 'Não foi possível marcar todas as notificações como lidas',
            variant: 'destructive'
          }
        }))
      }
    }
  }

  // Função para formatar a data
  const formatDate = (dateString?: string | Date) => {
    if (!dateString) return ""

    try {
      const date = new Date(dateString)
      return date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      })
    } catch (e) {
      return ""
    }
  }

  // Renderizar as notificações de forma segura
  const renderNotifications = () => {
    if (loading) {
      return (
        <div className="py-4 text-center text-sm text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
          Carregando notificações...
        </div>
      )
    }

    if (!Array.isArray(notifications) || notifications.length === 0) {
      return <div className="py-4 text-center text-sm text-muted-foreground">Nenhuma notificação</div>
    }

    // Garantir que estamos renderizando apenas os primeiros 5 itens
    return notifications.slice(0, 5).map((notification) => (
      <DropdownMenuItem
        key={notification.id}
        className={`flex flex-col items-start gap-1 py-2 ${!notification.lida ? "bg-muted/50" : ""}`}
        onClick={() => markAsRead(notification.id)}
      >
        <div className="flex w-full justify-between">
          <span className="font-medium">{notification.titulo}</span>
          <span className="text-xs text-muted-foreground">
            {formatDate(notification.createdAt || notification.created_at)}
          </span>
        </div>
        <p className="text-sm text-muted-foreground">{notification.mensagem}</p>
      </DropdownMenuItem>
    ))
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="relative rounded-full">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 p-0 text-[10px] text-white">
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
          <span className="sr-only">Notificações</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notificações</span>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" className="h-auto text-xs px-2 py-1" onClick={markAllAsRead}>
              Marcar todas como lidas
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {renderNotifications()}

        {!loading && Array.isArray(notifications) && notifications.length > 5 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="justify-center text-sm font-medium">
              Ver todas as notificações
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
