"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DateRangePicker } from "@/components/dashboard/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Bar,
  BarChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  Cell,
  Area,
  AreaChart,
  ResponsiveContainer,
} from "recharts"
import { ExportButton } from "@/components/dashboard/export-button"
import { PDFGenerator } from "@/lib/pdf-generator"
import { Loader2, Search, Filter, RefreshCw, DollarSign, Clock, CheckCircle, XCircle, BarChart3, TrendingUp, Download, FileText } from "lucide-react"
import { useSupabase } from "@/lib/supabase-provider"
import { toast } from "@/components/ui/use-toast"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

interface Secretaria {
  id: number
  nome: string
}

interface Tipo {
  id: number
  nome: string
}

interface Bordero {
  id: number
  borderoCod: string
  nomeEmpresa: string
  valor: number
  data: string
  status: string
  secretaria: { nome: string }
  tipo: { nome: string }
}

export default function RelatoriosPage() {
  // Estados para filtros
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [chartType, setChartType] = useState<"bar" | "line" | "pie" | "area">("bar")
  const [secretarias, setSecretarias] = useState<Secretaria[]>([])
  const [tipos, setTipos] = useState<Tipo[]>([])
  const [selectedSecretarias, setSelectedSecretarias] = useState<number[]>([])
  const [selectedTipos, setSelectedTipos] = useState<number[]>([])
  const [valorRange, setValorRange] = useState<[number, number]>([0, 5000000])
  const [valorMaximo, setValorMaximo] = useState<number>(5000000)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isFiltersVisible, setIsFiltersVisible] = useState(false)

  // Estados para dados
  const [chartData, setChartData] = useState<any[]>([])
  const [borderosPorTipo, setBorderosPorTipo] = useState<any[]>([])
  const [borderos, setBorderos] = useState<Bordero[]>([])
  const [resumoSecretarias, setResumoSecretarias] = useState<any[]>([])

  // Carregar dados iniciais
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setIsLoading(true)

        // Buscar dados básicos via API
        const response = await fetch("/api/relatorios/dados-basicos")
        if (!response.ok) {
          throw new Error("Erro ao buscar dados básicos")
        }

        const data = await response.json()
        console.log("Dados básicos recebidos:", data)
        setSecretarias(data.secretarias || [])
        setTipos(data.tipos || [])
        setValorMaximo(data.valorMaximo)
        setValorRange([0, data.valorMaximo])

        // Buscar dados para relatórios
        await fetchReportData()
      } catch (error) {
        console.error("Erro ao carregar dados iniciais:", error)
        toast({
          title: "Erro",
          description: "Não foi possível carregar os dados iniciais.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitialData()
  }, [])

  // Buscar dados com base nos filtros
  const fetchReportData = async () => {
    try {
      setIsLoading(true)

      // Construir parâmetros da URL
      const params = new URLSearchParams()

      if (dateRange.from && dateRange.to) {
        params.append("dateFrom", dateRange.from.toISOString())
        params.append("dateTo", dateRange.to.toISOString())
      }

      if (selectedSecretarias.length > 0) {
        params.append("secretarias", selectedSecretarias.join(","))
      }

      if (selectedTipos.length > 0) {
        params.append("tipos", selectedTipos.join(","))
      }

      if (valorRange[0] > 0) {
        params.append("valorMin", valorRange[0].toString())
      }

      if (valorRange[1] < valorMaximo) {
        params.append("valorMax", valorRange[1].toString())
      }

      if (statusFilter.length > 0) {
        params.append("status", statusFilter.join(","))
      }

      if (searchTerm) {
        params.append("search", searchTerm)
      }

      // Fazer requisição para a API
      const response = await fetch(`/api/relatorios?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Erro ao buscar dados do relatório")
      }

      const data = await response.json()
      console.log("Dados do relatório recebidos:", data)

      // Normalizar dados dos borderos
      const normalizedBorderos = (data.borderos || []).map((b: any) => ({
        id: b.id,
        borderoCod: b.bordero_cod,
        nomeEmpresa: b.nome_empresa,
        valor: b.valor,
        data: b.data,
        status: b.status,
        secretaria: b.secretaria || { nome: "Sem secretaria" },
        tipo: b.tipo || { nome: "Sem tipo" },
      }))

      setBorderos(normalizedBorderos)
      setChartData(data.chartData || [])
      setResumoSecretarias(data.resumoSecretarias || [])
      setBorderosPorTipo(data.borderosPorTipo || [])
    } catch (error) {
      console.error("Erro ao buscar dados para relatório:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados do relatório.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }



  // Função para renderizar o gráfico selecionado
  const renderChart = () => {
    if (chartData.length === 0) {
      return (
        <div className="flex h-[300px] items-center justify-center">
          <p className="text-muted-foreground">Nenhum dado disponível para exibição</p>
        </div>
      )
    }

    switch (chartType) {
      case "bar":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={chartData} margin={{ top: 20, right: 20, bottom: 60, left: 40 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="secretaria" angle={-45} textAnchor="end" height={60} />
              <YAxis
                tickFormatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                    maximumFractionDigits: 0,
                  }).format(value)
                }
              />
              <Tooltip
                formatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  }).format(Number(value))
                }
              />
              <Legend />
              <Bar dataKey="valor" name="Valor" fill="#3b82f6" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        )

      case "line":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={chartData} margin={{ top: 20, right: 20, bottom: 60, left: 40 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="secretaria" angle={-45} textAnchor="end" height={60} />
              <YAxis
                tickFormatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                    maximumFractionDigits: 0,
                  }).format(value)
                }
              />
              <Tooltip
                formatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  }).format(Number(value))
                }
              />
              <Legend />
              <Line type="monotone" dataKey="valor" name="Valor" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        )

      case "pie":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={120}
                fill="#8884d8"
                dataKey="valor"
                nameKey="secretaria"
                label={({ secretaria, percent }) => `${secretaria}: ${(percent * 100).toFixed(0)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={`hsl(${index * 45}, 70%, 60%)`} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  }).format(Number(value))
                }
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )

      case "area":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={chartData} margin={{ top: 20, right: 20, bottom: 60, left: 40 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="secretaria" angle={-45} textAnchor="end" height={60} />
              <YAxis
                tickFormatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                    maximumFractionDigits: 0,
                  }).format(value)
                }
              />
              <Tooltip
                formatter={(value) =>
                  new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  }).format(Number(value))
                }
              />
              <Legend />
              <Area type="monotone" dataKey="valor" name="Valor" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        )

      default:
        return null
    }
  }

  // Toggle para seleção de secretaria
  const toggleSecretaria = (id: number) => {
    setSelectedSecretarias((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  // Toggle para seleção de tipo
  const toggleTipo = (id: number) => {
    setSelectedTipos((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  // Toggle para seleção de status
  const toggleStatus = (status: string) => {
    setStatusFilter((prev) => (prev.includes(status) ? prev.filter((item) => item !== status) : [...prev, status]))
  }

  // Formatar valor para exibição
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
      maximumFractionDigits: 0,
    }).format(value)
  }

  // Formatar data para exibição
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  // Limpar todos os filtros
  const clearAllFilters = () => {
    setDateRange({ from: undefined, to: undefined })
    setSelectedSecretarias([])
    setSelectedTipos([])
    setValorRange([0, valorMaximo])
    setSearchTerm("")
    setStatusFilter([])
  }

  // Calcular resumos para os cards
  const calcularResumos = () => {
    const totalPagar = borderos
      .filter(b => ['novo', 'analise', 'assinado'].includes(b.status))
      .reduce((sum, b) => sum + b.valor, 0)

    const totalPendentesAssinatura = borderos
      .filter(b => b.status === 'analise')
      .reduce((sum, b) => sum + b.valor, 0)

    const totalAprovados = borderos
      .filter(b => ['assinado', 'pago'].includes(b.status))
      .reduce((sum, b) => sum + b.valor, 0)

    const totalCancelados = borderos
      .filter(b => ['cancelado', 'excluido'].includes(b.status))
      .reduce((sum, b) => sum + b.valor, 0)

    const totalBorderos = borderos.length

    return {
      totalPagar,
      totalPendentesAssinatura,
      totalAprovados,
      totalCancelados,
      totalBorderos
    }
  }

  const resumos = calcularResumos()

  // Função para gerar PDF
  const generatePDF = async () => {
    try {
      setIsLoading(true)

      // Buscar dados do usuário
      const userResponse = await fetch('/api/auth/user')
      const userData = userResponse.ok ? await userResponse.json() : { nome: 'Usuário', email: '<EMAIL>' }

      // Preparar dados para o PDF
      const reportData = {
        borderos: borderos.map(b => ({
          id: b.id,
          bordero_cod: b.borderoCod,
          nome_empresa: b.nomeEmpresa,
          valor: b.valor,
          status: b.status,
          data: b.data,
          secretaria: b.secretaria,
          tipo: b.tipo
        })),
        filtros: {
          dataInicio: dateRange.from?.toISOString().split('T')[0],
          dataFim: dateRange.to?.toISOString().split('T')[0],
          secretarias: selectedSecretarias.length > 0 ? selectedSecretarias.map(id => secretarias.find(s => s.id === id)?.nome).join(', ') : undefined,
          tipos: selectedTipos.length > 0 ? selectedTipos.map(id => tipos.find(t => t.id === id)?.nome).join(', ') : undefined,
          status: statusFilter.length > 0 ? statusFilter.join(', ') : undefined
        },
        usuario: userData
      }

      // Gerar PDF
      const pdfGenerator = new PDFGenerator()
      pdfGenerator.generateReport(reportData)

      // Salvar PDF
      const filename = `relatorio-borderos-${new Date().toISOString().split('T')[0]}.pdf`
      pdfGenerator.save(filename)

      toast({
        title: "PDF Gerado",
        description: `Relatório salvo como ${filename}`,
      })

    } catch (error) {
      console.error('Erro ao gerar PDF:', error)
      toast({
        title: "Erro",
        description: "Não foi possível gerar o relatório PDF",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Relatórios</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsFiltersVisible(!isFiltersVisible)} className="gap-1">
            <Filter className="h-3.5 w-3.5" />
            <span className="hidden sm:inline">Filtros</span>
          </Button>
          <DateRangePicker date={dateRange} onDateChange={setDateRange} />
          <Button variant="outline" size="sm" onClick={fetchReportData} disabled={isLoading} className="gap-1">
            {isLoading ? <Loader2 className="h-3.5 w-3.5 animate-spin" /> : <RefreshCw className="h-3.5 w-3.5" />}
            <span className="hidden sm:inline">Atualizar</span>
          </Button>
          <Button
            onClick={generatePDF}
            disabled={isLoading}
            className="gap-2 bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="h-4 w-4" />}
            Gerar Relatório PDF
          </Button>
        </div>
      </div>

      {/* Cards Resumidos */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total a Pagar</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatCurrency(resumos.totalPagar)}
            </div>
            <p className="text-xs text-muted-foreground">
              Novo + Em Análise + Assinado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendentes Assinatura</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {formatCurrency(resumos.totalPendentesAssinatura)}
            </div>
            <p className="text-xs text-muted-foreground">
              Em análise
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Aprovados</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {formatCurrency(resumos.totalAprovados)}
            </div>
            <p className="text-xs text-muted-foreground">
              Assinado + Pago
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cancelados</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {formatCurrency(resumos.totalCancelados)}
            </div>
            <p className="text-xs text-muted-foreground">
              Cancelado + Excluído
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Borderos</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resumos.totalBorderos}
            </div>
            <p className="text-xs text-muted-foreground">
              Todas as situações
            </p>
          </CardContent>
        </Card>
      </div>

      {isFiltersVisible && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Filtros Avançados</CardTitle>
              <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                Limpar filtros
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <Label>Busca</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Código, empresa..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Valor</Label>
                <div className="pt-6 px-2">
                  <Slider value={valorRange} min={0} max={valorMaximo} step={10000} onValueChange={setValorRange} />
                  <div className="flex justify-between mt-2 text-sm text-muted-foreground">
                    <span>{formatCurrency(valorRange[0])}</span>
                    <span>{formatCurrency(valorRange[1])}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Status</Label>
                <div className="grid grid-cols-2 gap-2">
                  {["novo", "analise", "assinado", "pago", "corrigir", "arquivado"].map((status) => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={statusFilter.includes(status)}
                        onCheckedChange={() => toggleStatus(status)}
                      />
                      <Label htmlFor={`status-${status}`} className="capitalize">
                        {status}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Secretarias</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-between">
                      <span>
                        {selectedSecretarias.length
                          ? `${selectedSecretarias.length} selecionadas`
                          : "Selecionar secretarias"}
                      </span>
                      <Filter className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[200px] p-0" align="start">
                    <div className="max-h-[200px] overflow-auto p-2">
                      {secretarias.map((secretaria) => (
                        <div key={secretaria.id} className="flex items-center space-x-2 py-1">
                          <Checkbox
                            id={`secretaria-${secretaria.id}`}
                            checked={selectedSecretarias.includes(secretaria.id)}
                            onCheckedChange={() => toggleSecretaria(secretaria.id)}
                          />
                          <Label htmlFor={`secretaria-${secretaria.id}`}>{secretaria.nome}</Label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Tipos</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-between">
                      <span>{selectedTipos.length ? `${selectedTipos.length} selecionados` : "Selecionar tipos"}</span>
                      <Filter className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[200px] p-0" align="start">
                    <div className="max-h-[200px] overflow-auto p-2">
                      {tipos.map((tipo) => (
                        <div key={tipo.id} className="flex items-center space-x-2 py-1">
                          <Checkbox
                            id={`tipo-${tipo.id}`}
                            checked={selectedTipos.includes(tipo.id)}
                            onCheckedChange={() => toggleTipo(tipo.id)}
                          />
                          <Label htmlFor={`tipo-${tipo.id}`}>{tipo.nome}</Label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>

              <div className="flex items-end">
                <Button onClick={fetchReportData} disabled={isLoading} className="gap-2">
                  {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                  Aplicar Filtros
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="geral" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="geral">Relatório Geral</TabsTrigger>
          <TabsTrigger value="tipo">Relatório por Tipo</TabsTrigger>
        </TabsList>
        <TabsContent value="geral" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Valores por Secretaria</CardTitle>
              <Select value={chartType} onValueChange={(value) => setChartType(value as any)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Tipo de Gráfico" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bar">Gráfico de Barras</SelectItem>
                  <SelectItem value="line">Gráfico de Linha</SelectItem>
                  <SelectItem value="pie">Gráfico de Pizza</SelectItem>
                  <SelectItem value="area">Gráfico de Área</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-[300px] items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="h-[400px] w-full">{renderChart()}</div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Resumo por Secretaria</CardTitle>
              <ExportButton data={resumoSecretarias} filename="resumo-secretarias" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-[200px] items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : resumoSecretarias.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhum dado disponível</p>
                </div>
              ) : (
                <Accordion type="multiple" className="w-full">
                  {resumoSecretarias.map((item, index) => (
                    <AccordionItem key={index} value={`secretaria-${index}`}>
                      <AccordionTrigger className="hover:no-underline">
                        <div className="flex items-center justify-between w-full pr-4">
                          <div className="flex items-center gap-3">
                            <div className="w-3 h-3 rounded-full bg-blue-500" />
                            <span className="font-medium">{item.Secretaria}</span>
                          </div>
                          <div className="flex items-center gap-6 text-sm">
                            <div className="text-right">
                              <div className="font-semibold text-green-600 dark:text-green-400">
                                {item.ValorTotal}
                              </div>
                              <div className="text-muted-foreground">Total</div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold">{item.Borderos}</div>
                              <div className="text-muted-foreground">Borderos</div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-blue-600 dark:text-blue-400">
                                {item.MediaPorBordero}
                              </div>
                              <div className="text-muted-foreground">Média</div>
                            </div>
                          </div>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="pt-4 space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card className="p-4">
                              <div className="flex items-center gap-2">
                                <DollarSign className="h-4 w-4 text-green-600" />
                                <span className="text-sm font-medium">Valor Total</span>
                              </div>
                              <div className="text-2xl font-bold text-green-600 dark:text-green-400 mt-2">
                                {item.ValorTotal}
                              </div>
                            </Card>
                            <Card className="p-4">
                              <div className="flex items-center gap-2">
                                <BarChart3 className="h-4 w-4 text-blue-600" />
                                <span className="text-sm font-medium">Quantidade</span>
                              </div>
                              <div className="text-2xl font-bold mt-2">
                                {item.Borderos} borderos
                              </div>
                            </Card>
                            <Card className="p-4">
                              <div className="flex items-center gap-2">
                                <TrendingUp className="h-4 w-4 text-purple-600" />
                                <span className="text-sm font-medium">Média</span>
                              </div>
                              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-2">
                                {item.MediaPorBordero}
                              </div>
                            </Card>
                          </div>

                          {/* Mini gráfico de barras (sparkline) */}
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-2">Distribuição por Status</h4>
                            <div className="h-20">
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={[
                                  { status: 'Novo', valor: Math.random() * 100 },
                                  { status: 'Análise', valor: Math.random() * 100 },
                                  { status: 'Assinado', valor: Math.random() * 100 },
                                  { status: 'Pago', valor: Math.random() * 100 }
                                ]}>
                                  <Bar dataKey="valor" fill="#3b82f6" radius={[2, 2, 0, 0]} />
                                  <Tooltip />
                                </BarChart>
                              </ResponsiveContainer>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tipo" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Relatório por Tipo de Bordero</CardTitle>
              <ExportButton data={borderosPorTipo} filename="relatorio-por-tipo" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex h-[200px] items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="space-y-6">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Quantidade</TableHead>
                        <TableHead>Valor Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {borderosPorTipo.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-4">
                            Nenhum dado disponível
                          </TableCell>
                        </TableRow>
                      ) : (
                        borderosPorTipo.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{item.tipo}</TableCell>
                            <TableCell>{item.quantidade}</TableCell>
                            <TableCell>{item.valorTotal}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="borderos">
                      <AccordionTrigger>Detalhes dos Borderos</AccordionTrigger>
                      <AccordionContent>
                        <div className="overflow-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Código</TableHead>
                                <TableHead>Empresa</TableHead>
                                <TableHead>Valor</TableHead>
                                <TableHead>Data</TableHead>
                                <TableHead>Secretaria</TableHead>
                                <TableHead>Tipo</TableHead>
                                <TableHead>Status</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {borderos.length === 0 ? (
                                <TableRow>
                                  <TableCell colSpan={7} className="text-center py-4">
                                    Nenhum bordero encontrado
                                  </TableCell>
                                </TableRow>
                              ) : (
                                borderos.slice(0, 10).map((bordero) => (
                                  <TableRow key={bordero.id}>
                                    <TableCell className="font-medium">{bordero.borderoCod}</TableCell>
                                    <TableCell>{bordero.nomeEmpresa}</TableCell>
                                    <TableCell>
                                      {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                                        bordero.valor,
                                      )}
                                    </TableCell>
                                    <TableCell>{formatDate(bordero.data)}</TableCell>
                                    <TableCell>{bordero.secretaria.nome}</TableCell>
                                    <TableCell>{bordero.tipo.nome}</TableCell>
                                    <TableCell className="capitalize">{bordero.status}</TableCell>
                                  </TableRow>
                                ))
                              )}
                              {borderos.length > 10 && (
                                <TableRow>
                                  <TableCell colSpan={7} className="text-center py-2 text-sm text-muted-foreground">
                                    Mostrando 10 de {borderos.length} borderos. Exporte para ver todos.
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
