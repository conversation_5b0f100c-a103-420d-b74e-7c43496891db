"use client"

import { useState, useEffect } from "react"
import { BorderoTable } from "@/components/dashboard/bordero-table"
import { DataTablePagination } from "@/components/ui/data-table-pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Eye,
  Edit,
  FileText,
  MoreHorizontal,
  Trash2,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { useRouter } from "next/navigation"

interface BorderoTableWithPaginationProps {
  status: "novo" | "analise" | "assinado" | "pago" | "todos" | "excluido" | "arquivado"
  secretariaId?: number
  tipoId?: number
  filtros?: any
}

export function BorderoTableWithPagination({
  status,
  secretariaId,
  tipoId,
  filtros
}: BorderoTableWithPaginationProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [totalItems, setTotalItems] = useState(0)
  const [borderos, setBorderos] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [paginatedBorderos, setPaginatedBorderos] = useState<any[]>([])

  // Função para buscar borderos com contagem total
  const fetchBorderos = async () => {
    try {
      setLoading(true)
      console.log(`Buscando borderos com status: ${status}`);

      // Construir URL base
      let url = `/api/borderos?status=${status}`;
      
      if (secretariaId) {
        url += `&secretariaId=${secretariaId}`;
      }

      if (tipoId) {
        url += `&tipoId=${tipoId}`;
      }

      // Adicionar filtros avançados
      if (filtros) {
        if (filtros.codigo) url += `&codigo=${encodeURIComponent(filtros.codigo)}`;
        if (filtros.empresa) url += `&empresa=${encodeURIComponent(filtros.empresa)}`;
        if (filtros.secretariaId && !secretariaId) url += `&secretariaId=${filtros.secretariaId}`;
        if (filtros.tipoId && !tipoId) url += `&tipoId=${filtros.tipoId}`;
        if (filtros.status && filtros.status !== status) url += `&status=${filtros.status}`;
        if (filtros.valorMin) url += `&valorMin=${filtros.valorMin}`;
        if (filtros.valorMax) url += `&valorMax=${filtros.valorMax}`;
        if (filtros.dataInicio) url += `&dataInicio=${filtros.dataInicio}`;
        if (filtros.dataFim) url += `&dataFim=${filtros.dataFim}`;
      }

      console.log("URL de busca (API borderos):", url);
      let response = await fetch(url);
      let data;
      let usedFallback = false;

      // Se a API de borderos falhar, tentar a API de relatórios como fallback
      if (!response.ok) {
        console.warn("API de borderos falhou, tentando API de relatórios como fallback");
        
        // Construir URL para API de relatórios
        let fallbackUrl = `/api/relatorios`;
        
        // Se não for "todos", adicione filtro de status
        if (status !== "todos") {
          fallbackUrl += `?status=${status}`;
        }

        // Adicionar outros filtros
        if (secretariaId) {
          fallbackUrl += fallbackUrl.includes('?') ? `&secretarias=${secretariaId}` : `?secretarias=${secretariaId}`;
        }

        if (tipoId) {
          fallbackUrl += fallbackUrl.includes('?') ? `&tipos=${tipoId}` : `?tipos=${tipoId}`;
        }

        // Adicionar filtros avançados para API de relatórios
        if (filtros) {
          if (filtros.codigo || filtros.empresa) {
            const searchTerm = filtros.codigo || filtros.empresa;
            fallbackUrl += fallbackUrl.includes('?') ? `&search=${encodeURIComponent(searchTerm)}` : `?search=${encodeURIComponent(searchTerm)}`;
          }
          if (filtros.secretariaId && !secretariaId) fallbackUrl += fallbackUrl.includes('?') ? `&secretarias=${filtros.secretariaId}` : `?secretarias=${filtros.secretariaId}`;
          if (filtros.tipoId && !tipoId) fallbackUrl += fallbackUrl.includes('?') ? `&tipos=${filtros.tipoId}` : `?tipos=${filtros.tipoId}`;
          if (filtros.valorMin) fallbackUrl += fallbackUrl.includes('?') ? `&valorMin=${filtros.valorMin}` : `?valorMin=${filtros.valorMin}`;
          if (filtros.valorMax) fallbackUrl += fallbackUrl.includes('?') ? `&valorMax=${filtros.valorMax}` : `?valorMax=${filtros.valorMax}`;
          if (filtros.dataInicio) fallbackUrl += fallbackUrl.includes('?') ? `&dateFrom=${filtros.dataInicio}` : `?dateFrom=${filtros.dataInicio}`;
          if (filtros.dataFim) fallbackUrl += fallbackUrl.includes('?') ? `&dateTo=${filtros.dataFim}` : `?dateTo=${filtros.dataFim}`;
        }

        console.log("URL de fallback (API relatórios):", fallbackUrl);
        response = await fetch(fallbackUrl);
        
        if (!response.ok) {
          console.error("Ambas as APIs falharam:", response.status, response.statusText);
          throw new Error("Erro ao buscar borderos");
        }
        
        usedFallback = true;
      }

      data = await response.json();
      console.log("Dados recebidos:", data);
      
      // Se estamos usando a API de relatórios, os borderos estão em data.borderos
      let borderosList;
      
      if (usedFallback) {
        // Filtrar os borderos pelo status correto se estamos usando a API de relatórios
        borderosList = data.borderos || [];
        if (status !== "todos") {
          borderosList = borderosList.filter((b: any) => b.status === status);
        }
      } else {
        borderosList = Array.isArray(data) ? data : [];
      }
      
      console.log(`Encontrados ${borderosList.length} borderos com status ${status}`);
      
      // Se ainda não temos dados, tentar uma última abordagem - buscar todos e filtrar manualmente
      if (borderosList.length === 0 && status !== "todos") {
        console.log("Tentando buscar todos os borderos e filtrar manualmente");
        const allResponse = await fetch("/api/relatorios");
        if (allResponse.ok) {
          const allData = await allResponse.json();
          if (allData.borderos && Array.isArray(allData.borderos)) {
            borderosList = allData.borderos.filter((b: any) => b.status === status);
            console.log(`Encontrados ${borderosList.length} borderos após filtro manual`);
          }
        }
      }
      
      setBorderos(borderosList);
      setTotalItems(borderosList.length);
    } catch (error) {
      console.error("Erro ao buscar borderos:", error);
      setBorderos([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  // Efeito para buscar borderos quando os filtros mudam
  useEffect(() => {
    fetchBorderos();
    setCurrentPage(1); // Resetar para a primeira página quando os filtros mudam
  }, [status, secretariaId, tipoId, filtros]);

  // Efeito para paginar os borderos
  useEffect(() => {
    if (borderos.length === 0) {
      setPaginatedBorderos([]);
      return;
    }

    // Se itemsPerPage for -1, mostrar todos
    if (itemsPerPage === -1) {
      setPaginatedBorderos(borderos);
      return;
    }

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, borderos.length);
    setPaginatedBorderos(borderos.slice(startIndex, endIndex));
  }, [borderos, currentPage, itemsPerPage]);

  // Função para mudar a página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Função para mudar itens por página
  const handleItemsPerPageChange = (value: string) => {
    const newItemsPerPage = parseInt(value);
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Resetar para a primeira página
  };

  return (
    <div className="space-y-4">
      {/* Tabela de borderos com os dados paginados */}
      <div className="relative">
        {/* Não usamos o BorderoTable diretamente porque precisamos controlar a paginação */}
        {/* Em vez disso, implementamos nossa própria versão da tabela */}

        {/* Nossa versão paginada da tabela */}
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-sky-600 rounded-full border-t-transparent"></div>
          </div>
        ) : (
          <div>
            {/* Layout Desktop */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full table-fixed border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="w-[160px] text-left py-3 px-4 font-medium text-sm">Código</th>
                    <th className="w-[300px] text-left py-3 px-4 font-medium text-sm">Empresa</th>
                    <th className="w-[140px] text-left py-3 px-4 font-medium text-sm">Valor</th>
                    <th className="w-[110px] text-left py-3 px-4 font-medium text-sm">Data</th>
                    <th className="w-[200px] text-left py-3 px-4 font-medium text-sm">Secretaria</th>
                    <th className="w-[100px] text-left py-3 px-4 font-medium text-sm">Tipo</th>
                    {status === "todos" && <th className="w-[130px] text-left py-3 px-4 font-medium text-sm">Status</th>}
                    <th className="text-right w-[100px] py-3 px-4 font-medium text-sm">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedBorderos.length === 0 ? (
                    <tr>
                      <td colSpan={status === "todos" ? 8 : 7} className="text-center py-8 text-gray-500">
                        Nenhum bordero encontrado.
                      </td>
                    </tr>
                  ) : (
                    paginatedBorderos.map((bordero) => (
                      <tr key={bordero.id} className="border-b hover:bg-slate-100 dark:hover:bg-slate-800/30 transition-colors">
                        <td className="py-3 px-4 font-medium font-mono text-sm truncate">{bordero.bordero_cod}</td>
                        <td className="py-3 px-4 truncate" title={bordero.nome_empresa}>
                          <div className="truncate font-medium">{bordero.nome_empresa}</div>
                        </td>
                        <td className="py-3 px-4 text-right font-semibold text-green-600 dark:text-green-400">
                          {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(bordero.valor)}
                        </td>
                        <td className="py-3 px-4 text-sm text-muted-foreground">{new Date(bordero.data).toLocaleDateString("pt-BR")}</td>
                        <td className="py-3 px-4 text-sm truncate" title={bordero.secretaria?.nome}>
                          <div className="truncate">{bordero.secretaria?.nome}</div>
                        </td>
                        <td className="py-3 px-4 text-sm truncate" title={bordero.tipo?.nome}>
                          <div className="truncate">{bordero.tipo?.nome}</div>
                        </td>
                        {status === "todos" && (
                          <td className="py-3 px-4">
                            {/* Renderizar status badge aqui */}
                            <div className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                              {bordero.status}
                            </div>
                          </td>
                        )}
                        <td className="py-3 px-4 text-right">
                          {/* Ações - Menu Dropdown */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                                <span className="mr-1">Ações</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                <span>Visualizar</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/editar`}>
                                <Edit className="mr-2 h-4 w-4" />
                                <span>Editar</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/documentos`}>
                                <FileText className="mr-2 h-4 w-4" />
                                <span>Documentos</span>
                              </DropdownMenuItem>
                              {status === "novo" && (
                                <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/analise`}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  <span>Enviar para Análise</span>
                                </DropdownMenuItem>
                              )}
                              {status === "analise" && (
                                <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/assinar`}>
                                  <FileText className="mr-2 h-4 w-4" />
                                  <span>Assinar</span>
                                </DropdownMenuItem>
                              )}
                              {(status === "novo" || status === "analise") && (
                                <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/excluir`} className="text-red-600 focus:text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  <span>Excluir</span>
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Layout Mobile (simplificado) */}
            <div className="md:hidden space-y-4">
              {paginatedBorderos.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  Nenhum bordero encontrado.
                </div>
              ) : (
                paginatedBorderos.map((bordero) => (
                  <div key={bordero.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <div className="font-mono text-sm font-medium text-gray-600">
                          {bordero.bordero_cod}
                        </div>
                        <div className="font-semibold text-lg text-green-600">
                          {new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL"
                          }).format(bordero.valor)}
                        </div>
                      </div>
                      <div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>Visualizar</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => window.location.href = `/dashboard/borderos/${bordero.id}/editar`}>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Editar</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <div className="text-sm font-medium text-gray-600">Empresa</div>
                        <div className="text-sm">{bordero.nome_empresa}</div>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-gray-600">Data</div>
                          <div>{new Date(bordero.data).toLocaleDateString("pt-BR")}</div>
                        </div>
                        <div>
                          <div className="font-medium text-gray-600">Secretaria</div>
                          <div className="truncate">{bordero.secretaria?.nome}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* Controles de paginação */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4 border-t">
        <div className="flex items-center gap-2">
          <Label htmlFor="itemsPerPage" className="text-sm">Itens por página:</Label>
          <Select
            value={itemsPerPage.toString()}
            onValueChange={handleItemsPerPageChange}
          >
            <SelectTrigger id="itemsPerPage" className="w-[100px]">
              <SelectValue placeholder="10" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
              <SelectItem value="-1">Todos</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Componente de paginação */}
        <DataTablePagination
          currentPage={currentPage}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage === -1 ? totalItems : itemsPerPage}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
}
