"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { DateRangePicker } from "@/components/dashboard/date-range-picker"
import { Plus, Loader2 } from "lucide-react"
import Link from "next/link"
import { useSupabase } from "@/lib/supabase-provider"
import { useToast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export default function MeusBorderosPage() {
  const { supabase, user } = useSupabase()
  const { toast } = useToast()
  const [search, setSearch] = useState("")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [loading, setLoading] = useState(true)
  const [borderos, setBorderos] = useState<any[]>([])

  useEffect(() => {
    if (user) {
      fetchMeusBorderos()
    }
  }, [user, dateRange])

  const fetchMeusBorderos = async () => {
    try {
      setLoading(true)

      let query = supabase
        .from("borderos")
        .select(
          `
          *,
          secretaria:secretarias(nome),
          tipo:tipos(nome)
        `,
        )
        .eq("responsavel_id", user?.id)
        .order("created_at", { ascending: false })

      // Aplicar filtro de data se existir
      if (dateRange.from && dateRange.to) {
        const fromDate = new Date(dateRange.from)
        fromDate.setHours(0, 0, 0, 0)

        const toDate = new Date(dateRange.to)
        toDate.setHours(23, 59, 59, 999)

        query = query.gte("data", fromDate.toISOString()).lte("data", toDate.toISOString())
      }

      const { data, error } = await query

      if (error) throw error

      setBorderos(data || [])
    } catch (error) {
      console.error("Erro ao buscar borderos:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar seus borderos.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Função para filtrar borderos pelo termo de busca
  const filteredBorderos = borderos.filter(
    (bordero) =>
      bordero.bordero_cod.toLowerCase().includes(search.toLowerCase()) ||
      bordero.nome_empresa.toLowerCase().includes(search.toLowerCase()) ||
      bordero.secretaria.nome.toLowerCase().includes(search.toLowerCase()) ||
      bordero.tipo.nome.toLowerCase().includes(search.toLowerCase()),
  )

  // Função para renderizar o badge de status
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "novo":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            Novo
          </Badge>
        )
      case "analise":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
            Em Análise
          </Badge>
        )
      case "assinado":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            Assinado
          </Badge>
        )
      case "pago":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">
            Pago
          </Badge>
        )
      case "corrigir":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
            Corrigir
          </Badge>
        )
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Meus Borderos</h1>
        <Link href="/dashboard/borderos/novo">
          <Button className="bg-sky-600 hover:bg-sky-700">
            <Plus className="mr-2 h-4 w-4" />
            Novo Bordero
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filtrar Borderos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-end">
            <div className="flex-1 space-y-2">
              <Input
                placeholder="Buscar por código, empresa..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <DateRangePicker date={dateRange} onDateChange={setDateRange} />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Borderos Cadastrados por Mim</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Código</TableHead>
                  <TableHead>Empresa</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Secretaria</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBorderos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center">
                      Nenhum bordero encontrado.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredBorderos.map((bordero) => (
                    <TableRow key={bordero.id}>
                      <TableCell className="font-medium">{bordero.bordero_cod}</TableCell>
                      <TableCell>{bordero.nome_empresa}</TableCell>
                      <TableCell>
                        {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(bordero.valor)}
                      </TableCell>
                      <TableCell>{new Date(bordero.data).toLocaleDateString("pt-BR")}</TableCell>
                      <TableCell>{bordero.secretaria.nome}</TableCell>
                      <TableCell>{bordero.tipo.nome}</TableCell>
                      <TableCell>{renderStatusBadge(bordero.status)}</TableCell>
                      <TableCell className="text-right">
                        <Link href={`/dashboard/borderos/${bordero.id}`}>
                          <Button variant="ghost" size="sm">
                            Visualizar
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
