-- Script para limpar níveis de acesso duplicados

-- 1. <PERSON><PERSON>, vamos ver os dados duplicados
SELECT nome, COUNT(*) as quantidade 
FROM niveis_acesso 
GROUP BY nome 
HAVING COUNT(*) > 1;

-- 2. <PERSON><PERSON><PERSON> todos os registros duplicados, mantendo apenas um de cada
DELETE FROM niveis_acesso 
WHERE id NOT IN (
    SELECT DISTINCT ON (nome) id 
    FROM niveis_acesso 
    ORDER BY nome, created_at ASC
);

-- 3. Verificar se ainda há duplicados
SELECT nome, COUNT(*) as quantidade 
FROM niveis_acesso 
GROUP BY nome;

-- 4. Se necess<PERSON><PERSON>, recriar os níveis básicos
INSERT INTO niveis_acesso (nome, descricao) VALUES
('Administrador', 'Acesso total ao sistema'),
('Operador', 'Acesso para operações básicas'),
('Visualizador', 'Apenas visualização de dados')
ON CONFLICT (nome) DO NOTHING;
