import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST(request: Request) {
  try {
    const { resetToken, newPassword } = await request.json()

    if (!resetToken || !newPassword) {
      return NextResponse.json({ error: "Token e nova senha são obrigatórios" }, { status: 400 })
    }

    // Validar força da senha
    if (newPassword.length < 8) {
      return NextResponse.json({ error: "A senha deve ter pelo menos 8 caracteres" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar token de redefinição
    const { data: resetData, error: tokenError } = await supabase
      .from("reset_tokens")
      .select("*")
      .eq("token", resetToken)
      .eq("usado", false)
      .single()

    if (tokenError || !resetData) {
      return NextResponse.json({ error: "Token inválido ou expirado" }, { status: 400 })
    }

    // Verificar se o token não expirou
    const now = new Date()
    const expiresAt = new Date(resetData.expira_em)

    if (now > expiresAt) {
      return NextResponse.json({ error: "Token expirado" }, { status: 400 })
    }

    // Buscar usuário pelo email
    const { data: usuario, error: userError } = await supabase
      .from("usuarios")
      .select("id, email")
      .eq("email", resetData.email)
      .single()

    if (userError || !usuario) {
      return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 })
    }

    // Atualizar senha no Supabase Auth
    const { error: passwordError } = await supabase.auth.admin.updateUserById(
      usuario.id,
      { password: newPassword }
    )

    if (passwordError) {
      console.error("Erro ao atualizar senha:", passwordError)
      return NextResponse.json({ error: "Erro ao atualizar senha" }, { status: 500 })
    }

    // Marcar token como usado
    const { error: updateError } = await supabase
      .from("reset_tokens")
      .update({ usado: true })
      .eq("id", resetData.id)

    if (updateError) {
      console.error("Erro ao marcar token como usado:", updateError)
    }

    // Registrar log de alteração de senha
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: usuario.id,
          acao: "reset_password",
          entidade: "usuario",
          entidade_id: usuario.id,
          detalhes: {
            email: usuario.email,
            metodo: "codigo_verificacao"
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    return NextResponse.json({ 
      message: "Senha redefinida com sucesso" 
    })

  } catch (error) {
    console.error("Erro ao redefinir senha:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
