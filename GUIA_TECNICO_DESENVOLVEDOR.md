# 🛠️ Guia Técnico para Desenvolvedores - CRM Bordero

## 🏗️ Arquitetura do Sistema

### 📁 Estrutura de Pastas
```
CRM/
├── app/                    # Next.js 15 App Router
│   ├── api/               # API Routes
│   ├── dashboard/         # Páginas do dashboard
│   ├── globals.css        # Estilos globais
│   └── layout.tsx         # Layout principal
├── components/            # Componentes React
│   ├── ui/               # Componentes base (shadcn/ui)
│   └── dashboard/        # Componentes específicos
├── lib/                  # Utilitários e configurações
├── prisma/               # Schema do banco de dados
├── scripts/              # Scripts de setup
├── sql/                  # Arquivos SQL
└── types/                # Definições TypeScript
```

### 🔧 Stack Tecnológico

#### Frontend
- **Next.js 15**: Framework React com App Router
- **React 18**: Biblioteca de interface
- **TypeScript**: Tipagem estática
- **Tailwind CSS**: Framework CSS utilitário
- **Shadcn/ui**: Componentes de interface
- **Lucide React**: Ícones

#### Backend
- **Next.js API Routes**: Endpoints da API
- **Prisma**: ORM para banco de dados
- **Supabase**: Backend-as-a-Service
- **PostgreSQL**: Banco de dados relacional

#### Ferramentas
- **ESLint**: Linting de código
- **Prettier**: Formatação de código
- **Vercel**: Deploy e hosting

---

## 🗃️ Modelos de Dados (Prisma)

### 👤 Usuario
```prisma
model Usuario {
  id            Int            @id @default(autoincrement())
  nome          String
  email         String         @unique
  senha         String
  nivelAcesso   String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  
  // Relacionamentos
  secretarias   SecretariaUsuario[]
  borderos      Bordero[]
  notificacoes  Notificacao[]
  direcionamentos DirecionamentoUsuario[]
  logAtividades LogAtividade[]
  devolucoes    BorderoDevolucao[]
}
```

### 📄 Bordero
```prisma
model Bordero {
  id            Int            @id @default(autoincrement())
  borderoCod    String         @unique
  valor         Float
  data          DateTime
  nomeEmpresa   String
  secretariaId  Int
  tipoId        Int
  observacao    String?
  status        String         @default("novo")
  dadosStatus   String?
  dataAlteracao DateTime       @updatedAt
  responsavelId Int?
  
  // Relacionamentos
  secretaria    Secretaria     @relation(fields: [secretariaId], references: [id])
  tipo          Tipo           @relation(fields: [tipoId], references: [id])
  responsavel   Usuario?       @relation(fields: [responsavelId], references: [id])
  devolucoes    BorderoDevolucao[]
}
```

### 🏢 Secretaria
```prisma
model Secretaria {
  id            Int            @id @default(autoincrement())
  nome          String
  slug          String         @unique
  valoresTotal  Float          @default(0)
  
  // Relacionamentos
  usuarios      SecretariaUsuario[]
  borderos      Bordero[]
}
```

---

## 🔌 APIs e Endpoints

### 📋 Borderos API

#### GET /api/borderos
```typescript
// Parâmetros de query
interface BorderosQuery {
  page?: number
  limit?: number
  search?: string
  secretariaId?: number
  tipoId?: number
  status?: string
  dataInicio?: string
  dataFim?: string
  valorMinimo?: number
  valorMaximo?: number
}

// Resposta
interface BorderosResponse {
  borderos: Bordero[]
  total: number
  page: number
  totalPages: number
}
```

#### POST /api/borderos
```typescript
interface CreateBorderoRequest {
  borderoCod: string
  valor: number
  data: string
  nomeEmpresa: string
  secretariaId: number
  tipoId: number
  observacao?: string
}
```

#### PUT /api/borderos/[id]
```typescript
interface UpdateBorderoRequest {
  valor?: number
  data?: string
  nomeEmpresa?: string
  secretariaId?: number
  tipoId?: number
  observacao?: string
  status?: string
  dadosStatus?: string
  responsavelId?: number
}
```

### 👥 Usuários API

#### GET /api/usuarios
```typescript
interface UsuariosResponse {
  usuarios: Usuario[]
  total: number
}
```

#### POST /api/usuarios
```typescript
interface CreateUsuarioRequest {
  nome: string
  email: string
  senha: string
  nivelAcesso: string
  secretariaIds?: number[]
}
```

---

## 🎨 Componentes UI

### 📝 Formulários

#### BorderoForm
```typescript
interface BorderoFormProps {
  bordero?: Bordero
  onSubmit: (data: BorderoFormData) => void
  onCancel: () => void
  isLoading?: boolean
}

// Localização: components/dashboard/borderos/bordero-form.tsx
```

#### UsuarioForm
```typescript
interface UsuarioFormProps {
  usuario?: Usuario
  onSubmit: (data: UsuarioFormData) => void
  onCancel: () => void
  isLoading?: boolean
}

// Localização: components/dashboard/usuarios/usuario-form.tsx
```

### 📊 Tabelas

#### BorderosTable
```typescript
interface BorderosTableProps {
  borderos: Bordero[]
  onEdit: (bordero: Bordero) => void
  onDelete: (id: number) => void
  onView: (bordero: Bordero) => void
  isLoading?: boolean
}

// Localização: components/dashboard/borderos/borderos-table.tsx
```

### 🔍 Filtros

#### BorderoFilters
```typescript
interface BorderoFiltersProps {
  filters: BorderoFilters
  onFiltersChange: (filters: BorderoFilters) => void
  secretarias: Secretaria[]
  tipos: Tipo[]
}

// Localização: components/dashboard/borderos/bordero-filters.tsx
```

---

## 🔐 Autenticação e Autorização

### 🔑 Supabase Auth
```typescript
// lib/supabase-client.ts
import { createClient } from '@supabase/supabase-js'

export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Verificar autenticação
export const getUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}
```

### 🛡️ Middleware de Proteção
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session && req.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', req.url))
  }
  
  return res
}
```

### 🎯 Níveis de Acesso
```typescript
// types/auth.ts
export type NivelAcesso = 'admin' | 'gerente' | 'operador' | 'visualizador'

export interface Permissoes {
  borderos: {
    criar: boolean
    editar: boolean
    excluir: boolean
    visualizar: boolean
  }
  usuarios: {
    criar: boolean
    editar: boolean
    excluir: boolean
    visualizar: boolean
  }
  configuracoes: {
    acessar: boolean
    editar: boolean
  }
}

// lib/permissions.ts
export const getPermissoes = (nivelAcesso: NivelAcesso): Permissoes => {
  switch (nivelAcesso) {
    case 'admin':
      return {
        borderos: { criar: true, editar: true, excluir: true, visualizar: true },
        usuarios: { criar: true, editar: true, excluir: true, visualizar: true },
        configuracoes: { acessar: true, editar: true }
      }
    case 'gerente':
      return {
        borderos: { criar: true, editar: true, excluir: false, visualizar: true },
        usuarios: { criar: false, editar: false, excluir: false, visualizar: true },
        configuracoes: { acessar: true, editar: false }
      }
    // ... outros níveis
  }
}
```

---

## 📊 Estado Global e Context

### 🌐 AuthContext
```typescript
// lib/auth-context.tsx
interface AuthContextType {
  user: User | null
  userDetails: UserDetails | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  updateProfile: (data: Partial<UserDetails>) => Promise<void>
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider')
  }
  return context
}
```

### 🔔 NotificationContext
```typescript
// lib/notification-context.tsx
interface NotificationContextType {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  markAsRead: (id: string) => void
}
```

---

## 🎨 Temas e Styling

### 🌙 Dark Mode
```typescript
// lib/theme-provider.tsx
export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system')
  
  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')
    
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light'
      root.classList.add(systemTheme)
    } else {
      root.classList.add(theme)
    }
  }, [theme])
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}
```

### 🎨 Tailwind Config
```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class',
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        // ... outras cores
      }
    }
  },
  plugins: [require('tailwindcss-animate')]
}
```

---

## 🔧 Utilitários e Helpers

### 📅 Formatação de Data
```typescript
// lib/date-utils.ts
import { format, parseISO } from 'date-fns'
import { ptBR } from 'date-fns/locale'

export const formatDate = (date: string | Date) => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  return format(dateObj, 'dd/MM/yyyy', { locale: ptBR })
}

export const formatDateTime = (date: string | Date) => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR })
}
```

### 💰 Formatação de Moeda
```typescript
// lib/currency-utils.ts
export const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

export const parseCurrency = (value: string): number => {
  return parseFloat(value.replace(/[^\d,-]/g, '').replace(',', '.'))
}
```

### 🔍 Validação
```typescript
// lib/validation.ts
import { z } from 'zod'

export const borderoSchema = z.object({
  borderoCod: z.string().min(1, 'Código é obrigatório'),
  valor: z.number().positive('Valor deve ser positivo'),
  data: z.string().min(1, 'Data é obrigatória'),
  nomeEmpresa: z.string().min(1, 'Nome da empresa é obrigatório'),
  secretariaId: z.number().positive('Secretaria é obrigatória'),
  tipoId: z.number().positive('Tipo é obrigatório'),
  observacao: z.string().optional()
})

export const usuarioSchema = z.object({
  nome: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  senha: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres'),
  nivelAcesso: z.enum(['admin', 'gerente', 'operador', 'visualizador'])
})
```

---

## 🚀 Deploy e Produção

### 📦 Build
```bash
# Instalar dependências
npm install

# Build para produção
npm run build

# Iniciar em produção
npm start
```

### 🌐 Vercel Deploy
```bash
# Instalar Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### 🔧 Variáveis de Ambiente
```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

---

## 🧪 Testes

### 🔬 Jest + Testing Library
```typescript
// __tests__/components/bordero-form.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { BorderoForm } from '@/components/dashboard/borderos/bordero-form'

describe('BorderoForm', () => {
  it('should render form fields', () => {
    render(<BorderoForm onSubmit={jest.fn()} onCancel={jest.fn()} />)
    
    expect(screen.getByLabelText(/código/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/valor/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/empresa/i)).toBeInTheDocument()
  })
  
  it('should call onSubmit with form data', async () => {
    const onSubmit = jest.fn()
    render(<BorderoForm onSubmit={onSubmit} onCancel={jest.fn()} />)
    
    fireEvent.change(screen.getByLabelText(/código/i), {
      target: { value: 'BRD-001' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /salvar/i }))
    
    expect(onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({ borderoCod: 'BRD-001' })
    )
  })
})
```

---

## 📝 Convenções de Código

### 📁 Nomenclatura de Arquivos
- Componentes: `PascalCase.tsx`
- Páginas: `kebab-case/page.tsx`
- Utilitários: `kebab-case.ts`
- Tipos: `kebab-case.types.ts`

### 🏷️ Nomenclatura de Variáveis
- Variáveis: `camelCase`
- Constantes: `UPPER_SNAKE_CASE`
- Componentes: `PascalCase`
- Interfaces: `PascalCase` com sufixo `Props` ou `Type`

### 📝 Comentários
```typescript
/**
 * Componente para exibir e editar borderos
 * @param bordero - Dados do bordero (opcional para criação)
 * @param onSubmit - Callback executado ao salvar
 * @param onCancel - Callback executado ao cancelar
 */
export const BorderoForm: React.FC<BorderoFormProps> = ({
  bordero,
  onSubmit,
  onCancel
}) => {
  // Implementação...
}
```

---

**Versão**: 1.0.0  
**Última Atualização**: Janeiro 2025  
**Desenvolvido por**: Equipe CRM Bordero
