import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import { formatCurrency } from './currency-utils'

interface BorderoData {
  id: string
  bordero_cod: string
  nome_empresa: string
  valor: number
  status: string
  data: string
  secretaria: { nome: string }
  //tipo: { nome: string }
  direcionamento?: { nome: string }
}

interface ReportData {
  borderos: BorderoData[]
  filtros: {
    periodo?: string
    dataInicio?: string
    dataFim?: string
    secretarias?: string
    //tipos?: string
    status?: string
  }
  usuario: {
    nome: string
    email: string
  }
  resumo?: {
    total: number
    valorTotal: number
    porStatus: {
      novo: number
      analise: number
      assinado: number
      pago: number
    }
  }
}

export class PDFGenerator {
  private doc: jsPDF
  private pageHeight: number
  private pageWidth: number
  private margin: number
  private currentY: number

  constructor() {
    // Configurar explicitamente para formato retrato A4
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })
    this.pageHeight = this.doc.internal.pageSize.getHeight()
    this.pageWidth = this.doc.internal.pageSize.getWidth()
    this.margin = 15
    this.currentY = this.margin

    // Configurar fonte padrão
    this.doc.setFont('helvetica')
  }

  generateReport(data: ReportData): void {
    this.addHeader(data.usuario)
    this.addFilters(data.filtros)
    this.addSummary(data.borderos)
    this.addTable(data.borderos)
    this.addFooter()
  }

  private addHeader(usuario: { nome: string; email: string }): void {
    // Fundo do cabeçalho institucional
    this.doc.setFillColor(54, 86, 229) // #3656e5 - cor institucional
    this.doc.rect(0, 0, this.pageWidth, 65, 'F')

    // Logo/Brasão estilizado
    this.doc.setFillColor(255, 255, 255)
    this.doc.rect(this.margin, this.margin, 50, 30, 'F')

    this.doc.setTextColor(54, 86, 229)
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('PREFEITURA', this.margin + 3, this.margin + 10)
    this.doc.setFontSize(8)
    this.doc.text('MUNICIPAL', this.margin + 3, this.margin + 17)
    this.doc.text('CRM BORDERÔ', this.margin + 3, this.margin + 24)

    // Título principal
    this.doc.setTextColor(255, 255, 255)
    this.doc.setFontSize(22)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('RELATÓRIO DE BORDERÔS', this.margin + 60, this.margin + 18)

    // Informações do relatório
    this.doc.setFontSize(9)
    this.doc.setFont('helvetica', 'normal')
    const agora = new Date()
    this.doc.text(`Gerado por: ${usuario.nome}`, this.margin + 60, this.margin + 30)
    this.doc.text(`Data/Hora: ${agora.toLocaleDateString('pt-BR')} às ${agora.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`, this.margin + 60, this.margin + 38)

    // Data destacada no canto direito
    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(agora.toLocaleDateString('pt-BR'), this.pageWidth - this.margin - 30, this.margin + 18)

    // Linha separadora
    this.doc.setDrawColor(255, 255, 255)
    this.doc.setLineWidth(1)
    this.doc.line(this.margin, 65, this.pageWidth - this.margin, 65)

    this.currentY = 75
  }



  private addFilters(filtros: ReportData['filtros']): void {
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(54, 86, 229) // Cor institucional
    this.doc.text('🔍 FILTROS APLICADOS', this.margin, this.currentY)
    this.currentY += 12

    this.doc.setFontSize(10)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(0, 0, 0)

    let hasFilters = false

    // Período selecionado
    if (filtros.periodo) {
      const periodoTexto = this.getPeriodoTexto(filtros.periodo)
      this.doc.text(`• Período: ${periodoTexto}`, this.margin + 5, this.currentY)
      this.currentY += 6
      hasFilters = true
    } else if (filtros.dataInicio || filtros.dataFim) {
      const periodo = `${filtros.dataInicio || 'Início'} até ${filtros.dataFim || 'Fim'}`
      this.doc.text(`• Período: ${periodo}`, this.margin + 5, this.currentY)
      this.currentY += 6
      hasFilters = true
    }

    if (filtros.secretarias) {
      this.doc.text(`• Secretarias: ${filtros.secretarias}`, this.margin + 5, this.currentY)
      this.currentY += 6
      hasFilters = true
    }

    if (filtros.tipos) {
      this.doc.text(`• Tipos: ${filtros.tipos}`, this.margin + 5, this.currentY)
      this.currentY += 6
      hasFilters = true
    }

    if (filtros.status) {
      this.doc.text(`• Status: ${filtros.status}`, this.margin + 5, this.currentY)
      this.currentY += 6
      hasFilters = true
    }

    if (!hasFilters) {
      this.doc.setTextColor(107, 114, 128) // gray-500
      this.doc.text('• Nenhum filtro aplicado - Exibindo todos os borderos', this.margin + 5, this.currentY)
      this.currentY += 6
    }

    this.currentY += 10
  }

  private getPeriodoTexto(periodo: string): string {
    const periodoMap: Record<string, string> = {
      'all': 'Todo o período disponível',
      'week': 'Semana atual (Domingo a Sábado)',
      'month': 'Mês atual',
      'year': 'Ano atual',
      'custom': 'Período personalizado'
    }
    return periodoMap[periodo] || periodo
  }

  private addSummary(borderos: BorderoData[]): void {
    const total = borderos.length
    const valorTotal = borderos.reduce((sum, b) => sum + b.valor, 0)

    // Calcular estatísticas por status
    const stats = {
      novos: borderos.filter(b => b.status === 'novo').length,
      analise: borderos.filter(b => b.status === 'analise').length,
      assinados: borderos.filter(b => b.status === 'assinado').length,
      pagos: borderos.filter(b => b.status === 'pago').length,
      cancelados: borderos.filter(b => ['cancelado', 'excluido'].includes(b.status)).length,

      // Valores por status
      valorNovos: borderos.filter(b => b.status === 'novo').reduce((sum, b) => sum + b.valor, 0),
      valorAnalise: borderos.filter(b => b.status === 'analise').reduce((sum, b) => sum + b.valor, 0),
      valorAssinados: borderos.filter(b => b.status === 'assinado').reduce((sum, b) => sum + b.valor, 0),
      valorPagos: borderos.filter(b => b.status === 'pago').reduce((sum, b) => sum + b.valor, 0),

      // Valores agregados
      valorAPagar: borderos.filter(b => ['novo', 'analise', 'assinado'].includes(b.status)).reduce((sum, b) => sum + b.valor, 0),
      valorProcessado: borderos.filter(b => ['assinado', 'pago'].includes(b.status)).reduce((sum, b) => sum + b.valor, 0),
    }

    // Título da seção
    this.doc.setFontSize(14)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(54, 86, 229) // Cor institucional
    this.doc.text('📊 RESUMO GERAL', this.margin, this.currentY)
    this.currentY += 15

    // Cards principais (4 cards em linha)
    this.addDashboardCards([
      { title: 'Total de Borderos', value: total.toString(), subtitle: 'Todos os borderos', color: [59, 130, 246] }, // blue-500
      { title: 'Valor Total', value: formatCurrency(valorTotal), subtitle: 'Soma de todos os valores', color: [34, 197, 94] }, // green-500
      { title: 'A Pagar', value: formatCurrency(stats.valorAPagar), subtitle: 'Novo + Análise + Assinado', color: [249, 115, 22] }, // orange-500
      { title: 'Processado', value: formatCurrency(stats.valorProcessado), subtitle: 'Assinado + Pago', color: [168, 85, 247] } // purple-500
    ])

    this.currentY += 20

    // Título da seção de status
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(54, 86, 229)
    this.doc.text('📈 DETALHAMENTO POR STATUS', this.margin, this.currentY)
    this.currentY += 15

    // Cards de status (6 cards em 2 linhas)
    this.addDashboardCards([
      { title: 'Novos', value: stats.novos.toString(), subtitle: formatCurrency(stats.valorNovos), color: [59, 130, 246] }, // blue-500
      { title: 'Em Análise', value: stats.analise.toString(), subtitle: formatCurrency(stats.valorAnalise), color: [234, 179, 8] }, // yellow-500
      { title: 'Assinados', value: stats.assinados.toString(), subtitle: formatCurrency(stats.valorAssinados), color: [34, 197, 94] } // green-500
    ])

    this.currentY += 10

    this.addDashboardCards([
      { title: 'Pagos', value: stats.pagos.toString(), subtitle: formatCurrency(stats.valorPagos), color: [16, 185, 129] }, // emerald-500
      { title: 'Cancelados', value: stats.cancelados.toString(), subtitle: formatCurrency(borderos.filter(b => ['cancelado', 'excluido'].includes(b.status)).reduce((sum, b) => sum + b.valor, 0)), color: [239, 68, 68] }, // red-500
      { title: 'Taxa Aprovação', value: `${total > 0 ? Math.round(((stats.assinados + stats.pagos) / total) * 100) : 0}%`, subtitle: 'Borderos aprovados', color: [139, 92, 246] } // violet-500
    ])

    this.currentY += 25
  }

  private addDashboardCards(cards: Array<{ title: string; value: string; subtitle: string; color: [number, number, number] }>): void {
    const cardWidth = 45
    const cardHeight = 25
    const cardSpacing = 5
    const cardsPerRow = 3
    const startX = this.margin

    cards.forEach((card, index) => {
      const row = Math.floor(index / cardsPerRow)
      const col = index % cardsPerRow
      const x = startX + col * (cardWidth + cardSpacing)
      const y = this.currentY + row * (cardHeight + 10)

      // Fundo do card com sombra
      this.doc.setFillColor(245, 245, 245) // Sombra
      this.doc.rect(x + 1, y + 1, cardWidth, cardHeight, 'F')

      // Fundo principal do card
      this.doc.setFillColor(255, 255, 255)
      this.doc.rect(x, y, cardWidth, cardHeight, 'F')

      // Borda colorida à esquerda
      this.doc.setFillColor(card.color[0], card.color[1], card.color[2])
      this.doc.rect(x, y, 2, cardHeight, 'F')

      // Borda do card
      this.doc.setDrawColor(229, 231, 235) // gray-200
      this.doc.setLineWidth(0.5)
      this.doc.rect(x, y, cardWidth, cardHeight, 'S')

      // Título do card
      this.doc.setTextColor(75, 85, 99) // gray-600
      this.doc.setFontSize(8)
      this.doc.setFont('helvetica', 'bold')
      this.doc.text(card.title, x + 4, y + 6)

      // Valor principal
      this.doc.setTextColor(card.color[0], card.color[1], card.color[2])
      this.doc.setFontSize(12)
      this.doc.setFont('helvetica', 'bold')

      // Ajustar tamanho da fonte se o texto for muito longo
      const textWidth = this.doc.getTextWidth(card.value)
      if (textWidth > cardWidth - 8) {
        this.doc.setFontSize(10)
      }

      this.doc.text(card.value, x + 4, y + 14)

      // Subtítulo
      this.doc.setTextColor(107, 114, 128) // gray-500
      this.doc.setFontSize(7)
      this.doc.setFont('helvetica', 'normal')

      // Quebrar texto do subtítulo se necessário
      const subtitleLines = this.doc.splitTextToSize(card.subtitle, cardWidth - 8)
      this.doc.text(subtitleLines[0], x + 4, y + 20)
    })

    // Atualizar currentY baseado no número de linhas de cards
    const rows = Math.ceil(cards.length / cardsPerRow)
    this.currentY += rows * (cardHeight + 10)
  }

  private addTable(borderos: BorderoData[]): void {
    const tableData = borderos.map(bordero => [
      bordero.bordero_cod,
      bordero.nome_empresa.substring(0, 35) + (bordero.nome_empresa.length > 35 ? '...' : ''),
      formatCurrency(bordero.valor),
      this.getStatusDisplay(bordero.status),
      bordero.secretaria.nome.substring(0, 20) + (bordero.secretaria.nome.length > 20 ? '...' : ''),
      bordero.tipo?.nome || 'N/A',
      new Date(bordero.data).toLocaleDateString('pt-BR')
    ])

    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Código', 'Empresa', 'Valor (R$)', 'Status', 'Secretaria', 'Tipo', 'Data']],
      body: tableData,
      theme: 'grid',
      styles: {
        fontSize: 9,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.5,
        textColor: [30, 41, 59] // slate-800
      },
      headStyles: {
        fillColor: [54, 86, 229], // #3656e5 - cor institucional
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 10,
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252] // slate-50
      },
      columnStyles: {
        0: { cellWidth: 15, halign: 'center' }, // Código
        1: { cellWidth: 55 }, // Empresa (maior)
        2: { cellWidth: 25, halign: 'right' }, // Valor
        3: { cellWidth: 18, halign: 'center' }, // Status
        4: { cellWidth: 30 }, // Secretaria
        //5: { cellWidth: 20 }, // Tipo
        5: { cellWidth: 20, halign: 'center' } // Data
      },
      margin: { left: this.margin, right: this.margin },
      didDrawPage: () => {
        this.addPageNumber()
      }
    })
  }

  private getStatusDisplay(status: string): string {
    const statusMap: Record<string, string> = {
      'novo': 'Novo',
      'analise': 'Análise',
      'assinado': 'Assinado',
      'pago': 'Pago',
      'corrigir': 'Corrigir',
      'cancelado': 'Cancelado'
    }
    return statusMap[status] || status
  }

  private addFooter(): void {
    const pageCount = this.doc.getNumberOfPages()

    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i)

      // Fundo do rodapé
      this.doc.setFillColor(248, 250, 252) // slate-50
      this.doc.rect(0, this.pageHeight - 25, this.pageWidth, 25, 'F')

      // Linha separadora superior
      this.doc.setDrawColor(54, 86, 229)
      this.doc.setLineWidth(0.5)
      this.doc.line(this.margin, this.pageHeight - 25, this.pageWidth - this.margin, this.pageHeight - 25)

      // Texto institucional à esquerda
      this.doc.setFontSize(8)
      this.doc.setFont('helvetica', 'normal')
      this.doc.setTextColor(71, 85, 105) // slate-600
      this.doc.text('Gerado via CRM de Borderôs - Prefeitura Municipal', this.margin, this.pageHeight - 15)

      // Página no centro
      this.doc.setFont('helvetica', 'bold')
      this.doc.text(`Página ${i} de ${pageCount}`, this.pageWidth / 2 - 15, this.pageHeight - 15)

      // Data/hora à direita
      this.doc.setFont('helvetica', 'normal')
      const agora = new Date()
      this.doc.text(`${agora.toLocaleDateString('pt-BR')} - ${agora.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`, this.pageWidth - this.margin - 45, this.pageHeight - 15)

      // Linha de identificação
      this.doc.setFontSize(7)
      this.doc.setTextColor(100, 116, 139) // slate-500
      this.doc.text('Documento gerado automaticamente pelo sistema', this.pageWidth / 2 - 35, this.pageHeight - 8)
    }
  }

  private addPageNumber(): void {
    const pageNumber = this.doc.getCurrentPageInfo().pageNumber
    const totalPages = this.doc.getNumberOfPages()

    this.doc.setFontSize(8)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(128, 128, 128)
    this.doc.text(
      `Página ${pageNumber} de ${totalPages}`,
      this.pageWidth - this.margin - 20,
      this.pageHeight - 10
    )
  }

  save(filename: string = 'relatorio-borderos.pdf'): void {
    this.doc.save(filename)
  }

  getBlob(): Blob {
    return this.doc.output('blob')
  }

  getDataUri(): string {
    return this.doc.output('datauristring')
  }
}
