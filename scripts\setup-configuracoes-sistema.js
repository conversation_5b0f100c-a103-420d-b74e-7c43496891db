const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function setupConfiguracoesSistema() {
  try {
    console.log('🔧 Configurando tabela configuracoes_sistema...\n')

    // 1. Verificar se a tabela existe
    console.log('1. Verificando se a tabela configuracoes_sistema existe...')
    const { data: existingData, error: selectError } = await supabase
      .from('configuracoes_sistema')
      .select('*')
      .limit(1)

    if (selectError && selectError.code === '42P01') {
      console.log('❌ Tabela configuracoes_sistema não existe')
      console.log('🔧 Criando tabela configuracoes_sistema...')
      
      // Criar a tabela usando RPC ou SQL direto
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS configuracoes_sistema (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
          modo_escuro BOOLEAN DEFAULT false,
          notificacoes_email BOOLEAN DEFAULT true,
          logo_url TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Inserir configuração padrão se não existir
        INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
        SELECT 'CRM de Bordero', false, true
        WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);
      `

      // Tentar executar via RPC se disponível
      try {
        const { error: rpcError } = await supabase.rpc('exec_sql', { sql: createTableSQL })
        if (rpcError) {
          console.log('⚠️ RPC não disponível, tentando inserção direta...')
          throw rpcError
        }
        console.log('✅ Tabela criada via RPC')
      } catch (rpcError) {
        // Se RPC não funcionar, tentar inserção direta para forçar criação
        console.log('🔧 Tentando forçar criação da tabela via inserção...')
        
        const { error: insertError } = await supabase
          .from('configuracoes_sistema')
          .insert({
            nome: 'CRM de Bordero',
            modo_escuro: false,
            notificacoes_email: true
          })

        if (insertError) {
          console.error('❌ Erro ao criar tabela:', insertError.message)
          console.log('\n📋 SQL para executar manualmente no Supabase:')
          console.log(createTableSQL)
          return
        }
        console.log('✅ Tabela criada via inserção')
      }
    } else if (selectError) {
      console.error('❌ Erro ao verificar tabela:', selectError.message)
      return
    } else {
      console.log('✅ Tabela configuracoes_sistema já existe')
    }

    // 2. Verificar dados existentes
    console.log('\n2. Verificando dados existentes...')
    const { data: configs, error: dataError } = await supabase
      .from('configuracoes_sistema')
      .select('*')

    if (dataError) {
      console.error('❌ Erro ao buscar configurações:', dataError.message)
      return
    }

    if (configs.length === 0) {
      console.log('❌ Nenhuma configuração encontrada')
      console.log('🔧 Inserindo configuração padrão...')
      
      const { data: newConfig, error: insertError } = await supabase
        .from('configuracoes_sistema')
        .insert({
          nome: 'CRM de Bordero',
          modo_escuro: false,
          notificacoes_email: true
        })
        .select()
        .single()

      if (insertError) {
        console.error('❌ Erro ao inserir configuração padrão:', insertError.message)
        return
      }

      console.log('✅ Configuração padrão inserida:', newConfig)
    } else {
      console.log('✅ Configurações encontradas:')
      configs.forEach(config => {
        console.log(`   - ID: ${config.id}`)
        console.log(`   - Nome: ${config.nome}`)
        console.log(`   - Modo Escuro: ${config.modo_escuro}`)
        console.log(`   - Notificações Email: ${config.notificacoes_email}`)
      })
    }

    // 3. Testar API
    console.log('\n3. Testando API de configurações...')
    
    // Testar GET
    const getResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/configuracoes/sistema`)
    if (getResponse.ok) {
      const getData = await getResponse.json()
      console.log('✅ GET /api/configuracoes/sistema funcionando:', getData)
    } else {
      console.log('❌ Erro no GET:', getResponse.status, await getResponse.text())
    }

    // Testar PUT
    const putResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/configuracoes/sistema`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        nome: 'CRM de Bordero - Teste',
        modoEscuro: false,
        notificacoesEmail: true
      })
    })

    if (putResponse.ok) {
      const putData = await putResponse.json()
      console.log('✅ PUT /api/configuracoes/sistema funcionando:', putData)
    } else {
      console.log('❌ Erro no PUT:', putResponse.status, await putResponse.text())
    }

    console.log('\n🎉 Configuração da tabela configuracoes_sistema concluída!')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

setupConfiguracoesSistema()
