import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Buscar dados do usuário na tabela usuarios
    const { data: usuario, error } = await supabase
      .from("usuarios")
      .select("nome, email, avatar_url")
      .eq("id", session.user.id)
      .single()

    if (error) {
      console.error("Erro ao buscar dados do usuário:", error)
      return NextResponse.json({ error: "Erro ao buscar dados do usuário" }, { status: 500 })
    }

    return NextResponse.json({
      nome: usuario?.nome || session.user.user_metadata?.full_name || "",
      email: session.user.email || "",
      avatar: usuario?.avatar_url || "",
    })
  } catch (error) {
    console.error("Erro ao buscar perfil:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { nome, avatar, senhaAtual, novaSenha } = body

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar Service Role para atualizar dados na tabela usuarios
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Atualizar dados na tabela usuarios
    const { error: updateError } = await supabaseAdmin
      .from("usuarios")
      .update({
        nome,
        avatar_url: avatar,
        updated_at: new Date().toISOString()
      })
      .eq("id", session.user.id)

    if (updateError) {
      console.error("Erro ao atualizar dados do usuário:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar dados do usuário" }, { status: 500 })
    }

    // Registrar no log do sistema
    try {
      await supabaseAdmin
        .from("sistema_logs")
        .insert({
          usuario_id: session.user.id,
          acao: "update",
          entidade: "perfil",
          detalhes: {
            nome_alterado: nome,
            avatar_alterado: !!avatar,
            senha_alterada: !!(novaSenha && senhaAtual)
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    // Se foi fornecida uma nova senha, atualizar no Supabase Auth
    if (novaSenha && senhaAtual) {
      try {
        // Atualizar a senha diretamente (o Supabase Auth já verifica a sessão atual)
        const { error: passwordError } = await supabase.auth.updateUser({
          password: novaSenha
        })

        if (passwordError) {
          console.error("Erro ao atualizar senha:", passwordError)
          return NextResponse.json({ error: "Erro ao atualizar senha: " + passwordError.message }, { status: 500 })
        }
      } catch (error) {
        console.error("Erro ao processar atualização de senha:", error)
        return NextResponse.json({ error: "Erro ao processar atualização de senha" }, { status: 500 })
      }
    }

    return NextResponse.json({ message: "Perfil atualizado com sucesso" })
  } catch (error) {
    console.error("Erro ao atualizar perfil:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
