const fetch = require('node-fetch')

async function testApiCall() {
  try {
    console.log('🔍 Testando chamada da API de atualização de status...')

    const borderoId = '3137f0c5-e2f8-405c-a797-a8bd6be55aef'
    const url = `http://localhost:3000/api/borderos/${borderoId}/status`

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: 'novo',
        dadosStatus: null,
        usuarioId: 'test-user-id'
      })
    })

    console.log('Status da resposta:', response.status)
    console.log('Headers da resposta:', Object.fromEntries(response.headers.entries()))

    const responseText = await response.text()
    console.log('Corpo da resposta:', responseText)

    if (response.ok) {
      console.log('✅ API funcionou corretamente')
    } else {
      console.log('❌ API retornou erro')
    }

  } catch (error) {
    console.error('❌ Erro ao chamar API:', error.message)
  }
}

testApiCall()
