-- Script para verificar e corrigir a estrutura da tabela usuarios
-- Execute este script no Supabase SQL Editor

-- 1. Verificar se a tabela usuarios existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'usuarios'
  ) THEN
    RAISE NOTICE 'Tabela usuarios não existe. Criando...';
    
    -- <PERSON>riar tabela de usuários
    CREATE TABLE usuarios (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      nome VARCHAR(255) NOT NULL,
      nivel_acesso_id UUID REFERENCES niveis_acesso(id),
      avatar_url TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Tabela usuarios criada com sucesso';
  ELSE
    RAISE NOTICE 'Tabela usuarios já existe';
  END IF;
END
$$;

-- 2. Verificar se a tabela niveis_acesso existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'niveis_acesso'
  ) THEN
    RAISE NOTICE 'Tabela niveis_acesso não existe. Criando...';
    
    -- Criar tabela de níveis de acesso
    CREATE TABLE niveis_acesso (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      nome VARCHAR(100) NOT NULL,
      descricao TEXT,
      permissoes JSONB NOT NULL DEFAULT '{}',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Tabela niveis_acesso criada com sucesso';
  ELSE
    RAISE NOTICE 'Tabela niveis_acesso já existe';
  END IF;
END
$$;

-- 3. Inserir níveis de acesso padrão se não existirem
INSERT INTO niveis_acesso (nome, descricao, permissoes) 
VALUES 
  (
    'Administrador',
    'Acesso completo ao sistema',
    '{
      "dashboard": true,
      "borderos": true,
      "secretarias": true,
      "direcionamentos": true,
      "tipos": true,
      "usuarios": true,
      "relatorios": true,
      "configuracoes": true
    }'::jsonb
  ),
  (
    'Operador',
    'Acesso para operações básicas',
    '{
      "dashboard": true,
      "borderos": true,
      "secretarias": false,
      "direcionamentos": false,
      "tipos": false,
      "usuarios": false,
      "relatorios": true,
      "configuracoes": false
    }'::jsonb
  ),
  (
    'Visualizador',
    'Apenas visualização de dados',
    '{
      "dashboard": true,
      "borderos": false,
      "secretarias": false,
      "direcionamentos": false,
      "tipos": false,
      "usuarios": false,
      "relatorios": true,
      "configuracoes": false
    }'::jsonb
  )
ON CONFLICT (nome) DO NOTHING;

-- 4. Verificar se a tabela log_atividades existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'log_atividades'
  ) THEN
    RAISE NOTICE 'Tabela log_atividades não existe. Criando...';
    
    CREATE TABLE log_atividades (
      id SERIAL PRIMARY KEY,
      usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
      acao VARCHAR(255) NOT NULL,
      entidade VARCHAR(255) NOT NULL,
      entidade_id VARCHAR(255),
      detalhes JSONB,
      ip VARCHAR(45),
      user_agent TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Tabela log_atividades criada com sucesso';
  ELSE
    RAISE NOTICE 'Tabela log_atividades já existe';
  END IF;
END
$$;

-- 5. Habilitar RLS nas tabelas
ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE niveis_acesso ENABLE ROW LEVEL SECURITY;
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;

-- 6. Remover políticas existentes
DROP POLICY IF EXISTS "usuarios_policy" ON usuarios;
DROP POLICY IF EXISTS "niveis_acesso_policy" ON niveis_acesso;
DROP POLICY IF EXISTS "log_atividades_policy" ON log_atividades;

-- 7. Criar políticas RLS (permitir tudo para usuários autenticados)
CREATE POLICY "usuarios_policy" ON usuarios FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "niveis_acesso_policy" ON niveis_acesso FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "log_atividades_policy" ON log_atividades FOR ALL USING (auth.role() = 'authenticated');

-- 8. Criar função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. Criar triggers para atualizar updated_at
DROP TRIGGER IF EXISTS update_usuarios_updated_at ON usuarios;
DROP TRIGGER IF EXISTS update_niveis_acesso_updated_at ON niveis_acesso;

CREATE TRIGGER update_usuarios_updated_at 
  BEFORE UPDATE ON usuarios 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_niveis_acesso_updated_at 
  BEFORE UPDATE ON niveis_acesso 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Verificar estrutura final
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name IN ('usuarios', 'niveis_acesso', 'log_atividades')
ORDER BY table_name, ordinal_position;

-- 11. Mostrar dados existentes
SELECT 'usuarios' as tabela, count(*) as registros FROM usuarios
UNION ALL
SELECT 'niveis_acesso' as tabela, count(*) as registros FROM niveis_acesso
UNION ALL
SELECT 'log_atividades' as tabela, count(*) as registros FROM log_atividades;
