import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { nome, valor_minimo, valor_maximo, usuarios } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome do direcionamento é obrigatório" }, { status: 400 })
    }

    if (valor_minimo === undefined || valor_maximo === undefined) {
      return NextResponse.json({ error: "Valores mínimo e máximo são obrigatórios" }, { status: 400 })
    }

    if (valor_minimo >= valor_maximo) {
      return NextResponse.json({ error: "O valor máximo deve ser maior que o valor mínimo" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe um direcionamento com este nome (exceto o atual)
    const { data: existingDirecionamento, error: checkError } = await supabase
      .from("direcionamentos")
      .select("id")
      .eq("nome", nome.trim())
      .neq("id", id)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar direcionamento:", checkError)
      return NextResponse.json({ error: "Erro ao verificar direcionamento" }, { status: 500 })
    }

    if (existingDirecionamento) {
      return NextResponse.json({ error: "Já existe um direcionamento com este nome" }, { status: 400 })
    }

    // Verificar se há sobreposição de valores com outros direcionamentos (exceto o atual)
    // Permitir sobreposição se for confirmado pelo usuário
    const { data: overlappingDirecionamentos, error: overlapError } = await supabase
      .from("direcionamentos")
      .select("id, nome, valor_minimo, valor_maximo")
      .neq("id", id)
      .or(`and(valor_minimo.lte.${valor_maximo},valor_maximo.gte.${valor_minimo})`)

    if (overlapError) {
      console.error("Erro ao verificar sobreposição:", overlapError)
      return NextResponse.json({ error: "Erro ao verificar sobreposição de valores" }, { status: 500 })
    }

    // Apenas avisar sobre sobreposição, mas permitir a edição
    let warningMessage = null
    if (overlappingDirecionamentos && overlappingDirecionamentos.length > 0) {
      warningMessage = `Atenção: Há sobreposição de valores com o direcionamento "${overlappingDirecionamentos[0].nome}"`
      console.warn(warningMessage)
    }

    const { data: direcionamento, error } = await supabase
      .from("direcionamentos")
      .update({
        nome: nome.trim(),
        valor_minimo,
        valor_maximo,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Erro ao atualizar direcionamento:", error)
      return NextResponse.json({ error: "Erro ao atualizar direcionamento" }, { status: 500 })
    }

    // Atualizar associações de usuários
    if (usuarios !== undefined) {
      // Remover associações existentes
      await supabase
        .from("direcionamento_usuarios")
        .delete()
        .eq("direcionamento_id", id)

      // Criar novas associações
      if (usuarios.length > 0) {
        const associacoes = usuarios.map((usuarioId: string) => ({
          direcionamento_id: id,
          usuario_id: usuarioId
        }))

        const { error: associacaoError } = await supabase
          .from("direcionamento_usuarios")
          .insert(associacoes)

        if (associacaoError) {
          console.error("Erro ao atualizar associações:", associacaoError)
          // Não falhar a atualização do direcionamento por causa das associações
        }
      }
    }

    return NextResponse.json({
      ...direcionamento,
      warning: warningMessage
    })
  } catch (error) {
    console.error("Erro ao atualizar direcionamento:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // Obter usuário autenticado
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar dados do direcionamento antes de excluir
    const { data: direcionamento, error: fetchError } = await supabase
      .from("direcionamentos")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError || !direcionamento) {
      return NextResponse.json({ error: "Direcionamento não encontrado" }, { status: 404 })
    }

    // Nota: A verificação de borderos associados foi removida pois a coluna direcionamento_id
    // não existe na tabela borderos. Em uma implementação futura, essa verificação pode ser adicionada.

    // Remover associações de usuários primeiro
    await supabase
      .from("direcionamento_usuarios")
      .delete()
      .eq("direcionamento_id", id)

    // Remover o direcionamento
    const { error } = await supabase
      .from("direcionamentos")
      .delete()
      .eq("id", id)

    if (error) {
      console.error("Erro ao excluir direcionamento:", error)
      return NextResponse.json({ error: "Erro ao excluir direcionamento" }, { status: 500 })
    }

    // Registrar log de exclusão
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: user.id,
          acao: "excluir",
          entidade: "direcionamento",
          entidade_id: id,
          detalhes: {
            nome: direcionamento.nome,
            descricao: direcionamento.descricao
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exclusão:", logError)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao excluir direcionamento:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
