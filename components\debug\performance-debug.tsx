"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { usePerformanceMonitor } from "@/lib/performance-monitor"
import { Activity, Clock, Zap } from "lucide-react"

export function PerformanceDebug() {
  const { getReport, logReport, clear } = usePerformanceMonitor()
  const [report, setReport] = useState<Record<string, { avg: number, count: number, max: number }>>({})
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setReport(getReport())
    }, 2000)

    return () => clearInterval(interval)
  }, [getReport])

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const getStatusColor = (avg: number) => {
    if (avg < 500) return "bg-green-100 text-green-800"
    if (avg < 2000) return "bg-yellow-100 text-yellow-800"
    return "bg-red-100 text-red-800"
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-white shadow-lg"
        >
          <Activity className="h-4 w-4 mr-2" />
          Performance
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Performance Monitor
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.keys(report).length === 0 ? (
            <p className="text-sm text-muted-foreground">
              Nenhuma métrica coletada ainda...
            </p>
          ) : (
            Object.entries(report).map(([label, metrics]) => (
              <div key={label} className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium">{label}</span>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getStatusColor(metrics.avg)}`}
                  >
                    {formatTime(metrics.avg)}
                  </Badge>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Max: {formatTime(metrics.max)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    Count: {metrics.count}
                  </div>
                </div>
              </div>
            ))
          )}
          
          <div className="flex gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={logReport}
              className="flex-1 text-xs"
            >
              Log Console
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clear}
              className="flex-1 text-xs"
            >
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Hook para mostrar/ocultar o debug
export function usePerformanceDebug() {
  const [isEnabled, setIsEnabled] = useState(false)

  useEffect(() => {
    // Habilitar debug em desenvolvimento ou com parâmetro URL
    const isDev = process.env.NODE_ENV === 'development'
    const hasDebugParam = typeof window !== 'undefined' && 
      new URLSearchParams(window.location.search).has('debug')
    
    setIsEnabled(isDev || hasDebugParam)
  }, [])

  return { isEnabled, setIsEnabled }
}
