"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { RefreshCw, Search, User, DollarSign, Shield } from "lucide-react"

interface DebugResult {
  usuario: {
    id: string
    nome: string
    email: string
  }
  direcionamentos_usuario: any[]
  limite_valor: number | null
  todos_direcionamentos: any[]
  debug_info: {
    total_direcionamentos_usuario: number
    total_direcionamentos_sistema: number
    tem_limite: boolean
    valor_limite_formatado: string
  }
}

export default function DebugDirecionamentosPage() {
  const [userId, setUserId] = useState("")
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<DebugResult | null>(null)
  const [error, setError] = useState("")

  const fetchDebugInfo = async (targetUserId?: string) => {
    setLoading(true)
    setError("")
    
    try {
      const url = targetUserId 
        ? `/api/debug/direcionamentos?userId=${targetUserId}`
        : `/api/debug/direcionamentos`
      
      const response = await fetch(url)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao buscar informações")
      }
      
      const data = await response.json()
      setResult(data)
    } catch (err: any) {
      setError(err.message)
      setResult(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Carregar informações do usuário atual ao montar o componente
    fetchDebugInfo()
  }, [])

  const handleSearch = () => {
    if (userId.trim()) {
      fetchDebugInfo(userId.trim())
    } else {
      fetchDebugInfo()
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Debug - Direcionamentos</h1>
        <Button onClick={() => fetchDebugInfo()} disabled={loading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </div>

      {/* Busca por usuário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Buscar Usuário
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="userId">ID do Usuário (deixe vazio para usuário atual)</Label>
              <Input
                id="userId"
                placeholder="Digite o ID do usuário..."
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            <div className="flex items-end">
              <Button onClick={handleSearch} disabled={loading}>
                <Search className="mr-2 h-4 w-4" />
                Buscar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Erro */}
      {error && (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-red-600">
              <strong>Erro:</strong> {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resultado */}
      {result && (
        <div className="space-y-6">
          {/* Informações do Usuário */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações do Usuário
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>ID</Label>
                  <div className="font-mono text-sm">{result.usuario.id}</div>
                </div>
                <div>
                  <Label>Nome</Label>
                  <div>{result.usuario.nome}</div>
                </div>
                <div>
                  <Label>Email</Label>
                  <div>{result.usuario.email}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resumo dos Limites */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Resumo dos Limites
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Status do Limite</Label>
                  <Badge variant={result.debug_info.tem_limite ? "destructive" : "secondary"}>
                    {result.debug_info.tem_limite ? "COM LIMITE" : "SEM LIMITE"}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <Label>Valor Máximo Permitido</Label>
                  <div className="text-lg font-semibold text-green-600">
                    {result.debug_info.valor_limite_formatado}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Direcionamentos do Usuário */}
          <Card>
            <CardHeader>
              <CardTitle>Direcionamentos do Usuário ({result.debug_info.total_direcionamentos_usuario})</CardTitle>
            </CardHeader>
            <CardContent>
              {result.direcionamentos_usuario.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Usuário não possui direcionamentos configurados
                </div>
              ) : (
                <div className="space-y-4">
                  {result.direcionamentos_usuario.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>Nome</Label>
                          <div className="font-medium">{item.direcionamento?.nome || "N/A"}</div>
                        </div>
                        <div>
                          <Label>Valor Mínimo</Label>
                          <div>{formatCurrency(item.direcionamento?.valor_minimo || 0)}</div>
                        </div>
                        <div>
                          <Label>Valor Máximo</Label>
                          <div className="font-semibold text-green-600">
                            {formatCurrency(item.direcionamento?.valor_maximo || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Todos os Direcionamentos */}
          <Card>
            <CardHeader>
              <CardTitle>Todos os Direcionamentos do Sistema ({result.debug_info.total_direcionamentos_sistema})</CardTitle>
            </CardHeader>
            <CardContent>
              {result.todos_direcionamentos.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Nenhum direcionamento configurado no sistema
                </div>
              ) : (
                <div className="space-y-4">
                  {result.todos_direcionamentos.map((dir) => (
                    <div key={dir.id} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label>Nome</Label>
                          <div className="font-medium">{dir.nome}</div>
                        </div>
                        <div>
                          <Label>Valor Mínimo</Label>
                          <div>{formatCurrency(dir.valor_minimo)}</div>
                        </div>
                        <div>
                          <Label>Valor Máximo</Label>
                          <div className="font-semibold text-green-600">
                            {formatCurrency(dir.valor_maximo)}
                          </div>
                        </div>
                        <div>
                          <Label>Usuários ({dir.usuarios?.length || 0})</Label>
                          <div className="space-y-1">
                            {dir.usuarios?.map((u: any) => (
                              <Badge key={u.usuario.id} variant="outline" className="text-xs">
                                {u.usuario.nome}
                              </Badge>
                            )) || <span className="text-muted-foreground text-sm">Nenhum</span>}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
