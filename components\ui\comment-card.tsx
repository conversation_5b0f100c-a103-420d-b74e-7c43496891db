"use client"

import { useState } from "react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MoreVertical, Edit, Trash2, Save, X } from "lucide-react"
import { safeFormatDistanceToNow } from "@/lib/date-utils"
import { EmojiPicker } from "@/components/ui/emoji-picker"

interface Comentario {
  id: string
  comentario: string
  mencoes: string[]
  created_at: string
  updated_at?: string
  usuario: {
    id: string
    nome: string
    email: string
  }
}

interface CommentCardProps {
  comentario: Comentario
  currentUserId: string
  onEdit: (id: string, novoTexto: string) => Promise<void>
  onDelete: (id: string) => Promise<void>
}

export function CommentCard({ comentario, currentUserId, onEdit, onDelete }: CommentCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState(comentario.comentario)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const isAuthor = comentario.usuario.id === currentUserId
  const hasBeenEdited = comentario.updated_at && comentario.updated_at !== comentario.created_at

  const getInitials = (nome: string) => {
    return nome.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const formatarComentarioComMencoes = (texto: string) => {
    return texto.replace(/@(\w+)/g, '<span class="text-blue-600 font-medium">@$1</span>')
  }

  const handleSaveEdit = async () => {
    if (editText.trim() === comentario.comentario) {
      setIsEditing(false)
      return
    }

    if (!editText.trim()) {
      return
    }

    setIsSaving(true)
    try {
      await onEdit(comentario.id, editText.trim())
      setIsEditing(false)
    } catch (error) {
      console.error('Erro ao editar comentário:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancelEdit = () => {
    setEditText(comentario.comentario)
    setIsEditing(false)
  }

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onDelete(comentario.id)
      setDeleteDialogOpen(false)
    } catch (error) {
      console.error('Erro ao excluir comentário:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    if (isEditing) {
      setEditText(prev => prev + emoji)
    }
  }

  return (
    <>
      <div className="flex gap-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="text-xs">
            {getInitials(comentario.usuario.nome)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{comentario.usuario.nome}</span>
            <span className="text-xs text-gray-500">
              {safeFormatDistanceToNow(comentario.created_at)}
            </span>
            {hasBeenEdited && (
              <Badge variant="outline" className="text-xs">
                editado
              </Badge>
            )}
            {comentario.mencoes && comentario.mencoes.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {comentario.mencoes.length} menção(ões)
              </Badge>
            )}
            
            {isAuthor && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-auto">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsEditing(true)}>
                    <Edit className="h-3 w-3 mr-2" />
                    Editar
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setDeleteDialogOpen(true)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-3 w-3 mr-2" />
                    Excluir
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          {isEditing ? (
            <div className="space-y-2">
              <div className="relative">
                <Textarea
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  className="min-h-[80px] pr-10"
                  placeholder="Edite seu comentário..."
                />
                <div className="absolute bottom-2 right-2">
                  <EmojiPicker onEmojiSelect={handleEmojiSelect}>
                    <Button variant="ghost" size="sm" type="button" className="h-6 w-6 p-0">
                      <span className="text-sm">😀</span>
                    </Button>
                  </EmojiPicker>
                </div>
              </div>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  onClick={handleSaveEdit}
                  disabled={isSaving || !editText.trim()}
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="h-3 w-3 mr-1" />
                      Salvar
                    </>
                  )}
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                >
                  <X className="h-3 w-3 mr-1" />
                  Cancelar
                </Button>
              </div>
            </div>
          ) : (
            <div 
              className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-sm"
              dangerouslySetInnerHTML={{ 
                __html: formatarComentarioComMencoes(comentario.comentario) 
              }}
            />
          )}
        </div>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir comentário</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este comentário? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                  Excluindo...
                </>
              ) : (
                'Excluir'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
