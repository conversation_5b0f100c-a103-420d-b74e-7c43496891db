"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/hooks/use-toast"
import { CheckCircle, XCircle, Loader2, Database, Settings, Search, AlertTriangle } from "lucide-react"

export default function SetupPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])
  const [verificationResult, setVerificationResult] = useState<any>(null)
  const [verifying, setVerifying] = useState(false)

  const setupSteps = [
    {
      id: "configuracoes-sistema",
      title: "Configurações do Sistema",
      description: "Criar tabela configuracoes_sistema",
      endpoint: "/api/setup-configuracoes-sistema"
    },
    {
      id: "logs",
      title: "<PERSON>stema de Logs",
      description: "Criar tabelas de log de atividades",
      endpoint: "/api/setup-logs-tables"
    }
  ]

  const runSetup = async (step: any) => {
    setLoading(true)
    try {
      const response = await fetch(step.endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      setResults(prev => [
        ...prev.filter(r => r.id !== step.id),
        {
          id: step.id,
          title: step.title,
          success: response.ok && data.success !== false,
          message: data.message || (response.ok ? "Configurado com sucesso" : "Erro na configuração"),
          sql: data.sql,
          error: data.error
        }
      ])

      if (response.ok && data.success !== false) {
        toast({
          title: "Sucesso",
          description: `${step.title} configurado com sucesso`,
        })
      } else {
        toast({
          title: "Atenção",
          description: data.message || "Verifique os detalhes abaixo",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      setResults(prev => [
        ...prev.filter(r => r.id !== step.id),
        {
          id: step.id,
          title: step.title,
          success: false,
          message: error.message || "Erro inesperado",
          error: error.message
        }
      ])

      toast({
        title: "Erro",
        description: `Erro ao configurar ${step.title}`,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const runAllSetup = async () => {
    for (const step of setupSteps) {
      await runSetup(step)
      // Aguardar um pouco entre as execuções
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  const getResult = (stepId: string) => {
    return results.find(r => r.id === stepId)
  }

  const verifyTables = async () => {
    setVerifying(true)
    try {
      const response = await fetch("/api/setup-verify-tables")
      const data = await response.json()

      setVerificationResult(data)

      if (data.success) {
        toast({
          title: "Verificação Concluída",
          description: `${data.summary.existing}/${data.summary.total} tabelas funcionando corretamente`,
        })
      } else {
        toast({
          title: "Problemas Encontrados",
          description: `${data.summary.missing} tabelas ausentes, ${data.summary.errors} erros`,
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Erro na Verificação",
        description: error.message,
        variant: "destructive"
      })
    } finally {
      setVerifying(false)
    }
  }

  const fixTables = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/setup-verify-tables", {
        method: "POST"
      })
      const data = await response.json()

      if (data.success) {
        toast({
          title: "Correção Executada",
          description: "Tentativa de criação de tabelas concluída",
        })
        // Verificar novamente após a correção
        await verifyTables()
      } else {
        toast({
          title: "Erro na Correção",
          description: data.error || "Erro ao tentar corrigir tabelas",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Setup do Sistema</h1>
          <p className="text-muted-foreground">
            Configure as tabelas e funcionalidades necessárias do sistema
          </p>
        </div>
        <Button onClick={runAllSetup} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Settings className="mr-2 h-4 w-4" />
          Executar Tudo
        </Button>
      </div>

      {/* Card de Verificação de Tabelas */}
      <Card className="border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/30">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Search className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <div>
                <CardTitle className="text-lg text-blue-900 dark:text-blue-100">Verificação de Tabelas</CardTitle>
                <CardDescription className="dark:text-blue-200">Verificar se todas as tabelas do banco estão funcionando</CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {verificationResult && (
                <Badge variant={verificationResult.success ? "default" : "destructive"}>
                  {verificationResult.success ? (
                    <CheckCircle className="mr-1 h-3 w-3" />
                  ) : (
                    <AlertTriangle className="mr-1 h-3 w-3" />
                  )}
                  {verificationResult.success ? "Tudo OK" : "Problemas"}
                </Badge>
              )}
              <Button
                onClick={verifyTables}
                disabled={verifying || loading}
                size="sm"
                variant="outline"
              >
                {verifying && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Verificar
              </Button>
              {verificationResult && !verificationResult.success && (
                <Button
                  onClick={fixTables}
                  disabled={loading || verifying}
                  size="sm"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Corrigir
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        {verificationResult && (
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-4 gap-4 text-center">
                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{verificationResult.summary.total}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total</div>
                </div>
                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">{verificationResult.summary.existing}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Funcionando</div>
                </div>
                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">{verificationResult.summary.missing}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Ausentes</div>
                </div>
                <div className="p-3 bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{verificationResult.summary.errors}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Erros</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium dark:text-gray-200">Status das Tabelas:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(verificationResult.tables).map(([tableName, tableInfo]: [string, any]) => (
                    <div key={tableName} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border dark:border-gray-700">
                      <span className="text-sm font-mono dark:text-gray-300">{tableName}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">({tableInfo.records} registros)</span>
                        <Badge variant={tableInfo.status === 'ok' ? "default" : "destructive"} className="text-xs">
                          {tableInfo.status === 'ok' ? (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          ) : (
                            <XCircle className="mr-1 h-3 w-3" />
                          )}
                          {tableInfo.status === 'ok' ? 'OK' : 'Erro'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="grid gap-6">
        {setupSteps.map((step) => {
          const result = getResult(step.id)
          return (
            <Card key={step.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Database className="h-5 w-5" />
                    <div>
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                      <CardDescription>{step.description}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {result && (
                      <Badge variant={result.success ? "default" : "destructive"}>
                        {result.success ? (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        ) : (
                          <XCircle className="mr-1 h-3 w-3" />
                        )}
                        {result.success ? "Sucesso" : "Erro"}
                      </Badge>
                    )}
                    <Button
                      onClick={() => runSetup(step)}
                      disabled={loading}
                      size="sm"
                    >
                      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Executar
                    </Button>
                  </div>
                </div>
              </CardHeader>
              {result && (
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-sm">
                      <strong>Resultado:</strong> {result.message}
                    </div>

                    {result.error && (
                      <div className="text-sm text-red-600">
                        <strong>Erro:</strong> {result.error}
                      </div>
                    )}

                    {result.sql && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">
                          SQL para executar manualmente no Supabase:
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md">
                          <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                            {result.sql}
                          </pre>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Copie e cole este SQL no SQL Editor do Supabase Dashboard
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          )
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Instruções</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm">
            <p><strong>1.</strong> Execute cada configuração clicando no botão "Executar" ou use "Executar Tudo"</p>
            <p><strong>2.</strong> Se aparecer SQL para execução manual, copie e cole no SQL Editor do Supabase</p>
            <p><strong>3.</strong> Após executar o SQL manualmente, execute a configuração novamente para verificar</p>
            <p><strong>4.</strong> Todas as configurações devem mostrar "Sucesso" antes de usar o sistema</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
