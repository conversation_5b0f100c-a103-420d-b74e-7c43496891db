import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET() {
  try {
    // Verificar se o usuário está autenticado
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    const {
      data: { session },
    } = await supabase.auth.getSession()

    // Retornar as chaves do Pusher (mesmo sem autenticação para fins de desenvolvimento)
    // Em produção, você pode querer restringir isso apenas a usuários autenticados
    return NextResponse.json({
      key: process.env.PUSHER_KEY,
      cluster: process.env.PUSHER_CLUSTER,
    })
  } catch (error) {
    console.error("Erro ao obter chave do Pusher:", error)
    return NextResponse.json({ error: "Erro ao obter chave do Pusher" }, { status: 500 })
  }
}
