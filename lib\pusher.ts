import Pusher from "pusher"
import PusherClient from "pusher-js"

// Configuração do servidor (segura)
export const pusherServer = new Pusher({
  appId: process.env.PUSHER_APP_ID!,
  key: process.env.PUSHER_KEY!,
  secret: process.env.PUSHER_SECRET!,
  cluster: process.env.PUSHER_CLUSTER!,
  useTLS: true,
})

// Função para criar o cliente Pusher (será chamada após obter a chave via API)
export const createPusherClient = (key: string, cluster: string) => {
  return new PusherClient(key, {
    cluster: cluster,
  })
}
