import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

// PUT - Editar comentário
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string; comentarioId: string }> }
) {
  try {
    const { id, comentarioId } = await params
    const body = await request.json()
    const { comentario } = body

    if (!comentario || !comentario.trim()) {
      return NextResponse.json({ error: "Comentário não pode estar vazio" }, { status: 400 })
    }

    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o comentário existe e se o usuário é o autor
    const { data: comentarioExistente, error: fetchError } = await supabase
      .from("bordero_comentarios")
      .select("*, bordero:borderos(bordero_cod)")
      .eq("id", comentarioId)
      .eq("bordero_id", id)
      .single()

    if (fetchError || !comentarioExistente) {
      return NextResponse.json({ error: "Comentário não encontrado" }, { status: 404 })
    }

    if (comentarioExistente.usuario_id !== session.user.id) {
      return NextResponse.json({ error: "Você só pode editar seus próprios comentários" }, { status: 403 })
    }

    // Atualizar comentário
    const { data: comentarioAtualizado, error: updateError } = await supabase
      .from("bordero_comentarios")
      .update({
        comentario: comentario.trim(),
        updated_at: new Date().toISOString()
      })
      .eq("id", comentarioId)
      .select(`
        *,
        usuario:usuarios(id, nome, email)
      `)
      .single()

    if (updateError) {
      console.error("Erro ao atualizar comentário:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar comentário" }, { status: 500 })
    }

    // Registrar no log de atividades
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "editar_comentario",
          entidade: "bordero",
          entidade_id: id,
          detalhes: {
            bordero_cod: comentarioExistente.bordero.bordero_cod,
            comentario_id: comentarioId,
            comentario_anterior: comentarioExistente.comentario.substring(0, 100),
            comentario_novo: comentario.substring(0, 100)
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    // Log de comentário removido - comentários não aparecem mais na timeline

    return NextResponse.json(comentarioAtualizado)
  } catch (error) {
    console.error("Erro na API de edição de comentário:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

// DELETE - Excluir comentário
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string; comentarioId: string }> }
) {
  try {
    const { id, comentarioId } = await params

    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o comentário existe e se o usuário é o autor
    const { data: comentarioExistente, error: fetchError } = await supabase
      .from("bordero_comentarios")
      .select("*, bordero:borderos(bordero_cod)")
      .eq("id", comentarioId)
      .eq("bordero_id", id)
      .single()

    if (fetchError || !comentarioExistente) {
      return NextResponse.json({ error: "Comentário não encontrado" }, { status: 404 })
    }

    if (comentarioExistente.usuario_id !== session.user.id) {
      return NextResponse.json({ error: "Você só pode excluir seus próprios comentários" }, { status: 403 })
    }

    // Excluir comentário
    const { error: deleteError } = await supabase
      .from("bordero_comentarios")
      .delete()
      .eq("id", comentarioId)

    if (deleteError) {
      console.error("Erro ao excluir comentário:", deleteError)
      return NextResponse.json({ error: "Erro ao excluir comentário" }, { status: 500 })
    }

    // Registrar no log de atividades
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "excluir_comentario",
          entidade: "bordero",
          entidade_id: id,
          detalhes: {
            bordero_cod: comentarioExistente.bordero.bordero_cod,
            comentario_id: comentarioId,
            comentario_excluido: comentarioExistente.comentario.substring(0, 100)
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    // Log de comentário removido - comentários não aparecem mais na timeline

    return NextResponse.json({ message: "Comentário excluído com sucesso" })
  } catch (error) {
    console.error("Erro na API de exclusão de comentário:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
