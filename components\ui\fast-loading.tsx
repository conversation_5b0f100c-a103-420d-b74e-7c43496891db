"use client"

import { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"

interface FastLoadingProps {
  message?: string
  timeout?: number
  onTimeout?: () => void
  className?: string
}

export function FastLoading({ 
  message = "Carregando...", 
  timeout = 8000,
  onTimeout,
  className = ""
}: FastLoadingProps) {
  const [showTimeout, setShowTimeout] = useState(false)
  const [dots, setDots] = useState("")

  // Animação dos pontos
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === "...") return ""
        return prev + "."
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  // Timeout
  useEffect(() => {
    if (timeout > 0) {
      const timer = setTimeout(() => {
        setShowTimeout(true)
        onTimeout?.()
      }, timeout)

      return () => clearTimeout(timer)
    }
  }, [timeout, onTimeout])

  return (
    <div className={`flex items-center justify-center min-h-[200px] ${className}`}>
      <div className="text-center">
        <div className="relative">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-3 text-sky-600" />
          {showTimeout && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full animate-pulse" />
          )}
        </div>
        
        <p className="text-sm text-muted-foreground">
          {message}{dots}
        </p>
        
        {showTimeout && (
          <p className="text-xs text-yellow-600 mt-2">
            Carregamento demorado detectado...
          </p>
        )}
      </div>
    </div>
  )
}

// Componente específico para verificação de permissões
export function PermissionLoading({ onTimeout }: { onTimeout?: () => void }) {
  return (
    <FastLoading 
      message="Verificando permissões"
      timeout={8000}
      onTimeout={onTimeout}
    />
  )
}

// Componente específico para autenticação
export function AuthLoading({ onTimeout }: { onTimeout?: () => void }) {
  return (
    <FastLoading 
      message="Verificando autenticação"
      timeout={10000}
      onTimeout={onTimeout}
    />
  )
}
