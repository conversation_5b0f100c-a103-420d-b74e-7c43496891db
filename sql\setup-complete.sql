-- =====================================================
-- SETUP COMPLETO DO SISTEMA CRM DE BORDERO
-- Execute este SQL no Supabase SQL Editor
-- =====================================================

-- 1. CRIAR TABELA CONFIGURACOES_SISTEMA
-- =====================================================
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
  modo_escuro BOOLEAN DEFAULT false,
  notificacoes_email BOOLEAN DEFAULT true,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habilitar RLS
ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir acesso total
DROP POLICY IF EXISTS "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema;
CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
FOR ALL USING (true);

-- Inserir configuração padrão se não existir
INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
SELECT 'CRM de Bordero', false, true
WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);

-- 2. ATUALIZAR TABELA NIVEIS_ACESSO COM PERMISSÕES
-- =====================================================
-- Adicionar coluna de permissões se não existir
ALTER TABLE niveis_acesso 
ADD COLUMN IF NOT EXISTS permissoes JSONB DEFAULT '{}';

-- Atualizar níveis existentes com permissões padrão
UPDATE niveis_acesso 
SET permissoes = CASE 
  WHEN nome = 'Administrador' THEN '{
    "dashboard": true,
    "borderos": true,
    "secretarias": true,
    "direcionamentos": true,
    "tipos": true,
    "usuarios": true,
    "relatorios": true,
    "configuracoes": true
  }'::jsonb
  WHEN nome = 'Usuário' THEN '{
    "dashboard": true,
    "borderos": true,
    "secretarias": false,
    "direcionamentos": false,
    "tipos": false,
    "usuarios": false,
    "relatorios": false,
    "configuracoes": false
  }'::jsonb
  WHEN nome = 'Operador' THEN '{
    "dashboard": true,
    "borderos": true,
    "secretarias": false,
    "direcionamentos": false,
    "tipos": false,
    "usuarios": false,
    "relatorios": true,
    "configuracoes": false
  }'::jsonb
  WHEN nome = 'Visualizador' THEN '{
    "dashboard": true,
    "borderos": true,
    "secretarias": false,
    "direcionamentos": false,
    "tipos": false,
    "usuarios": false,
    "relatorios": true,
    "configuracoes": false
  }'::jsonb
  WHEN nome = 'Cadastrador' THEN '{
    "dashboard": true,
    "borderos": true,
    "secretarias": false,
    "direcionamentos": false,
    "tipos": false,
    "usuarios": false,
    "relatorios": false,
    "configuracoes": false
  }'::jsonb
  ELSE '{
    "dashboard": true,
    "borderos": false,
    "secretarias": false,
    "direcionamentos": false,
    "tipos": false,
    "usuarios": false,
    "relatorios": false,
    "configuracoes": false
  }'::jsonb
END
WHERE permissoes IS NULL OR permissoes = '{}';

-- 3. CRIAR TABELAS DE LOG SE NÃO EXISTIREM
-- =====================================================
CREATE TABLE IF NOT EXISTS log_atividades (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  usuario_id UUID REFERENCES usuarios(id),
  acao VARCHAR(100) NOT NULL,
  entidade VARCHAR(100) NOT NULL,
  entidade_id UUID,
  detalhes JSONB,
  ip VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS bordero_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  bordero_id UUID REFERENCES borderos(id),
  usuario_id UUID REFERENCES usuarios(id),
  acao VARCHAR(100) NOT NULL,
  detalhes JSONB,
  data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habilitar RLS nas tabelas de log
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;
ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;

-- Políticas para tabelas de log
DROP POLICY IF EXISTS "Permitir acesso total a log_atividades" ON log_atividades;
CREATE POLICY "Permitir acesso total a log_atividades" ON log_atividades
FOR ALL USING (true);

DROP POLICY IF EXISTS "Permitir acesso total a bordero_logs" ON bordero_logs;
CREATE POLICY "Permitir acesso total a bordero_logs" ON bordero_logs
FOR ALL USING (true);

-- 4. VERIFICAÇÕES FINAIS
-- =====================================================
-- Verificar se as tabelas foram criadas
SELECT 'configuracoes_sistema' as tabela, COUNT(*) as registros FROM configuracoes_sistema
UNION ALL
SELECT 'niveis_acesso' as tabela, COUNT(*) as registros FROM niveis_acesso
UNION ALL
SELECT 'log_atividades' as tabela, COUNT(*) as registros FROM log_atividades
UNION ALL
SELECT 'bordero_logs' as tabela, COUNT(*) as registros FROM bordero_logs;

-- Verificar permissões dos níveis de acesso
SELECT nome, permissoes FROM niveis_acesso ORDER BY nome;
