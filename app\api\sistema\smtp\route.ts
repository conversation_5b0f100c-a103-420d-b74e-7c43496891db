import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import nodemailer from "nodemailer"

// GET - Buscar configurações SMTP
export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: config, error } = await supabaseAdmin
      .from("smtp_config")
      .select("*")
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error("Erro ao buscar configurações SMTP:", error)
      return NextResponse.json({ error: "Erro ao buscar configurações" }, { status: 500 })
    }

    // Não retornar a senha por segurança
    if (config) {
      const { senha, ...configSemSenha } = config
      return NextResponse.json(configSemSenha)
    }

    return NextResponse.json(null)
  } catch (error) {
    console.error("Erro na API SMTP:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

// POST - Salvar configurações SMTP
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { host, port, email_remetente, senha, secure } = body

    if (!host || !port || !email_remetente || !senha) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 })
    }

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe configuração
    const { data: existingConfig } = await supabaseAdmin
      .from("smtp_config")
      .select("id")
      .limit(1)
      .single()

    let result
    if (existingConfig) {
      // Atualizar configuração existente
      const { data, error } = await supabaseAdmin
        .from("smtp_config")
        .update({
          host,
          port: parseInt(port),
          email_remetente,
          senha,
          secure: !!secure,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingConfig.id)
        .select()
        .single()

      result = { data, error }
    } else {
      // Criar nova configuração
      const { data, error } = await supabaseAdmin
        .from("smtp_config")
        .insert({
          host,
          port: parseInt(port),
          email_remetente,
          senha,
          secure: !!secure
        })
        .select()
        .single()

      result = { data, error }
    }

    if (result.error) {
      console.error("Erro ao salvar configurações SMTP:", result.error)
      return NextResponse.json({ error: "Erro ao salvar configurações" }, { status: 500 })
    }

    // Registrar no log do sistema
    try {
      await supabaseAdmin
        .from("sistema_logs")
        .insert({
          usuario_id: session.user.id,
          acao: existingConfig ? "update" : "create",
          entidade: "smtp_config",
          detalhes: {
            host,
            port: parseInt(port),
            email_remetente,
            secure: !!secure
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    return NextResponse.json({ message: "Configurações salvas com sucesso" })
  } catch (error) {
    console.error("Erro na API SMTP:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
