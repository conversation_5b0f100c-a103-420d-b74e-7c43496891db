-- Criar tabela de comentários de borderos
CREATE TABLE IF NOT EXISTS bordero_comentarios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bordero_id INTEGER REFERENCES borderos(id) ON DELETE CASCADE,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    comentario TEXT NOT NULL,
    mencoes JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>r índices para performance
CREATE INDEX IF NOT EXISTS idx_bordero_comentarios_bordero_id ON bordero_comentarios(bordero_id);
CREATE INDEX IF NOT EXISTS idx_bordero_comentarios_usuario_id ON bordero_comentarios(usuario_id);
CREATE INDEX IF NOT EXISTS idx_bordero_comentarios_created_at ON bordero_comentarios(created_at);

-- Habilitar RLS
ALTER TABLE bordero_comentarios ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON>r política para permitir acesso aos comentários
DROP POLICY IF EXISTS "Permitir acesso aos comentários de borderos" ON bordero_comentarios;
CREATE POLICY "Permitir acesso aos comentários de borderos" ON bordero_comentarios
FOR ALL USING (true);

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_bordero_comentarios_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar updated_at
DROP TRIGGER IF EXISTS update_bordero_comentarios_updated_at ON bordero_comentarios;
CREATE TRIGGER update_bordero_comentarios_updated_at
    BEFORE UPDATE ON bordero_comentarios
    FOR EACH ROW
    EXECUTE FUNCTION update_bordero_comentarios_updated_at();

-- Inserir alguns comentários de exemplo (opcional)
-- INSERT INTO bordero_comentarios (bordero_id, usuario_id, comentario) VALUES
-- (1, (SELECT id FROM usuarios LIMIT 1), 'Bordero recebido e em análise.'),
-- (1, (SELECT id FROM usuarios LIMIT 1), 'Documentação aprovada, seguindo para próxima etapa.');
