"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { Eye, EyeOff, Check, X } from "lucide-react"

interface PasswordInputProps extends React.ComponentProps<"input"> {
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  showCriteria?: boolean
  onValidityChange?: (isValid: boolean) => void
}

interface PasswordCriteria {
  minLength: boolean
  hasUppercase: boolean
  hasLowercase: boolean
  hasNumber: boolean
  hasSpecialChar: boolean
}

interface PasswordStrength {
  score: number
  label: string
  color: string
  bgColor: string
}

export const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, value, onChange, showCriteria = true, onValidityChange, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [criteria, setCriteria] = React.useState<PasswordCriteria>({
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecialChar: false
    })

    const validatePassword = (password: string): PasswordCriteria => {
      return {
        minLength: password.length >= 8,
        hasUppercase: /[A-Z]/.test(password),
        hasLowercase: /[a-z]/.test(password),
        hasNumber: /\d/.test(password),
        hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
      }
    }

    const calculatePasswordStrength = (password: string, criteria: PasswordCriteria): PasswordStrength => {
      if (!password) {
        return { score: 0, label: "", color: "", bgColor: "" }
      }

      let score = 0

      // Pontuação baseada nos critérios
      if (criteria.minLength) score += 20
      if (criteria.hasUppercase) score += 20
      if (criteria.hasLowercase) score += 20
      if (criteria.hasNumber) score += 20
      if (criteria.hasSpecialChar) score += 20

      // Bônus por comprimento extra
      if (password.length >= 12) score += 10
      if (password.length >= 16) score += 10

      // Determinar força
      if (score < 40) {
        return {
          score,
          label: "Muito Fraca",
          color: "text-red-600",
          bgColor: "bg-red-500"
        }
      } else if (score < 60) {
        return {
          score,
          label: "Fraca",
          color: "text-orange-600",
          bgColor: "bg-orange-500"
        }
      } else if (score < 80) {
        return {
          score,
          label: "Média",
          color: "text-yellow-600",
          bgColor: "bg-yellow-500"
        }
      } else if (score < 100) {
        return {
          score,
          label: "Forte",
          color: "text-blue-600",
          bgColor: "bg-blue-500"
        }
      } else {
        return {
          score,
          label: "Excelente",
          color: "text-green-600",
          bgColor: "bg-green-500"
        }
      }
    }



    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      onChange(e)

      const newCriteria = validatePassword(newValue)
      setCriteria(newCriteria)

      // Verificar se todos os critérios foram atendidos
      const isValid = Object.values(newCriteria).every(Boolean)
      onValidityChange?.(isValid)
    }

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    const criteriaItems = [
      { key: 'minLength', label: 'Mínimo 8 caracteres', met: criteria.minLength },
      { key: 'hasUppercase', label: 'Pelo menos 1 letra maiúscula', met: criteria.hasUppercase },
      { key: 'hasLowercase', label: 'Pelo menos 1 letra minúscula', met: criteria.hasLowercase },
      { key: 'hasNumber', label: 'Pelo menos 1 número', met: criteria.hasNumber },
      { key: 'hasSpecialChar', label: 'Pelo menos 1 caractere especial (!@#$%^&*)', met: criteria.hasSpecialChar }
    ]

    const allCriteriaMet = Object.values(criteria).every(Boolean)
    const passwordStrength = calculatePasswordStrength(value, criteria)

    return (
      <div className="space-y-2">
        <div className="relative">
          <Input
            ref={ref}
            type={showPassword ? "text" : "password"}
            className={cn(
              "pr-10",
              value && !allCriteriaMet && "border-red-300 focus:border-red-500 focus:ring-red-500",
              value && allCriteriaMet && "border-green-300 focus:border-green-500 focus:ring-green-500",
              className
            )}
            value={value}
            onChange={handleInputChange}
            {...props}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 flex items-center pr-3"
            onClick={togglePasswordVisibility}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </button>
        </div>

        {/* Indicador de Força da Senha */}
        {value && passwordStrength.label && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Força da senha:</span>
              <span className={cn("text-sm font-medium", passwordStrength.color)}>
                {passwordStrength.label}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={cn("h-2 rounded-full transition-all duration-300", passwordStrength.bgColor)}
                style={{ width: `${Math.min(passwordStrength.score, 100)}%` }}
              />
            </div>
          </div>
        )}

        {showCriteria && value && (
          <div className="space-y-1 p-3 bg-gray-50 dark:bg-gray-800 rounded-md border dark:border-gray-700">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Critérios da senha:
            </p>
            {criteriaItems.map((item) => (
              <div key={item.key} className="flex items-center gap-2">
                {item.met ? (
                  <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                ) : (
                  <X className="h-4 w-4 text-red-500 dark:text-red-400" />
                )}
                <span className={cn(
                  "text-sm",
                  item.met ? "text-green-700 dark:text-green-300" : "text-red-600 dark:text-red-400"
                )}>
                  {item.label}
                </span>
              </div>
            ))}

            {allCriteriaMet && (
              <div className="mt-2 p-2 bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded text-sm text-green-800 dark:text-green-200">
                ✅ Senha atende a todos os critérios!
              </div>
            )}
          </div>
        )}
      </div>
    )
  }
)

PasswordInput.displayName = "PasswordInput"
