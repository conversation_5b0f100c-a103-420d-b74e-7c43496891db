"use client"

import * as React from "react"
import { Monitor, Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"

import { Button } from "@/components/ui/button"

export function ThemeToggleSimple() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const cycleTheme = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  const getIcon = () => {
    if (!mounted) return <Sun className="h-4 w-4" />
    
    switch (theme) {
      case "light":
        return <Sun className="h-4 w-4" />
      case "dark":
        return <Moon className="h-4 w-4" />
      case "system":
        return <Monitor className="h-4 w-4" />
      default:
        return <Sun className="h-4 w-4" />
    }
  }

  const getTitle = () => {
    if (!mounted) return "Alternar tema"
    
    switch (theme) {
      case "light":
        return "Modo claro (clique para escuro)"
      case "dark":
        return "Modo escuro (clique para sistema)"
      case "system":
        return "Modo sistema (clique para claro)"
      default:
        return "Alternar tema"
    }
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      className="ml-auto"
      onClick={cycleTheme}
      title={getTitle()}
    >
      {getIcon()}
      <span className="sr-only">Alternar tema</span>
    </Button>
  )
}
