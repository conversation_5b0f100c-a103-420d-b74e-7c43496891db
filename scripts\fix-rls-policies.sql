-- Script para corrigir políticas RLS das notificações
-- Execute este script no Supabase SQL Editor

-- 1. Verificar se a tabela notificacoes existe
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'notificacoes') THEN
    -- Criar tabela notificacoes se não existir
    CREATE TABLE notificacoes (
      id BIGSERIAL PRIMARY KEY,
      titulo VARCHAR(255) NOT NULL,
      mensagem TEXT NOT NULL,
      usuario_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      lida BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Tabela notificacoes criada com sucesso';
  ELSE
    RAISE NOTICE 'Tabela notificacoes já existe';
  END IF;
END
$$;

-- 2. Habilitar RLS na tabela notificacoes
ALTER TABLE notificacoes ENABLE ROW LEVEL SECURITY;

-- 3. Remover políticas existentes
DROP POLICY IF EXISTS "Usuários podem ver suas próprias notificações" ON notificacoes;
DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias notificações" ON notificacoes;
DROP POLICY IF EXISTS "Sistema pode inserir notificações" ON notificacoes;
DROP POLICY IF EXISTS "Permitir acesso total a notificacoes" ON notificacoes;

-- 4. Criar políticas RLS corretas
-- Política para SELECT (usuários podem ver apenas suas notificações)
CREATE POLICY "Usuários podem ver suas próprias notificações" ON notificacoes
FOR SELECT USING (auth.uid() = usuario_id);

-- Política para UPDATE (usuários podem atualizar apenas suas notificações)
CREATE POLICY "Usuários podem atualizar suas próprias notificações" ON notificacoes
FOR UPDATE USING (auth.uid() = usuario_id);

-- Política para INSERT (sistema pode inserir notificações para qualquer usuário)
CREATE POLICY "Sistema pode inserir notificações" ON notificacoes
FOR INSERT WITH CHECK (true);

-- Política para DELETE (usuários podem deletar apenas suas notificações)
CREATE POLICY "Usuários podem deletar suas próprias notificações" ON notificacoes
FOR DELETE USING (auth.uid() = usuario_id);

-- 5. Verificar se a coluna updated_at existe
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'notificacoes' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE notificacoes ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Coluna updated_at adicionada à tabela notificacoes';
  ELSE
    RAISE NOTICE 'Coluna updated_at já existe na tabela notificacoes';
  END IF;
END
$$;

-- 6. Criar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_notificacoes_updated_at ON notificacoes;
CREATE TRIGGER update_notificacoes_updated_at
  BEFORE UPDATE ON notificacoes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 7. Verificar políticas criadas
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'notificacoes'
ORDER BY policyname;

-- 8. Testar inserção de notificação de exemplo (opcional)
-- INSERT INTO notificacoes (titulo, mensagem, usuario_id)
-- VALUES ('Teste', 'Notificação de teste', auth.uid());

RAISE NOTICE 'Políticas RLS para notificacoes configuradas com sucesso!';
RAISE NOTICE 'Execute: SELECT * FROM pg_policies WHERE tablename = ''notificacoes''; para verificar as políticas.';
