const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function checkBorderos() {
  try {
    console.log('🔍 Verificando borderos na base de dados...')

    const { data: borderos, error } = await supabase
      .from('borderos')
      .select(`
        *,
        secretaria:secretarias(nome),
        tipo:tipos(nome)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Erro ao buscar borderos:', error.message)
      return
    }

    console.log(`✅ Encontrados ${borderos.length} borderos:`)
    
    borderos.forEach((bordero, index) => {
      console.log(`\n${index + 1}. Bordero ID: ${bordero.id}`)
      console.log(`   - Código: ${bordero.bordero_cod}`)
      console.log(`   - Empresa: ${bordero.nome_empresa}`)
      console.log(`   - Valor: R$ ${bordero.valor}`)
      console.log(`   - Status: ${bordero.status}`)
      console.log(`   - Secretaria: ${bordero.secretaria?.nome || 'N/A'}`)
      console.log(`   - Tipo: ${bordero.tipo?.nome || 'N/A'}`)
      console.log(`   - Data: ${new Date(bordero.data).toLocaleDateString('pt-BR')}`)
    })

    if (borderos.length === 0) {
      console.log('\n⚠️ Nenhum bordero encontrado. Vou criar alguns de exemplo...')
      
      // Buscar secretarias e tipos
      const { data: secretarias } = await supabase.from('secretarias').select('*').limit(2)
      const { data: tipos } = await supabase.from('tipos').select('*').limit(2)
      
      if (secretarias && secretarias.length > 0 && tipos && tipos.length > 0) {
        const { data: newBorderos, error: insertError } = await supabase
          .from('borderos')
          .insert([
            {
              bordero_cod: 'BOR-2024-001',
              valor: 15000.50,
              data: new Date().toISOString(),
              nome_empresa: 'Empresa ABC Ltda',
              secretaria_id: secretarias[0].id,
              tipo_id: tipos[0].id,
              observacao: 'Bordero de exemplo',
              status: 'novo'
            },
            {
              bordero_cod: 'BOR-2024-002',
              valor: 8500.00,
              data: new Date().toISOString(),
              nome_empresa: 'Construtora XYZ',
              secretaria_id: secretarias[0].id,
              tipo_id: tipos[0].id,
              observacao: 'Outro bordero de exemplo',
              status: 'novo'
            }
          ])
          .select()
        
        if (insertError) {
          console.error('❌ Erro ao criar borderos:', insertError.message)
        } else {
          console.log('✅ Borderos de exemplo criados:', newBorderos.length)
          newBorderos.forEach(bordero => {
            console.log(`   - ${bordero.bordero_cod} (ID: ${bordero.id})`)
          })
        }
      }
    }

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

checkBorderos()
