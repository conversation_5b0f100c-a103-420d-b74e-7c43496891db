"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { CurrencyInput } from "@/components/ui/currency-input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker"
import { Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "@/hooks/use-toast"
import { useNotifications } from "@/lib/notifications"

interface Secretaria {
  id: number
  nome: string
}

interface Tipo {
  id: number
  nome: string
}

interface Usuario {
  id: string
  nome: string
}

export default function NovoBorderoPage() {
  const router = useRouter()
  const notify = useNotifications()

  const [date, setDate] = useState<Date>()
  const [loading, setLoading] = useState(false)
  const [secretarias, setSecretarias] = useState<Secretaria[]>([])
  const [tipos, setTipos] = useState<Tipo[]>([])
  const [usuarios, setUsuarios] = useState<Usuario[]>([])

  const [formData, setFormData] = useState({
    borderoCod: "",
    valor: "",
    nomeEmpresa: "",
    secretariaId: "",
    tipoId: "",
    observacao: "",
    devolucoes: [] as string[]
  })

  // Carregar dados ao montar o componente
  useEffect(() => {
    fetchSecretarias()
    fetchTipos()
    fetchUsuarios()
  }, [])

  const fetchSecretarias = async () => {
    try {
      const response = await fetch('/api/secretarias')
      if (response.ok) {
        const data = await response.json()
        setSecretarias(data)
      }
    } catch (error) {
      console.error('Erro ao carregar secretarias:', error)
    }
  }

  const fetchTipos = async () => {
    try {
      const response = await fetch('/api/tipos')
      if (response.ok) {
        const data = await response.json()
        setTipos(data)
      }
    } catch (error) {
      console.error('Erro ao carregar tipos:', error)
    }
  }

  const fetchUsuarios = async () => {
    try {
      const response = await fetch('/api/usuarios')
      if (response.ok) {
        const data = await response.json()
        setUsuarios(data.usuarios || data)
      }
    } catch (error) {
      console.error('Erro ao carregar usuários:', error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleDevolucoesChange = (usuarioId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      devolucoes: checked
        ? [...prev.devolucoes, usuarioId]
        : prev.devolucoes.filter(id => id !== usuarioId)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!date) {
      toast({
        title: "Erro",
        description: "Por favor, selecione uma data.",
        variant: "destructive"
      })
      return
    }

    if (!formData.borderoCod || !formData.valor || !formData.nomeEmpresa ||
        !formData.secretariaId || !formData.tipoId) {
      toast({
        title: "Erro",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/borderos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          borderoCod: formData.borderoCod,
          valor: parseFloat(formData.valor),
          data: date.toISOString(),
          nomeEmpresa: formData.nomeEmpresa,
          secretariaId: formData.secretariaId,
          tipoId: formData.tipoId,
          observacao: formData.observacao,
          devolucoes: formData.devolucoes
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Erro detalhado da API:', errorData)

        let errorMessage = errorData.error || 'Erro ao criar bordero'

        if (errorData.details && errorData.details.length > 0) {
          errorMessage += '\n\nDetalhes:\n' + errorData.details.join('\n')
        }

        if (errorData.fields) {
          const camposComErro = Object.entries(errorData.fields)
            .filter(([_, hasError]) => hasError)
            .map(([field, _]) => field)

          if (camposComErro.length > 0) {
            errorMessage += '\n\nCampos com erro: ' + camposComErro.join(', ')
          }
        }

        throw new Error(errorMessage)
      }

      const bordero = await response.json()

      notify.success(
        "Bordero Criado",
        `Bordero ${formData.borderoCod} foi criado com sucesso!`
      )

      router.push('/dashboard/borderos')
    } catch (error: any) {
      console.error('Erro ao criar bordero:', error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível criar o bordero.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Novo Bordero</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Cadastrar Novo Bordero</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="borderoCod">Código do Bordero *</Label>
                <Input
                  id="borderoCod"
                  placeholder="Digite o código do bordero"
                  value={formData.borderoCod}
                  onChange={(e) => handleInputChange('borderoCod', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="valor">Valor (R$) *</Label>
                <CurrencyInput
                  id="valor"
                  placeholder="R$ 0,00"
                  value={formData.valor}
                  onChange={(value) => handleInputChange('valor', value.toString())}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="data">Data *</Label>
                <EnhancedDatePicker
                  date={date}
                  onDateChange={setDate}
                  placeholder="Selecione uma data"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="empresa">Nome da Empresa *</Label>
                <Input
                  id="empresa"
                  placeholder="Digite o nome da empresa"
                  value={formData.nomeEmpresa}
                  onChange={(e) => handleInputChange('nomeEmpresa', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secretaria">Secretaria *</Label>
                <Select value={formData.secretariaId} onValueChange={(value) => handleInputChange('secretariaId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma secretaria" />
                  </SelectTrigger>
                  <SelectContent>
                    {secretarias.map((secretaria) => (
                      <SelectItem key={secretaria.id} value={secretaria.id}>
                        {secretaria.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="tipo">Tipo de Bordero *</Label>
                <Select value={formData.tipoId} onValueChange={(value) => handleInputChange('tipoId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    {tipos.map((tipo) => (
                      <SelectItem key={tipo.id} value={tipo.id}>
                        {tipo.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Seção de Devoluções */}
            <div className="col-span-full space-y-4">
              <Label>Usuários para Devolução (opcional)</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {usuarios.map((usuario) => (
                  <div key={usuario.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`devolucao-${usuario.id}`}
                      checked={formData.devolucoes.includes(usuario.id)}
                      onChange={(e) => handleDevolucoesChange(usuario.id, e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor={`devolucao-${usuario.id}`} className="text-sm">
                      {usuario.nome}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-span-full space-y-2">
              <Label htmlFor="observacao">Observação</Label>
              <Textarea
                id="observacao"
                placeholder="Digite observações adicionais"
                className="min-h-[120px]"
                value={formData.observacao}
                onChange={(e) => handleInputChange('observacao', e.target.value)}
              />
            </div>

            <div className="col-span-full flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/dashboard/borderos')}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-sky-600 hover:bg-sky-700"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  'Salvar Bordero'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
