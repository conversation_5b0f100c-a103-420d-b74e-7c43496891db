"use client"

import { useState, use<PERSON><PERSON><PERSON>, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/hooks/use-toast"
import { ArrowLeft, Upload, FileSpreadsheet, CheckCircle, AlertTriangle, XCircle, Settings, Eye, Play, Pause, BanknoteIcon, Download } from "lucide-react"
import jsPDF from "jspdf"
import autoTable from "jspdf-autotable"

// Adicionar o autoTable ao jsPDF
declare module "jspdf" {
  interface jsPDF {
    autoTable: typeof autoTable;
    lastAutoTable: {
      finalY: number;
    } | undefined;
    previousAutoTable: {
      finalY: number;
    } | undefined;
  }
}
import { useDropzone } from "react-dropzone"
import * as XLSX from "xlsx"
import Link from "next/link"

// Função para normalizar texto removendo acentos, caracteres especiais, múltiplos espaços e aparando
function normalizeText(text: string | number | boolean | null | undefined): string {
  if (text === undefined || text === null || text === '') return '';
  if (typeof text === 'number' && text === 0) return '0'; // Mantém '0' para números
  if (typeof text === 'boolean') return String(text); // Converte boolean para string

  let normalized = String(text)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove caracteres de controle
    .normalize('NFD') // Normaliza para decompor caracteres acentuados
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .toLowerCase() // Converte para minúsculas
    .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais, mas mantém espaços e números
    .replace(/\s+/g, ' ') // Substitui múltiplos espaços por um único
    .trim(); // Remove espaços no início e no fim

  return normalized;
}

interface ExcelRow {
  [key: string]: any
}

interface MappedRow {
  [key: string]: any
}

interface ColumnMapping {
  [excelColumn: string]: string
}

export default function ImportarBorderosPage() {
  const router = useRouter()
  const [step, setStep] = useState<'upload' | 'mapping' | 'validation' | 'import'>('upload')
  const [excelData, setExcelData] = useState<ExcelRow[]>([])
  const [excelColumns, setExcelColumns] = useState<string[]>([])
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({})
  const [mappedData, setMappedData] = useState<MappedRow[]>([])
  const [secretarias, setSecretarias] = useState<any[]>([])
  const [tipos, setTipos] = useState<any[]>([])
  const [direcionamentos, setDirecionamentos] = useState<any[]>([])
  const [importing, setImporting] = useState(false)
  const [paused, setPaused] = useState(false)
  const [cancelled, setCancelled] = useState(false)
  const [progressCount, setProgressCount] = useState(0)
  const [successCount, setSuccessCount] = useState(0)
  const [errorCount, setErrorCount] = useState(0)
  const [logs, setLogs] = useState<{status: 'success'|'error', message: string, bordero_cod?: string, nome_empresa?: string}[]>([])
  const currentRecordRef = useRef<{bordero_cod?: string, nome_empresa?: string}|null>(null)
  const pausedRef = useRef(paused)
  const cancelledRef = useRef(cancelled)
  const [importProgress, setImportProgress] = useState(0)
  const [importResults, setImportResults] = useState<any>(null)
  const totalValidRowsRef = useRef<number>(0)

  const [hasHeader, setHasHeader] = useState(true)
  const [skipLines, setSkipLines] = useState(0)
  const [previewData, setPreviewData] = useState<ExcelRow[]>([])
  const [inconsistentData, setInconsistentData] = useState<{
    secretarias: string[]
    direcionamentos: string[]
    tipos: string[]
  }>({ secretarias: [], direcionamentos: [], tipos: [] })
  const [validationFilter, setValidationFilter] = useState<'all' | 'valid' | 'warning' | 'error'>('all')

  const abortControllerRef = useRef<AbortController | null>(null);

  // Carregar dados de referência ao montar o componente
  useEffect(() => {
    loadReferenceData();
  }, []);

  useEffect(() => { pausedRef.current = paused; }, [paused]);
  useEffect(() => { cancelledRef.current = cancelled; }, [cancelled]);

  // Função auxiliar para obter o valor do Select
  const getSelectValue = useCallback((fieldName: string) => {
    const found = Object.entries(columnMapping).find(([_, value]) => value === fieldName)
    const result = found?.[0] || 'unmapped'
    return result && result.trim() !== '' ? result : 'unmapped'
  }, [columnMapping])

  // Configuração do dropzone com processamento avançado
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

        if (rawData.length === 0) {
          toast({
            title: "Erro",
            description: "Planilha vazia ou inválida.",
            variant: "destructive"
          })
          return
        }

        console.log('📊 Dados brutos da planilha:', rawData.slice(0, 3));

        let processedData = rawData.slice(skipLines);
        console.log(`⏭️ Pulando ${skipLines} linhas. Dados após pular:`, processedData.slice(0, 3));

        let headers: string[];
        let rows: any[];

        if (hasHeader && processedData.length > 0) {
          headers = processedData[0] as string[];
          rows = processedData.slice(1);
          console.log('📋 Cabeçalhos detectados:', headers);
        } else {
          const maxCols = Math.max(...processedData.map((row: any) => row.length));
          headers = Array.from({ length: maxCols }, (_, i) => `Coluna ${i + 1}`);
          rows = processedData;
          console.log('🔤 Cabeçalhos gerados automaticamente:', headers);
        }

        const excelRows = rows
          .map((row: any) => {
            const obj: ExcelRow = {};
            headers.forEach((header, index) => {
              obj[header] = row[index] || '';
            });
            return obj;
          })
          .filter(row => Object.values(row).some(val => val !== undefined && val !== null && val !== '' && String(val).trim() !== ''));

        console.log(`✅ Processadas ${excelRows.length} linhas válidas de ${rows.length} totais`);

        setExcelColumns(headers);
        setExcelData(excelRows);

        console.log('📊 excelData (primeiras 5 linhas):', excelRows.slice(0, 5));

        // Mapeamento automático inteligente
        const autoMapping: Record<string, string> = {};
        headers.forEach(header => {
          const normalizedHeader = normalizeText(header);

          if (normalizedHeader.includes('bordero') || normalizedHeader.includes('codigo')) {
            autoMapping[header] = 'bordero_cod';
          } else if (normalizedHeader.includes('valor') || normalizedHeader.includes('vlr')) {
            autoMapping[header] = 'valor';
          } else if (normalizedHeader.includes('data') || normalizedHeader.includes('dt')) {
            autoMapping[header] = 'data';
          } else if (normalizedHeader.includes('empresa') || normalizedHeader.includes('nomeempresa') || normalizedHeader.includes('cliente')) {
            autoMapping[header] = 'nome_empresa';
          } else if (normalizedHeader.includes('secretaria') || normalizedHeader.includes('sec')) {
            autoMapping[header] = 'secretaria_id';
          } else if (normalizedHeader.includes('tipo') || normalizedHeader.includes('tipobordero')) {
            autoMapping[header] = 'tipo_id';
          } else if (normalizedHeader.includes('direcionamento') || normalizedHeader.includes('direc')) {
            autoMapping[header] = 'direcionamento_id';
          } else if (normalizedHeader.includes('obs') || normalizedHeader.includes('observacao') || normalizedHeader.includes('devolucao')) {
            autoMapping[header] = 'observacao';
          }
        });

        setColumnMapping(autoMapping);
        setPreviewData(rawData.slice(0, 5) as ExcelRow[]);

        toast({
          title: "✅ Planilha Processada",
          description: `${excelRows.length} linhas válidas encontradas. Configure o mapeamento.`
        });
      } catch (error) {
        console.error('❌ Erro ao processar planilha:', error);
        toast({
          title: "Erro",
          description: "Erro ao processar a planilha. Verifique o formato do arquivo e o console.",
          variant: "destructive"
        });
      }
    }
    reader.readAsArrayBuffer(file);
  }, [hasHeader, skipLines]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1
  });

  // Carregar dados de referência (secretarias, tipos, direcionamentos)
  const loadReferenceData = async () => {
    try {
      console.log('🔄 Carregando dados de referência (secretarias, tipos, direcionamentos)...');
      const [secretariasRes, tiposRes, direcionamentosRes] = await Promise.all([
        fetch('/api/secretarias'),
        fetch('/api/tipos'),
        fetch('/api/direcionamentos')
      ]);

      if (secretariasRes.ok && tiposRes.ok && direcionamentosRes.ok) {
        const secretariasData = await secretariasRes.json();
        const tiposData = await tiposRes.json();
        const direcionamentosData = await direcionamentosRes.json();

        console.log('✅ Dados de referência carregados:');
        console.log('- Secretarias:', secretariasData.map((s: any) => `${s.nome} (ID: ${s.id})`));
        console.log('- Tipos:', tiposData.map((t: any) => `${t.nome} (ID: ${t.id})`));
        console.log('- Direcionamentos:', direcionamentosData.map((d: any) => `${d.nome} (ID: ${d.id})`));

        setSecretarias(secretariasData);
        setTipos(tiposData);
        setDirecionamentos(direcionamentosData);
      } else {
        const secretariasError = !secretariasRes.ok ? await secretariasRes.text() : '';
        const tiposError = !tiposRes.ok ? await tiposRes.text() : '';
        const direcionamentosError = !direcionamentosRes.ok ? await direcionamentosRes.text() : '';
        
        console.error('❌ Falha ao carregar um ou mais dados de referência.');
        console.error('Erro Secretarias:', secretariasError);
        console.error('Erro Tipos:', tiposError);
        console.error('Erro Direcionamentos:', direcionamentosError);

        toast({
          title: "Erro de Carregamento",
          description: "Não foi possível carregar todos os dados de referência (secretarias, tipos, direcionamentos). Isso pode afetar a validação. Verifique o console.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('❌ Erro inesperado ao carregar dados de referência:', error);
      toast({
        title: "Erro de Conexão",
        description: "Erro ao tentar conectar com a API para carregar dados de referência. Verifique sua conexão e o backend.",
        variant: "destructive"
      });
    }
  };

  // Validar dados mapeados
  const validateMappedData = useCallback(async () => {
    if (secretarias.length === 0 || tipos.length === 0 || direcionamentos.length === 0) {
      toast({
        title: "Dados de Referência Ausentes",
        description: "Por favor, aguarde o carregamento dos dados de referência ou verifique a conexão com o backend.",
        variant: "destructive"
      });
      console.error("Dados de referência não carregados ao tentar validar.");
      // Tenta recarregar e depois valida
      loadReferenceData().then(() => {
        // Precisa re-chamar a validação após o carregamento assíncrono
        setTimeout(() => validateMappedData(), 100); // Pequeno atraso para garantir o estado atualizado
      });
      return;
    }

    const inconsistent = {
      secretarias: new Set<string>(),
      direcionamentos: new Set<string>(),
      tipos: new Set<string>()
    };

    const validated: MappedRow[] = await Promise.all(excelData.map(async row => {
      const mapped: MappedRow['mapped'] = {};
      const errors: string[] = [];
      let status: 'valid' | 'warning' | 'error' = 'valid';
      let duplicateBordero = false;

      // Process each column mapping
      for (const [excelCol, systemCol] of Object.entries(columnMapping)) {
        const originalValue = row[excelCol];
        let value = String(originalValue || '').trim();

        if (systemCol === 'bordero_cod' && value) {
          mapped[systemCol] = value;
          
          // Verificar se o bordero já existe no sistema
          try {
            const exists = await checkBorderoExists(value);
            if (exists) {
              duplicateBordero = true;
              errors.push(`⚠️ ATENÇÃO: Bordero ${value} já existe no sistema e será ignorado na importação`);
              status = 'error';
            }
          } catch (err) {
            console.error("Erro ao verificar duplicidade:", err);
          }
        } else if (systemCol === 'nome_empresa' && value) {
          mapped[systemCol] = value;
        } else if (systemCol === 'valor' && value) {
          let cleanValue = value.toString()
            .replace(/[^\d.,]/g, '')
            .replace(/\./g, '')
            .replace(',', '.');
          const numValue = parseFloat(cleanValue);
          if (isNaN(numValue) || numValue <= 0) {
            errors.push(`Valor inválido: ${originalValue}`);
            status = 'error';
          } else {
            mapped[systemCol] = numValue;
          }
        } else if (systemCol === 'data' && value) {
          try {
            let date: Date | null = null;
            if (typeof originalValue === 'number' && originalValue > 1) { // Excel serial date
              const excelEpoch = new Date(1899, 11, 30); // Excel's epoch (1900-01-01 is day 1, with a bug)
              date = new Date(excelEpoch.getTime() + originalValue * 24 * 60 * 60 * 1000);
            } else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(value)) { // DD/MM/YYYY
              const [dia, mes, ano] = value.split('/');
              date = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
            } else if (/^\d{4}-\d{2}-\d{2}$/.test(value)) { // YYYY-MM-DD
              date = new Date(value);
            } else { // Try general parsing
              date = new Date(value);
            }

            if (!date || isNaN(date.getTime()) || date.getFullYear() < 1900 || date.getFullYear() > 2100) {
              errors.push(`Data inválida: ${originalValue}`);
              status = 'error';
            } else {
              mapped[systemCol] = date.toISOString().split('T')[0];
            }
          } catch {
            errors.push(`Data inválida: ${originalValue}`);
            status = 'error';
          }
        } else if (systemCol === 'secretaria_id' && value) {
          console.log('\n--- VALIDAÇÃO DE SECRETARIA ---');
          console.log('Valor original da planilha para secretaria:', originalValue);
          const normalizedValue = normalizeText(value);
          console.log('Valor normalizado da planilha:', normalizedValue);

          let foundSecretaria = null;
          let bestScore = 0; // Para encontrar a melhor correspondência por similaridade

          for (const s of secretarias) {
            if (!s || !s.nome) continue;
            const normalizedSecretaria = normalizeText(s.nome);
            console.log(`Comparando com "${s.nome}" (normalizado: "${normalizedSecretaria}")`);

            // 1. Tentativa de correspondência exata
            if (normalizedSecretaria === normalizedValue) {
              console.log(`✅ Correspondência EXATA encontrada: "${originalValue}" -> "${s.nome}" (ID: ${s.id})`);
              foundSecretaria = s;
              break;
            }
            // 2. Tentativa de inclusão (uma string contém a outra)
            if (normalizedSecretaria.includes(normalizedValue) || normalizedValue.includes(normalizedSecretaria)) {
                console.log(`✅ Correspondência por INCLUSÃO: "${originalValue}" -> "${s.nome}" (ID: ${s.id})`);
                foundSecretaria = s;
                break;
            }
            // 3. Tentativa de palavras em comum (mais de 3 letras)
            const palavrasValor = normalizedValue.split(' ').filter(w => w.length > 3);
            const palavrasSecretaria = normalizedSecretaria.split(' ').filter(w => w.length > 3);
            const commonWords = palavrasValor.filter(pv => palavrasSecretaria.includes(pv));
            if (commonWords.length > 0) {
                console.log(`✅ Correspondência por PALAVRAS COMUNS: "${originalValue}" -> "${s.nome}" (ID: ${s.id}) - Palavras: ${commonWords.join(', ')}`);
                foundSecretaria = s;
                break;
            }

            // 4. Tentativa de similaridade (Levenshtein) - só se não encontrado ainda
            const similarity = calculateSimilarity(normalizedValue, normalizedSecretaria);
            console.log(`  - Similaridade com "${s.nome}": ${similarity.toFixed(3)}`);
            if (similarity > 0.7 && similarity > bestScore) { // Limiar de 0.7
              bestScore = similarity;
              foundSecretaria = s;
            }
          }

          if (foundSecretaria) {
            mapped[systemCol] = foundSecretaria.id;
            mapped['secretaria_nome'] = foundSecretaria.nome;
            console.log(`FINAL: Mapeado com sucesso: "${originalValue}" para ID:`, foundSecretaria.id, 'Tipo:', typeof foundSecretaria.id);
          } else {
            console.log(`❌ Nenhuma correspondência para secretaria: "${originalValue}" (normalizado: "${normalizedValue}")`);
            inconsistent.secretarias.add(originalValue);
            errors.push(`Secretaria não encontrada: ${originalValue}`);
            status = status === 'valid' ? 'warning' : status;
            mapped['secretaria_nome'] = undefined;
          }
          console.log('---------------------------------');
        } else if (systemCol === 'tipo_id' && value) {
          const normalizedValue = normalizeText(value);
          const tipo = tipos.find(t => normalizeText(t.nome) === normalizedValue || normalizeText(t.nome).includes(normalizedValue) || normalizedValue.includes(normalizeText(t.nome)));
          if (tipo) {
            mapped[systemCol] = tipo.id;
          } else {
            inconsistent.tipos.add(originalValue);
            errors.push(`Tipo não encontrado: ${originalValue}`);
            status = status === 'valid' ? 'warning' : status;
          }
        } else if (systemCol === 'direcionamento_id' && value) {
          const normalizedValue = normalizeText(value);
          const direcionamento = direcionamentos.find(d => normalizeText(d.nome) === normalizedValue || normalizeText(d.nome).includes(normalizedValue) || normalizedValue.includes(normalizeText(d.nome)));
          if (direcionamento) {
            mapped[systemCol] = direcionamento.id;
          } else {
            inconsistent.direcionamentos.add(originalValue);
            errors.push(`Direcionamento não encontrado: ${originalValue}`);
            status = status === 'valid' ? 'warning' : status;
          }
        } else if (systemCol === 'observacao') {
          mapped[systemCol] = value;
        }
      }

      // Validações obrigatórias
      if (!mapped.bordero_cod || String(mapped.bordero_cod).trim() === '') {
        errors.push('Código do bordero é obrigatório');
        status = 'error';
      }
      if (!mapped.nome_empresa || String(mapped.nome_empresa).trim() === '') {
        errors.push('Nome da empresa é obrigatório');
        status = 'error';
      }
      if (mapped.valor === undefined || mapped.valor === null || mapped.valor <= 0) {
        errors.push('Valor deve ser um número maior que zero');
        status = 'error';
      }

      return {
        original: row,
        mapped,
        status: duplicateBordero ? 'error' : status,
        errors,
        duplicate: duplicateBordero
      };
    }));

    setInconsistentData({
      secretarias: Array.from(inconsistent.secretarias),
      direcionamentos: Array.from(inconsistent.direcionamentos),
      tipos: Array.from(inconsistent.tipos)
    });

    setMappedData(validated);
    console.log('✨ mappedData (primeiras 5 linhas):', validated.slice(0, 5));
    console.log('Número de linhas válidas após validação:', validated.filter(r => r.status === 'valid').length);
    setStep('validation');
  }, [excelData, columnMapping, secretarias, tipos, direcionamentos]);

  // Função para calcular similaridade entre strings (0 a 1)
  function calculateSimilarity(str1: string, str2: string): number {
    if (str1.length === 0 || str2.length === 0) return 0;
    if (str1 === str2) return 1;

    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    // Converte para conjuntos de n-gramas (trigramas, por exemplo)
    const getNGrams = (s: string, n: number) => {
      const grams = new Set<string>();
      for (let i = 0; i <= s.length - n; i++) {
        grams.add(s.substring(i, i + n));
      }
      return grams;
    };

    const n = 3; // Usando trigramas
    const s1Grams = getNGrams(str1, n);
    const s2Grams = getNGrams(str2, n);

    const intersection = new Set([...s1Grams].filter(x => s2Grams.has(x)));
    const union = new Set([...s1Grams, ...s2Grams]);

    if (union.size === 0) return 0; // Evita divisão por zero

    const jaccard = intersection.size / union.size;

    // Combina com a distância de Levenshtein para robustez
    const track = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    for (let i = 0; i <= str1.length; i += 1) track[0][i] = i;
    for (let j = 0; j <= str2.length; j += 1) track[j][0] = j;

    for (let j = 1; j <= str2.length; j += 1) {
      for (let i = 1; i <= str1.length; i += 1) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        track[j][i] = Math.min(
          track[j][i - 1] + 1,
          track[j - 1][i] + 1,
          track[j - 1][i - 1] + indicator
        );
      }
    }
    const levenshteinDistance = track[str2.length][str1.length];
    const levenshteinSimilarity = 1 - levenshteinDistance / maxLength;

    // Pondera as duas métricas (ex: 70% Jaccard, 30% Levenshtein)
    return (jaccard * 0.7) + (levenshteinSimilarity * 0.3);
  }

  // Executar importação
  const executeImport = async () => {
    setImporting(true);
    setPaused(false);
    setCancelled(false);
    setProgressCount(0);
    setSuccessCount(0);
    setErrorCount(0);
    setLogs([]);
    setImportResults(null);
    setImportProgress(0);
    setStep('import');
    
    // Filtrar apenas borderos válidos e não duplicados
    const validRows = mappedData.filter(row => row.status === 'valid' && !row.duplicate);
    totalValidRowsRef.current = validRows.length;
    
    // Inicializar progresso
    setProgressCount(0);
    setImportProgress(0);
    setLogs([{status:'success', message:`Iniciando importação de ${validRows.length} borderos...`}]);
    
    // Processar em lotes para melhor feedback visual
    const BATCH_SIZE = 5; // Processar em lotes de 5 para atualizar a UI mais frequentemente
    
    for (let i = 0; i < validRows.length; i++) {
      if (cancelledRef.current) {
        setLogs(logs => [{status:'error', message:'Importação cancelada pelo usuário.'}, ...logs]);
        break;
      }
      while (pausedRef.current) await new Promise(res => setTimeout(res, 300));
      
      const row = validRows[i];
      currentRecordRef.current = { bordero_cod: row.mapped.bordero_cod, nome_empresa: row.mapped.nome_empresa };
      
      // Atualizar progresso com animação suave
      const progressPercentage = Math.round(((i+1) / validRows.length) * 100);
      setProgressCount(i+1);
      setImportProgress(prev => {
        // Incrementar gradualmente para dar sensação de progresso contínuo
        const increment = (progressPercentage - prev) / 2;
        return prev + increment;
      });
      
      // A cada BATCH_SIZE registros ou no último registro, atualizar o progresso exato
      if (i % BATCH_SIZE === 0 || i === validRows.length - 1) {
        setTimeout(() => setImportProgress(progressPercentage), 100);
      }
      abortControllerRef.current = new AbortController();
      try {
        console.log(`[IMPORT] Enviando registro ${i+1}/${validRows.length}:`, row.mapped);
        const response = await fetch('/api/borderos/import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ borderos: [row.mapped] }),
          signal: abortControllerRef.current.signal
        });
        let result = null;
        try {
          result = await response.json();
        } catch (jsonErr) {
          console.error('[IMPORT] Erro ao parsear JSON da resposta:', jsonErr);
          setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (Resposta não é JSON)`}, ...logs.slice(0,49)]);
          setErrorCount(c => c+1);
          continue;
        }
        if (response.ok && result.success > 0) {
          setSuccessCount(c => c+1);
          setLogs(logs => [{status:'success', message:`Importado: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa}`}, ...logs.slice(0,49)]);
          console.log(`[IMPORT] Sucesso:`, result);
        } else {
          let errorMsg = '';
          if (typeof result?.error === 'string') errorMsg = result.error;
          else if (Array.isArray(result?.details) && result.details.length > 0 && result.details[0]?.error) errorMsg = result.details[0].error;
          else if (result?.details) errorMsg = JSON.stringify(result.details);
          else errorMsg = JSON.stringify(result);
          setErrorCount(c => c+1);
          setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${errorMsg})`}, ...logs.slice(0,49)]);
          console.error(`[IMPORT] Falha:`, errorMsg, result);
        }
      } catch (err: any) {
        if (err.name === 'AbortError') {
          setLogs(logs => [{status:'error', message:'Importação cancelada pelo usuário (AbortController).'}, ...logs]);
          console.warn('[IMPORT] Importação abortada pelo usuário.');
          break;
        }
        setErrorCount(c => c+1);
        setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${err.message || err})`}, ...logs.slice(0,49)]);
        console.error(`[IMPORT] Erro inesperado:`, err);
      }
    }
    setImporting(false);
    setImportResults({ success: successCount, errors: errorCount });
  };

  // Função para gerar o relatório PDF
  const generatePDFReport = () => {
    try {
      // Criar uma nova instância do PDF
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.getWidth();
      
      // Adicionar título e data
      doc.setFontSize(18);
      doc.setTextColor(0, 0, 128);
      doc.text("Relatório de Importação de Borderos", pageWidth / 2, 20, { align: "center" });
      
      doc.setFontSize(12);
      doc.setTextColor(100, 100, 100);
      const dataAtual = new Date().toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      doc.text(`Gerado em: ${dataAtual}`, pageWidth / 2, 30, { align: "center" });
      
      // Resumo da importação
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text("Resumo da Importação", 14, 45);
      
      // Tabela de resumo
      const successCount = typeof importResults?.success === 'number' ? importResults.success : 0;
      const errorCount = Array.isArray(importResults?.errors) ? importResults.errors.length :
                        (typeof importResults?.errors === 'number' ? importResults.errors : 0);
      
      // @ts-ignore - Ignorar erro de tipagem do autoTable
      doc.autoTable({
        startY: 50,
        head: [['Borderos Importados com Sucesso', 'Borderos com Erros', 'Total Processado']],
        body: [
          [successCount.toString(), errorCount.toString(), (successCount + errorCount).toString()]
        ],
        headStyles: { fillColor: [0, 102, 204], textColor: 255 },
        alternateRowStyles: { fillColor: [240, 240, 240] },
        margin: { top: 50 }
      });
      
      // Tabela de borderos importados com sucesso
      if (successCount > 0) {
        doc.setFontSize(14);
        doc.setTextColor(0, 100, 0);
        // @ts-ignore - Ignorar erro de tipagem do lastAutoTable
        const successY = doc.lastAutoTable?.finalY + 20 || 100;
        doc.text("Borderos Importados com Sucesso", 14, successY);
        
        // Filtrar logs de sucesso para obter os borderos importados
        const successLogs = logs.filter(log => log.status === 'success' && log.message.includes('Importado:'));
        
        // Preparar dados para a tabela
        const successTableData = successLogs.map(log => {
          const parts = log.message.split(' - ');
          const codigo = parts[0].replace('Importado: ', '');
          const empresa = parts[1] || '';
          return [codigo, empresa];
        });
        
        // Adicionar tabela de sucessos
        // @ts-ignore - Ignorar erro de tipagem do autoTable
        doc.autoTable({
          startY: successY + 5,
          head: [['Código do Bordero', 'Empresa']],
          body: successTableData,
          headStyles: { fillColor: [46, 125, 50], textColor: 255 },
          alternateRowStyles: { fillColor: [240, 255, 240] },
          margin: { top: 60 }
        });
      }
      
      // Tabela de borderos com erros
      if (errorCount > 0) {
        // Verificar se precisamos adicionar uma nova página
        // @ts-ignore - Ignorar erro de tipagem do lastAutoTable
        if (doc.lastAutoTable?.finalY > 200) {
          doc.addPage();
        }
        
        doc.setFontSize(14);
        doc.setTextColor(180, 0, 0);
        // @ts-ignore - Ignorar erro de tipagem do lastAutoTable
        const errorY = doc.lastAutoTable?.finalY + 20 || 150;
        doc.text("Borderos com Erros", 14, errorY);
        
        // Filtrar logs de erro para obter os borderos com falha
        const errorLogs = logs.filter(log => log.status === 'error' && log.message.includes('Falha:'));
        
        // Preparar dados para a tabela
        const errorTableData = errorLogs.map(log => {
          const parts = log.message.split(' - ');
          if (parts.length >= 2) {
            const codigo = parts[0].replace('Falha: ', '');
            const empresaComErro = parts[1].split(' (');
            const empresa = empresaComErro[0];
            const erro = empresaComErro.length > 1 ? empresaComErro[1].replace(')', '') : 'Erro desconhecido';
            return [codigo, empresa, erro];
          }
          return ['N/A', 'N/A', log.message];
        });
        
        // Adicionar tabela de erros
        // @ts-ignore - Ignorar erro de tipagem do autoTable
        doc.autoTable({
          startY: errorY + 5,
          head: [['Código do Bordero', 'Empresa', 'Motivo do Erro']],
          body: errorTableData,
          headStyles: { fillColor: [183, 28, 28], textColor: 255 },
          alternateRowStyles: { fillColor: [255, 240, 240] },
          columnStyles: {
            2: { cellWidth: 80 }
          },
          margin: { top: 60 }
        });
      }
      
      // Adicionar rodapé
      const pageCount = doc.internal.pages.length - 1;
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        doc.text(`Página ${i} de ${pageCount}`, pageWidth - 20, doc.internal.pageSize.getHeight() - 10, { align: "right" });
        doc.text("Sistema de Gestão de Borderos", 20, doc.internal.pageSize.getHeight() - 10);
      }
      
      // Salvar o PDF
      doc.save(`Relatorio_Importacao_Borderos_${new Date().toISOString().slice(0,10)}.pdf`);
      
      toast({
        title: "Relatório Gerado",
        description: "O relatório de importação foi gerado e baixado com sucesso.",
        variant: "default"
      });
      
    } catch (error) {
      console.error("Erro ao gerar relatório PDF:", error);
      toast({
        title: "Erro ao Gerar Relatório",
        description: "Ocorreu um erro ao gerar o relatório PDF. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Cancelamento eficiente
  const handleCancelImport = () => {
    setCancelled(true);
    if (typeof AbortController !== 'undefined' && abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  const handleResetImport = () => {
    setImporting(false);
    setPaused(false);
    setCancelled(false);
    setProgressCount(0);
    setSuccessCount(0);
    setErrorCount(0);
    setLogs([]);
    setImportResults(null);
    setStep('upload');
    setExcelData([]);
    setMappedData([]);
    setColumnMapping({});
    setPreviewData([]);
    setInconsistentData({ secretarias: [], direcionamentos: [], tipos: [] });
    toast({
      title: "Importação Reiniciada",
      description: "O processo de importação foi reiniciado. Você pode carregar um novo arquivo.",
    });
  };

  // Renderizar status
  const renderStatus = (status: string) => {
    switch (status) {
      case 'valid':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"><CheckCircle className="w-3 h-3 mr-1" />Válido</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"><AlertTriangle className="w-3 h-3 mr-1" />Aviso</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"><XCircle className="w-3 h-3 mr-1" />Erro</Badge>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/borderos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Importar Borderos</h1>
            <p className="text-muted-foreground">
              Importe borderos em lote através de planilhas Excel
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href="/dashboard/borderos/importacoes">
            <Button variant="outline" size="sm">
              Histórico de Importações
            </Button>
          </Link>
          <Link href="/dashboard/borderos/diagnostico">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Diagnóstico
            </Button>
          </Link>
        </div>
      </div>

      {/* Indicador de progresso */}
      <div className="flex items-center gap-4">
        {['upload', 'mapping', 'validation', 'import'].map((s, index) => (
          <div key={s} className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === s ? 'bg-blue-600 text-white' :
              ['upload', 'mapping', 'validation', 'import'].indexOf(step) > index ? 'bg-green-600 text-white' :
              'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
            }`}>
              {index + 1}
            </div>
            <span className="text-sm font-medium capitalize">
              {s === 'upload' ? 'Upload' : s === 'mapping' ? 'Mapeamento' : s === 'validation' ? 'Validação' : 'Importação'}
            </span>
            {index < 3 && <div className="w-8 h-px bg-gray-300 dark:bg-gray-600" />}
          </div>
        ))}
      </div>

      {/* Conteúdo baseado no step */}
      {step === 'upload' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Upload da Planilha
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              <input {...getInputProps()} />
              <FileSpreadsheet className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              {isDragActive ? (
                <p className="text-lg">Solte o arquivo aqui...</p>
              ) : (
                <div>
                  <p className="text-lg mb-2">Arraste e solte uma planilha Excel aqui</p>
                  <p className="text-sm text-muted-foreground mb-4">ou clique para selecionar</p>
                  <Button variant="outline">Selecionar Arquivo</Button>
                </div>
              )}
            </div>
            {/* Configurações Avançadas de Processamento */}
            <div className="mt-6 bg-slate-50 dark:bg-slate-900 p-4 rounded-lg">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                ⚙️ Configurações de Processamento
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasHeader"
                      checked={hasHeader}
                      onCheckedChange={(checked) => setHasHeader(checked as boolean)}
                    />
                    <Label htmlFor="hasHeader" className="text-sm font-medium">
                      ✅ A primeira linha contém cabeçalhos
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    Marque se a primeira linha da planilha contém os nomes das colunas.
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="skipLines" className="text-sm font-medium">
                      ⏭️ Pular linhas no início
                    </Label>
                    <Input
                      id="skipLines"
                      type="number"
                      min="0"
                      max="10"
                      value={skipLines}
                      onChange={(e) => setSkipLines(parseInt(e.target.value) || 0)}
                      className="w-24"
                      placeholder="0"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Útil para pular linhas de título, informações extras ou espaços em branco.
                  </p>
                </div>
              </div>

              {/* Dicas de uso */}
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/30 rounded border border-blue-200 dark:border-blue-800">
                <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">💡 Dicas de Uso:</h5>
                <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Configure as opções antes de fazer o upload para melhor processamento.</li>
                  <li>• Use "Pular linhas" se sua planilha tem títulos ou informações extras no topo.</li>
                  <li>• O preview mostrará exatamente como os dados serão interpretados.</li>
                </ul>
              </div>
            </div>

            {/* Preview dos Dados Brutos (se arquivo foi carregado) */}
            {previewData.length > 0 && (
              <div className="mt-6 bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  👁️ Preview dos Dados Brutos (5 primeiras linhas)
                </h4>
                <div className="overflow-x-auto">
                  <Table>
                    <TableBody>
                      {previewData.map((row: any, index) => (
                        <TableRow
                          key={index}
                          className={
                            index === 0 && hasHeader
                              ? 'font-medium bg-blue-100 dark:bg-blue-900 border-2 border-blue-300'
                              : index < skipLines
                                ? 'opacity-50 bg-gray-100 dark:bg-gray-800 line-through'
                                : ''
                          }
                        >
                          <TableCell className="w-12 text-xs font-mono text-gray-500">
                            {index + 1}
                          </TableCell>
                          {Array.isArray(row) ? row.map((cell, cellIndex) => (
                            <TableCell key={cellIndex} className="text-xs max-w-[120px] truncate">
                              {String(cell || '').substring(0, 30)}
                              {String(cell || '').length > 30 ? '...' : ''}
                            </TableCell>
                          )) : Object.values(row).map((cell, cellIndex) => (
                            <TableCell key={cellIndex} className="text-xs max-w-[120px] truncate">
                              {String(cell || '').substring(0, 30)}
                              {String(cell || '').length > 30 ? '...' : ''}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Legenda do Preview */}
                <div className="mt-3 text-xs text-muted-foreground space-y-1">
                  <p className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 rounded"></span>
                    {hasHeader ? '🟦 Linha azul = cabeçalho detectado' : '⚪ Sem cabeçalho - colunas serão numeradas automaticamente'}
                  </p>
                  {skipLines > 0 && (
                    <p className="flex items-center gap-2">
                      <span className="w-4 h-4 bg-gray-100 dark:bg-gray-800 rounded opacity-50"></span>
                      🚫 Linhas riscadas = serão puladas ({skipLines} linha{skipLines > 1 ? 's' : ''})
                    </p>
                  )}
                  <p>📊 Total de linhas válidas que serão processadas: <strong>{excelData.length}</strong></p>
                </div>

                <div className="mt-4 flex gap-3">
                  <Button onClick={() => setStep('mapping')} className="bg-blue-600 hover:bg-blue-700">
                    ➡️ Continuar para Mapeamento
                  </Button>
                  <Button variant="outline" onClick={handleResetImport}>
                    🔄 Carregar Outro Arquivo
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-4 text-sm text-muted-foreground">
              <p>• Formatos aceitos: .xlsx, .xls</p>
              <p>• Colunas recomendadas: Código, Empresa, Valor, Tipo, Secretaria, Status, Data</p>
              <p>• Configure as opções acima antes de fazer o upload</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Mapeamento de Colunas */}
      {step === 'mapping' && excelColumns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              🔗 Mapeamento de Colunas
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Mapeie as colunas da planilha para os campos do sistema. Campos obrigatórios: Código, Empresa e Valor.
            </p>
            {Object.keys(columnMapping).length > 0 && (
              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg mt-2">
                <p className="text-sm text-green-800 dark:text-green-200">
                  🤖 <strong>Mapeamento automático aplicado!</strong> Verifique se os campos estão corretos e ajuste se necessário.
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Indicadores de campos obrigatórios */}
            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">📋 Campos Obrigatórios</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Código do Bordero
                </Badge>
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Nome da Empresa
                </Badge>
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Valor
                </Badge>
              </div>
              <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">
                Estes campos são obrigatórios para a importação. Certifique-se de mapeá-los corretamente.
              </p>
            </div>

            {/* Mapeamento dos campos do sistema */}
            <div className="space-y-4">
              {/* Código do Bordero */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">🔢 Código do Bordero *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('bordero_cod')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'bordero_cod') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'bordero_cod'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`bordero-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o código do bordero
                </div>
              </div>

              {/* Valor */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">💰 Valor *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('valor')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'valor') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'valor'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`valor-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o valor monetário
                </div>
              </div>

              {/* Nome da Empresa */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">🏢 Nome da Empresa *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('nome_empresa')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'nome_empresa') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'nome_empresa'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`empresa-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o nome da empresa
                </div>
              </div>

              {/* Data */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📅 Data</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('data')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'data') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'data'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`data-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém a data
                </div>
              </div>

              {/* Secretaria */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">🏛️ Secretaria</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('secretaria_id')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'secretaria_id') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'secretaria_id'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`secretaria-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém a secretaria
                </div>
              </div>

              {/* Tipo de Bordero */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📋 Tipo de Bordero</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('tipo_id')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'tipo_id') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'tipo_id'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`tipo-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o tipo do bordero
                </div>
              </div>

              {/* Direcionamento */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📍 Direcionamento</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('direcionamento_id')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'direcionamento_id') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'direcionamento_id'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`direcionamento-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o direcionamento
                </div>
              </div>

              {/* Observação */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📝 Observação</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('observacao')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'observacao') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'observacao'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`observacao-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém observações
                </div>
              </div>
            </div>

            <Separator />

            {/* Preview dos dados com mapeamento */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Eye className="w-4 h-4" />
                👁️ Preview dos Dados (5 primeiras linhas)
              </h4>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {excelColumns.map((column, colIndex) => {
                        const mappedField = columnMapping[column]
                        const isRequired = ['bordero_cod', 'nome_empresa', 'valor'].includes(mappedField || '')
                        return (
                          <TableHead key={`header-${colIndex}-${column}`} className={isRequired ? 'bg-red-50 dark:bg-red-950/20' : ''}>
                            <div className="space-y-1">
                              <div className="font-medium">{column}</div>
                              <div className="text-xs text-muted-foreground">
                                {mappedField && mappedField !== 'unmapped' ? (
                                  <Badge variant="outline" className={isRequired ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}>
                                    {mappedField === 'bordero_cod' ? '🔢 Código' :
                                     mappedField === 'nome_empresa' ? '🏢 Empresa' :
                                     mappedField === 'valor' ? '💰 Valor' :
                                     mappedField === 'tipo_id' ? '📋 Tipo' :
                                     mappedField === 'secretaria_id' ? '🏛️ Secretaria' :
                                     mappedField === 'direcionamento_id' ? '📍 Direcionamento' :
                                     mappedField === 'status' ? '📊 Status' :
                                     mappedField === 'data' ? '📅 Data' :
                                     mappedField === 'observacao' ? '📝 Observação' : mappedField}
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-gray-100 text-gray-600">
                                    ❌ Não mapeado
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {excelData.slice(0, 5).map((row, rowIndex) => (
                      <TableRow key={`row-${rowIndex}`}>
                        {excelColumns.map((column, colIndex) => {
                          const mappedField = columnMapping[column]
                          const isRequired = ['bordero_cod', 'nome_empresa', 'valor'].includes(mappedField || '')
                          return (
                            <TableCell key={`cell-${rowIndex}-${colIndex}-${column}`} className={`text-xs ${isRequired ? 'bg-red-50 dark:bg-red-950/20 font-medium' : ''}`}>
                              {String(row[column] || '').substring(0, 30)}
                              {String(row[column] || '').length > 30 ? '...' : ''}
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-xs text-muted-foreground">
                🔴 Colunas destacadas em vermelho = campos obrigatórios mapeados |
                🔵 Colunas azuis = campos opcionais mapeados |
                ⚪ Colunas cinzas = não mapeadas
              </p>
            </div>

            <div className="flex gap-4">
              <Button variant="outline" onClick={() => setStep('upload')}>
                Voltar
              </Button>
              <Button
                onClick={() => {
                  // Garante que os dados de referência estão carregados antes da validação
                  if (secretarias.length === 0 || tipos.length === 0 || direcionamentos.length === 0) {
                    loadReferenceData().then(() => validateMappedData());
                  } else {
                    validateMappedData();
                  }
                }}
                disabled={
                  !Object.values(columnMapping).includes('bordero_cod') ||
                  !Object.values(columnMapping).includes('valor') ||
                  !Object.values(columnMapping).includes('nome_empresa')
                }
              >
                Continuar para Validação
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Validação */}
      {step === 'validation' && (
        <Card>
          <CardHeader>
            <CardTitle>Validação dos Dados</CardTitle>
            <p className="text-sm text-muted-foreground">
              Revise os dados antes da importação
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Resumo de validação */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">✅ Válidos</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {mappedData.filter(row => row.status === 'valid').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium">⚠️ Avisos</span>
                </div>
                <div className="text-2xl font-bold text-yellow-600">
                  {mappedData.filter(row => row.status === 'warning').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <XCircle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium">❌ Erros</span>
                </div>
                <div className="text-2xl font-bold text-red-600">
                  {mappedData.filter(row => row.status === 'error').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">📊 Total</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {mappedData.length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <BanknoteIcon className="w-4 h-4 text-emerald-600" />
                  <span className="text-sm font-medium">💰 Valor Total</span>
                </div>
                <div className="text-2xl font-bold text-emerald-600">
                  {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(
                    mappedData
                      .filter(row => row.status === 'valid')
                      .reduce((total, row) => total + (row.mapped.valor || 0), 0)
                  )}
                </div>
              </Card>
            </div>

            {/* Filtros de visualização */}
            <div className="flex items-center gap-4">
              <Label>Filtrar por status:</Label>
              <Select value={validationFilter} onValueChange={(value: any) => setValidationFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="valid">✅ Válidos</SelectItem>
                  <SelectItem value="warning">⚠️ Avisos</SelectItem>
                  <SelectItem value="error">❌ Erros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Borderos duplicados detectados */}
            {mappedData.filter(row => row.duplicate).length > 0 && (
              <Card className="border-red-200 bg-red-50 dark:bg-red-950/20 mb-4">
                <CardHeader>
                  <CardTitle className="text-red-800 dark:text-red-200 flex items-center gap-2">
                    <XCircle className="w-5 h-5" />
                    Borderos Duplicados Detectados ({mappedData.filter(row => row.duplicate).length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm mb-2">
                    Os seguintes borderos já existem no sistema e serão ignorados durante a importação:
                  </p>
                  <div className="flex flex-wrap gap-2 mt-1 max-h-32 overflow-y-auto">
                    {mappedData.filter(row => row.duplicate).map((row, idx) => (
                      <Badge key={idx} variant="outline" className="text-red-700 bg-red-100">
                        {row.mapped.bordero_cod}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Dados inconsistentes detectados */}
            {(inconsistentData.secretarias.length > 0 || inconsistentData.direcionamentos.length > 0 || inconsistentData.tipos.length > 0) && (
              <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20">
                <CardHeader>
                  <CardTitle className="text-yellow-800 dark:text-yellow-200 flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Dados Inconsistentes Detectados
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {inconsistentData.secretarias.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Secretarias não encontradas:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.secretarias.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {inconsistentData.direcionamentos.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Direcionamentos não encontrados:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.direcionamentos.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {inconsistentData.tipos.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Tipos não encontrados:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.tipos.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Código</TableHead>
                    <TableHead>Empresa</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Secretaria</TableHead>
                    <TableHead>Erros/Avisos</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mappedData
                    .filter(row => validationFilter === 'all' || row.status === validationFilter)
                    .slice(0, 50) // Limita a 50 linhas para melhor performance em preview
                    .map((row, index) => (
                    <TableRow key={index} className={
                      row.status === 'error' ? 'bg-red-50 dark:bg-red-950/20' :
                      row.status === 'warning' ? 'bg-yellow-50 dark:bg-yellow-950/20' :
                      'bg-green-50 dark:bg-green-950/20'
                    }>
                      <TableCell>{renderStatus(row.status)}</TableCell>
                      <TableCell className="font-mono text-sm">{row.mapped.bordero_cod || '-'}</TableCell>
                      <TableCell className="max-w-[200px] truncate">{row.mapped.nome_empresa || '-'}</TableCell>
                      <TableCell className="font-medium">
                        {row.mapped.valor ?
                          new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(row.mapped.valor)
                          : '-'
                        }
                      </TableCell>
                      <TableCell>
                        {row.mapped.secretaria_nome ? row.mapped.secretaria_nome : <span className="text-red-600">Não encontrada</span>}
                      </TableCell>
                      <TableCell>
                        {row.errors.length > 0 && (
                          <div className="space-y-1">
                            {row.errors.map((error: string, i: number) => (
                              <div key={i} className="text-xs text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded">
                                {error}
                              </div>
                            ))}
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {mappedData.filter(row => validationFilter === 'all' || row.status === validationFilter).length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        Nenhum registro encontrado com o filtro selecionado.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="flex gap-4">
              <Button variant="outline" onClick={() => setStep('mapping')}>
                Voltar
              </Button>
              <Button
                onClick={executeImport}
                disabled={mappedData.filter(row => row.status === 'valid').length === 0}
              >
                Importar {mappedData.filter(row => row.status === 'valid').length} Borderos
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Importação */}
      {step === 'import' && (
        <Card>
          <CardHeader>
            <CardTitle>Importação em Andamento</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Progress
                value={importProgress}
                className="w-full h-4"
                max={100}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{progressCount} de {totalValidRowsRef.current} borderos</span>
                <span className="font-medium">{Math.round(importProgress)}% concluído</span>
              </div>
            </div>

            {importing && !importResults && (
              <div className="text-center">
                <p className="text-lg font-medium">
                  <Play className="inline-block w-5 h-5 mr-2 text-blue-600" />
                  Importação em andamento... ({mappedData.filter(row => row.status === 'valid').length} borderos sendo processados)
                </p>
                <div className="flex justify-center gap-4 mt-4">
                  <Button variant="outline" onClick={handleCancelImport} disabled={!importing || cancelled}>
                    Cancelar Importação
                  </Button>
                </div>
              </div>
            )}

            {importResults && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="p-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium">Importados</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {typeof importResults.success === 'number' ? importResults.success : 0}
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4 text-red-600" />
                      <span className="text-sm font-medium">Falhas</span>
                    </div>
                    <div className="text-2xl font-bold text-red-600">
                      {Array.isArray(importResults.errors) ? importResults.errors.length : (typeof importResults.errors === 'number' ? importResults.errors : 0)}
                    </div>
                  </Card>
                </div>

                {importResults.errors && importResults.errors.length > 0 && (
                  <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
                    <CardHeader>
                      <CardTitle className="text-red-800 dark:text-red-200 flex items-center gap-2">
                        <XCircle className="w-5 h-5" />
                        Detalhes das Falhas
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {importResults.errors.map((error: any, index: number) => (
                        <div key={index} className="text-sm text-red-700 dark:text-red-300">
                          {error.message || "Erro desconhecido durante a importação."}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                <div className="flex gap-4">
                  <Button onClick={() => router.push('/dashboard/borderos')}>
                    Ver Borderos
                  </Button>
                  <Button variant="outline" onClick={handleResetImport}>
                    Nova Importação
                  </Button>
                  <Button
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:text-green-800"
                    onClick={() => generatePDFReport()}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Baixar Relatório
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-6 bg-slate-900/80 p-4 rounded-lg">
              <div className="flex gap-4 mb-2">
                <span>Progresso: {progressCount} / {mappedData.filter(row => row.status === 'valid').length}</span>
                <span className="text-green-500">Sucessos: {successCount}</span>
                <span className="text-red-500">Falhas: {errorCount}</span>
              </div>
              <div className="mb-2">Registro atual: {currentRecordRef.current?.bordero_cod} - {currentRecordRef.current?.nome_empresa}</div>
              <div className="h-32 overflow-y-auto bg-black/30 rounded p-2 text-xs">
                {logs.map((log: {status: string, message: string}, i: number) => (
                  <div key={i} className={log.status==='success'?'text-green-400':'text-red-400'}>{log.message}</div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
  
}

// Função para verificar se um bordero já existe no sistema
async function checkBorderoExists(borderoCod: string): Promise<boolean> {
  try {
    // Adiciona um timestamp para evitar cache do navegador
    const timestamp = new Date().getTime();
    const url = `/api/borderos/check?codigo=${encodeURIComponent(borderoCod)}&_t=${timestamp}`;
    
    console.log(`Verificando existência do bordero: ${borderoCod}`);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Erro ao verificar bordero (${response.status}):`, errorText);
      throw new Error(`Erro ao verificar bordero: ${response.status} ${errorText}`);
    }
    
    const data = await response.json();
    console.log(`Resultado da verificação para ${borderoCod}:`, data);
    return data.exists;
  } catch (error) {
    console.error("Erro ao verificar existência do bordero:", error);
    // Em caso de erro na API, assumimos que não existe para evitar bloquear a importação
    // mas logamos o erro para diagnóstico
    return false;
  }
}