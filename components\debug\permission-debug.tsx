"use client"

import { useSupabase } from "@/lib/supabase-provider"
import { usePermissions } from "@/hooks/use-permissions"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function PermissionDebug() {
  const { user, userDetails, userPermissions, loading } = useSupabase()
  const { hasPermission, isAdmin } = usePermissions()

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Debug de Permissões</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Carregando...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Debug de Permissões</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-semibold">Usu<PERSON><PERSON> Autenticado:</h4>
          <p>{user ? "✅ Sim" : "❌ Não"}</p>
          {user && <p className="text-sm text-muted-foreground">ID: {user.id}</p>}
        </div>

        <div>
          <h4 className="font-semibold">Detalhes do Usuário:</h4>
          {userDetails ? (
            <div className="space-y-1">
              <p>Nome: {userDetails.nome}</p>
              <p>Email: {userDetails.email}</p>
              <p>Nível de Acesso: {userDetails.nivel_acesso}</p>
              <p>É Admin: {isAdmin() ? "✅ Sim" : "❌ Não"}</p>
            </div>
          ) : (
            <p>❌ Não carregado</p>
          )}
        </div>

        <div>
          <h4 className="font-semibold">Permissões:</h4>
          {userPermissions ? (
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(userPermissions).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <Badge variant={value ? "default" : "secondary"}>
                    {key}: {value ? "✅" : "❌"}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <p>❌ Não carregadas</p>
          )}
        </div>

        <div>
          <h4 className="font-semibold">Teste de Permissões:</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              "dashboard",
              "borderos", 
              "secretarias",
              "direcionamentos",
              "tipos",
              "usuarios",
              "relatorios",
              "configuracoes"
            ].map((permission) => (
              <div key={permission} className="flex items-center gap-2">
                <Badge variant={hasPermission(permission as any) ? "default" : "secondary"}>
                  {permission}: {hasPermission(permission as any) ? "✅" : "❌"}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
