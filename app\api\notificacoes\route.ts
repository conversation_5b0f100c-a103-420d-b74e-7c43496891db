import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { pusherServer } from "@/lib/pusher"

export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json([], { status: 200 }) // Retornar array vazio em vez de erro
    }

    const usuarioId = session.user.id

    const { data: notificacoes, error } = await supabase
      .from("notificacoes")
      .select("*")
      .eq("usuario_id", usuarioId)
      .order("created_at", { ascending: false })
      .limit(20)

    if (error) {
      console.error("Erro ao buscar notificações:", error)
      return NextResponse.json([], { status: 200 }) // Retornar array vazio em caso de erro
    }

    // Garantir que estamos retornando um array
    return NextResponse.json(Array.isArray(notificacoes) ? notificacoes : [])
  } catch (error) {
    console.error("Erro ao buscar notificações:", error)
    return NextResponse.json([], { status: 200 }) // Retornar array vazio em caso de erro
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { titulo, mensagem, usuarioId } = body

    if (!titulo || !mensagem || !usuarioId) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 })
    }

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: notificacao, error } = await supabase
      .from("notificacoes")
      .insert({
        titulo,
        mensagem,
        usuario_id: usuarioId,
        lida: false,
      })
      .select()
      .single()

    if (error) {
      console.error("Erro ao criar notificação:", error)
      return NextResponse.json({ error: "Erro ao criar notificação" }, { status: 500 })
    }

    // Enviar notificação em tempo real
    await pusherServer.trigger("notificacoes", "nova-notificacao", {
      ...notificacao,
      createdAt: notificacao.created_at,
    })

    return NextResponse.json(notificacao)
  } catch (error) {
    console.error("Erro ao criar notificação:", error)
    return NextResponse.json({ error: "Erro ao criar notificação" }, { status: 500 })
  }
}
