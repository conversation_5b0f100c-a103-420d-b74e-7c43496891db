import { createClient } from "@supabase/supabase-js"

export type UserRole = "admin" | "gestor" | "usuario" | "visualizador"

export interface UserPermissions {
  canCreateBordero: boolean
  canEditBordero: boolean
  canDeleteBordero: boolean
  canViewAllBorderos: boolean
  canManageUsers: boolean
  canManageSecretarias: boolean
  canManageTipos: boolean
  canViewReports: boolean
  canChangeStatus: boolean
  canAccessAdmin: boolean
}

export const ROLE_PERMISSIONS: Record<UserRole, UserPermissions> = {
  admin: {
    canCreateBordero: true,
    canEditBordero: true,
    canDeleteBordero: true,
    canViewAllBorderos: true,
    canManageUsers: true,
    canManageSecretarias: true,
    canManageTipos: true,
    canViewReports: true,
    canChangeStatus: true,
    canAccessAdmin: true,
  },
  gestor: {
    canCreateBordero: true,
    canEditBordero: true,
    canDeleteBordero: false,
    canViewAllBorderos: true,
    canManageUsers: false,
    canManageSecretarias: false,
    canManageTipos: false,
    canViewReports: true,
    canChangeStatus: true,
    canAccessAdmin: false,
  },
  usuario: {
    canCreateBordero: true,
    canEditBordero: true,
    canDeleteBordero: false,
    canViewAllBorderos: false,
    canManageUsers: false,
    canManageSecretarias: false,
    canManageTipos: false,
    canViewReports: false,
    canChangeStatus: false,
    canAccessAdmin: false,
  },
  visualizador: {
    canCreateBordero: false,
    canEditBordero: false,
    canDeleteBordero: false,
    canViewAllBorderos: false,
    canManageUsers: false,
    canManageSecretarias: false,
    canManageTipos: false,
    canViewReports: false,
    canChangeStatus: false,
    canAccessAdmin: false,
  },
}

export async function getUserPermissions(userId: string): Promise<UserPermissions | null> {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: user, error } = await supabase
      .from("usuarios")
      .select("role")
      .eq("id", userId)
      .single()

    if (error || !user) {
      console.error("Erro ao buscar usuário:", error)
      return null
    }

    const role = user.role as UserRole
    return ROLE_PERMISSIONS[role] || ROLE_PERMISSIONS.visualizador
  } catch (error) {
    console.error("Erro ao obter permissões:", error)
    return null
  }
}

export async function checkPermission(
  userId: string,
  permission: keyof UserPermissions
): Promise<boolean> {
  const permissions = await getUserPermissions(userId)
  return permissions ? permissions[permission] : false
}

export async function requirePermission(
  userId: string,
  permission: keyof UserPermissions
): Promise<void> {
  const hasPermission = await checkPermission(userId, permission)
  if (!hasPermission) {
    throw new Error(`Permissão negada: ${permission}`)
  }
}

// Hook para usar no frontend
export function usePermissions() {
  // Esta função será implementada no frontend para usar com o contexto de autenticação
  return {
    hasPermission: (permission: keyof UserPermissions) => {
      // Implementar lógica do frontend
      return false
    },
    isAdmin: () => {
      // Implementar lógica do frontend
      return false
    },
    isGestor: () => {
      // Implementar lógica do frontend
      return false
    }
  }
}

// Função para criar usuário admin padrão
export async function createDefaultAdmin() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe um admin
    const { data: existingAdmin, error: checkError } = await supabase
      .from("usuarios")
      .select("id")
      .eq("role", "admin")
      .limit(1)

    if (checkError) {
      console.error("Erro ao verificar admin existente:", checkError)
      return
    }

    if (existingAdmin && existingAdmin.length > 0) {
      console.log("Admin já existe")
      return
    }

    // Criar usuário admin padrão
    const { data: admin, error: createError } = await supabase
      .from("usuarios")
      .insert({
        nome: "Administrador",
        email: "<EMAIL>",
        role: "admin",
        ativo: true,
      })
      .select()
      .single()

    if (createError) {
      console.error("Erro ao criar admin:", createError)
      return
    }

    console.log("Usuário admin criado:", admin)
    return admin
  } catch (error) {
    console.error("Erro ao criar admin padrão:", error)
  }
}
