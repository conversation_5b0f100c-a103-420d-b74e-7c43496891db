import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar logs do sistema com informações do usuário
    const { data: logs, error } = await supabaseAdmin
      .from("sistema_logs")
      .select(`
        *,
        usuario:usuarios(nome, email)
      `)
      .order("created_at", { ascending: false })
      .limit(100)

    if (error) {
      console.error("Erro ao buscar logs:", error)
      return NextResponse.json({ error: "Erro ao buscar logs" }, { status: 500 })
    }

    return NextResponse.json(logs || [])
  } catch (error) {
    console.error("Erro na API de logs:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
