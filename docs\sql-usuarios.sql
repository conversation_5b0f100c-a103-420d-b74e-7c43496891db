-- <PERSON><PERSON>r tabela de níveis de acesso
CREATE TABLE IF NOT EXISTS niveis_acesso (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(100) NOT NULL UNIQUE,
  descricao TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserir níveis de acesso padrão
INSERT INTO niveis_acesso (nome, descricao) VALUES
('Administrador', 'Acesso total ao sistema'),
('Operador', 'Acesso para operações básicas'),
('Visualizador', 'Apenas visualização de dados')
ON CONFLICT (nome) DO NOTHING;

-- <PERSON><PERSON>r tabel<PERSON> de usuários
CREATE TABLE IF NOT EXISTS usuarios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  se<PERSON>a VARCHAR(255) NOT NULL,
  nivel_acesso_id UUID REFERENCES niveis_acesso(id) ON DELETE SET NULL,
  ativo BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_usuarios_email ON usuarios(email);
CREATE INDEX IF NOT EXISTS idx_usuarios_nivel_acesso ON usuarios(nivel_acesso_id);
CREATE INDEX IF NOT EXISTS idx_usuarios_ativo ON usuarios(ativo);

-- Criar função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Criar triggers para atualizar updated_at
CREATE TRIGGER update_niveis_acesso_updated_at 
    BEFORE UPDATE ON niveis_acesso 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_usuarios_updated_at 
    BEFORE UPDATE ON usuarios 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir usuário administrador padrão (senha: admin123)
INSERT INTO usuarios (nome, email, senha, nivel_acesso_id) 
SELECT 
  'Administrador',
  '<EMAIL>',
  'admin123',
  na.id
FROM niveis_acesso na 
WHERE na.nome = 'Administrador'
ON CONFLICT (email) DO NOTHING;
