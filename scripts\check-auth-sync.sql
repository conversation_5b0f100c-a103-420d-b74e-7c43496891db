-- Script para verificar sincronização entre tabela usuarios e Supabase Auth

-- 1. Verificar usuários na tabela usuarios
SELECT 
    id,
    nome,
    email,
    created_at,
    nivel_acesso_id
FROM usuarios
ORDER BY created_at DESC;

-- 2. Verificar se há usuários órfãos (na tabela mas não no Auth)
-- Este script deve ser executado via API ou dashboard do Supabase
-- pois não podemos acessar auth.users diretamente via SQL

-- 3. Para verificar via API, use este endpoint:
-- GET /api/debug/check-auth-sync

-- 4. Verificar níveis de acesso
SELECT 
    u.nome,
    u.email,
    na.nome as nivel_acesso
FROM usuarios u
LEFT JOIN niveis_acesso na ON u.nivel_acesso_id = na.id
ORDER BY u.created_at DESC;

-- 5. Verificar logs de alteração de senha
SELECT 
    la.*,
    u.nome as usuario_nome
FROM log_atividades la
LEFT JOIN usuarios u ON la.usuario_id = u.id
WHERE la.acao = 'alterar_senha'
ORDER BY la.created_at DESC
LIMIT 10;
