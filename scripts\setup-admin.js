const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function setupAdmin() {
  try {
    console.log('🚀 Iniciando configuração do administrador...')

    // 1. Buscar ou criar usuário no Supabase Auth
    let userId = null

    // Primeiro, tentar buscar o usuário existente
    const { data: existingUsers } = await supabase.auth.admin.listUsers()
    const existingUser = existingUsers.users.find(u => u.email === '<EMAIL>')

    if (existingUser) {
      userId = existingUser.id
      console.log('✅ Usuário já existe no Auth:', userId)
    } else {
      // Se não existir, criar novo usuário
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'Admin@123',
        email_confirm: true
      })

      if (authError) {
        console.error('❌ Erro ao criar usuário no Auth:', authError.message)
        return
      }

      userId = authData.user.id
      console.log('✅ Usuário criado no Auth:', userId)
    }

    if (!userId) {
      console.error('❌ Não foi possível obter o ID do usuário')
      return
    }

    console.log('✅ Usuário criado/encontrado no Auth:', userId)

    // 2. Criar ou buscar nível de acesso administrador
    let nivelAdminId = null

    // Primeiro, tentar buscar o nível existente
    const { data: existingNivel, error: searchError } = await supabase
      .from('niveis_acesso')
      .select('id')
      .eq('nome', 'Administrador')
      .maybeSingle()

    if (existingNivel) {
      nivelAdminId = existingNivel.id
      console.log('✅ Nível de acesso encontrado:', nivelAdminId)
    } else {
      // Se não existir, criar o nível
      const { data: newNivel, error: createError } = await supabase
        .from('niveis_acesso')
        .insert({
          nome: 'Administrador',
          descricao: 'Acesso completo ao sistema',
          permissoes: {
            dashboard: true,
            borderos: true,
            secretarias: true,
            direcionamentos: true,
            tipos: true,
            usuarios: true,
            relatorios: true,
            configuracoes: true
          }
        })
        .select('id')
        .single()

      if (createError) {
        console.error('❌ Erro ao criar nível de acesso:', createError.message)
        return
      }

      nivelAdminId = newNivel.id
      console.log('✅ Nível de acesso criado:', nivelAdminId)
    }

    // 3. Inserir/atualizar usuário na tabela usuarios
    const { data: userData, error: userError } = await supabase
      .from('usuarios')
      .upsert({
        id: userId,
        email: '<EMAIL>',
        nome: 'Administrador do Sistema',
        nivel_acesso_id: nivelAdminId
      })
      .select()

    if (userError) {
      console.error('❌ Erro ao inserir usuário na tabela:', userError.message)
      return
    }

    console.log('✅ Usuário inserido na tabela usuarios')

    // 4. Verificar se tudo está funcionando
    const { data: testUser, error: testError } = await supabase
      .from('usuarios')
      .select('*, niveis_acesso(nome, permissoes)')
      .eq('id', userId)
      .single()

    if (testError) {
      console.error('❌ Erro ao verificar usuário:', testError.message)
      return
    }

    console.log('✅ Verificação concluída:')
    console.log('   - Nome:', testUser.nome)
    console.log('   - Email:', testUser.email)
    console.log('   - Nível:', testUser.niveis_acesso?.nome)
    console.log('   - Permissões:', JSON.stringify(testUser.niveis_acesso?.permissoes, null, 2))

    console.log('\n🎉 Configuração concluída com sucesso!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Senha: Admin@123')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

setupAdmin()
