-- =====================================================
-- SCRIPT PARA CORRIGIR PROBLEMAS DE IMPORTAÇÃO DE BORDEROS
-- Execute este SQL no Supabase SQL Editor
-- =====================================================

-- 1. VERIFICAR ESTRUTURA ATUAL DA TABELA BORDEROS
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'borderos' 
ORDER BY ordinal_position;

-- 2. VERIFICAR SE A TABELA EXISTE
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'borderos'
) as tabela_existe;

-- 3. VERIFICAR POLÍTICAS RLS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'borderos';

-- 4. <PERSON><PERSON><PERSON><PERSON><PERSON> CONSTRAINTS E CHAVES ESTRANGEIRAS
SELECT
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
LEFT JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.table_name = 'borderos';

-- 5. CORRIGIR ESTRUTURA DA TABELA SE NECESSÁRIO
DO $$
BEGIN
    -- Verificar se a tabela existe, se não, criar
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'borderos') THEN
        CREATE TABLE borderos (
            id SERIAL PRIMARY KEY,
            bordero_cod VARCHAR(255) UNIQUE NOT NULL,
            valor DECIMAL(15,2) NOT NULL,
            data TIMESTAMP WITH TIME ZONE NOT NULL,
            nome_empresa VARCHAR(255) NOT NULL,
            secretaria_id INTEGER REFERENCES secretarias(id),
            tipo_id INTEGER REFERENCES tipos(id),
            observacao TEXT,
            status VARCHAR(50) DEFAULT 'novo',
            dados_status TEXT,
            data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            responsavel_id UUID REFERENCES usuarios(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE 'Tabela borderos criada com sucesso';
    ELSE
        RAISE NOTICE 'Tabela borderos já existe';
    END IF;

    -- Verificar e adicionar colunas que podem estar faltando
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'borderos' AND column_name = 'responsavel_id'
    ) THEN
        ALTER TABLE borderos ADD COLUMN responsavel_id UUID REFERENCES usuarios(id);
        RAISE NOTICE 'Coluna responsavel_id adicionada';
    END IF;

    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'borderos' AND column_name = 'dados_status'
    ) THEN
        ALTER TABLE borderos ADD COLUMN dados_status TEXT;
        RAISE NOTICE 'Coluna dados_status adicionada';
    END IF;

    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'borderos' AND column_name = 'data_alteracao'
    ) THEN
        ALTER TABLE borderos ADD COLUMN data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE 'Coluna data_alteracao adicionada';
    END IF;
END $$;

-- 6. HABILITAR RLS E CRIAR POLÍTICAS CORRETAS
ALTER TABLE borderos ENABLE ROW LEVEL SECURITY;

-- Remover políticas existentes
DROP POLICY IF EXISTS "Permitir acesso total a borderos" ON borderos;
DROP POLICY IF EXISTS "borderos_policy" ON borderos;
DROP POLICY IF EXISTS "Enable all for authenticated users" ON borderos;
DROP POLICY IF EXISTS "borderos_all_access" ON borderos;

-- Criar política simples e funcional
CREATE POLICY "borderos_full_access" ON borderos
FOR ALL TO authenticated
USING (true)
WITH CHECK (true);

-- 7. CRIAR ÍNDICES PARA PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_borderos_bordero_cod ON borderos(bordero_cod);
CREATE INDEX IF NOT EXISTS idx_borderos_status ON borderos(status);
CREATE INDEX IF NOT EXISTS idx_borderos_secretaria_id ON borderos(secretaria_id);
CREATE INDEX IF NOT EXISTS idx_borderos_tipo_id ON borderos(tipo_id);
CREATE INDEX IF NOT EXISTS idx_borderos_responsavel_id ON borderos(responsavel_id);
CREATE INDEX IF NOT EXISTS idx_borderos_data ON borderos(data);
CREATE INDEX IF NOT EXISTS idx_borderos_created_at ON borderos(created_at);

-- 8. VERIFICAR TABELAS RELACIONADAS
SELECT 'secretarias' as tabela, COUNT(*) as registros FROM secretarias
UNION ALL
SELECT 'tipos' as tabela, COUNT(*) as registros FROM tipos
UNION ALL
SELECT 'usuarios' as tabela, COUNT(*) as registros FROM usuarios;

-- 9. INSERIR DADOS BÁSICOS SE NÃO EXISTIREM
INSERT INTO secretarias (nome, slug, valores_total)
SELECT 'Secretaria Padrão', 'secretaria-padrao', 0
WHERE NOT EXISTS (SELECT 1 FROM secretarias);

INSERT INTO tipos (nome, descricao)
SELECT 'Tipo Padrão', 'Tipo padrão para borderos'
WHERE NOT EXISTS (SELECT 1 FROM tipos);

-- 10. VERIFICAÇÃO FINAL
SELECT 
    'borderos' as tabela,
    COUNT(*) as total_registros,
    COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
    COUNT(CASE WHEN status = 'em_andamento' THEN 1 END) as em_andamento,
    COUNT(CASE WHEN status = 'concluido' THEN 1 END) as concluidos
FROM borderos;

-- 11. TESTAR INSERÇÃO SIMPLES
DO $$
DECLARE
    test_secretaria_id INTEGER;
    test_tipo_id INTEGER;
    test_user_id UUID;
BEGIN
    -- Buscar IDs para teste
    SELECT id INTO test_secretaria_id FROM secretarias LIMIT 1;
    SELECT id INTO test_tipo_id FROM tipos LIMIT 1;
    SELECT id INTO test_user_id FROM usuarios LIMIT 1;
    
    -- Tentar inserir um bordero de teste
    IF test_secretaria_id IS NOT NULL AND test_tipo_id IS NOT NULL THEN
        INSERT INTO borderos (
            bordero_cod,
            valor,
            data,
            nome_empresa,
            secretaria_id,
            tipo_id,
            status,
            responsavel_id
        ) VALUES (
            'TEST-' || EXTRACT(EPOCH FROM NOW())::TEXT,
            1000.00,
            NOW(),
            'Empresa Teste',
            test_secretaria_id,
            test_tipo_id,
            'novo',
            test_user_id
        );
        
        RAISE NOTICE 'Teste de inserção bem-sucedido';
        
        -- Remover o registro de teste
        DELETE FROM borderos WHERE bordero_cod LIKE 'TEST-%';
        RAISE NOTICE 'Registro de teste removido';
    ELSE
        RAISE NOTICE 'Não foi possível fazer teste - faltam dados básicos';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro no teste de inserção: %', SQLERRM;
END $$;

-- 12. RESULTADO FINAL
SELECT 'DIAGNÓSTICO COMPLETO - Tabela borderos está pronta para importação' as status;
