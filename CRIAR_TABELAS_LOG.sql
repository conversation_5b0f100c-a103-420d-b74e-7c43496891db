-- =====================================================
-- CRIAR TABELAS DE LOG - EXECUTE NO SUPABASE
-- =====================================================

-- 1. CRIAR TABELA LOG_ATIVIDADES
CREATE TABLE log_atividades (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  usuario_id UUID REFERENCES usuarios(id),
  acao VARCHAR(100) NOT NULL,
  entidade VARCHAR(100) NOT NULL,
  entidade_id UUID,
  detalhes JSONB,
  ip VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CRIAR TABELA BORDERO_LOGS
CREATE TABLE bordero_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  bordero_id UUID REFERENCES borderos(id),
  usuario_id UUID REFERENCES usuarios(id),
  acao VARCHAR(100) NOT NULL,
  detalhes TEXT,
  data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. HABILITAR RLS
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;
ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;

-- 4. CRIAR POLÍTICAS
CREATE POLICY "Permitir acesso total a log_atividades" ON log_atividades
FOR ALL USING (true);

CREATE POLICY "Permitir acesso total a bordero_logs" ON bordero_logs
FOR ALL USING (true);

-- 5. INSERIR LOG DE CRIAÇÃO PARA O BORDERO EXISTENTE
INSERT INTO bordero_logs (bordero_id, usuario_id, acao, detalhes, data_hora)
SELECT 
  b.id,
  b.responsavel_id,
  'criar',
  'Bordero criado no sistema',
  b.created_at
FROM borderos b
WHERE b.bordero_cod = 'BOR-2025-01';

-- 6. VERIFICAR SE AS TABELAS FORAM CRIADAS
SELECT 'log_atividades' as tabela, COUNT(*) as registros FROM log_atividades
UNION ALL
SELECT 'bordero_logs' as tabela, COUNT(*) as registros FROM bordero_logs;
