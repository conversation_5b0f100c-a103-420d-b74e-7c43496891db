"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON> } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { CurrencyInput } from "@/components/ui/currency-input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker"
import { toast } from "@/hooks/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { ArrowLeft, Save, Trash2 } from "lucide-react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import Link from "next/link"

interface Bordero {
  id: string
  bordero_cod: string
  valor: number
  data: string
  nome_empresa: string
  secretaria_id: string
  tipo_id: string
  observacao: string
  status: string
  created_at: string
  updated_at: string
  secretaria: {
    id: string
    nome: string
  }
  tipo: {
    id: string
    nome: string
  }
  responsavel?: {
    id: string
    nome: string
    email: string
  }
}

interface Secretaria {
  id: string
  nome: string
}

interface Tipo {
  id: string
  nome: string
}

interface Usuario {
  id: string
  nome: string
  email: string
}

export default function EditarBorderoPage() {
  const router = useRouter()
  const params = useParams()
  const notify = useNotify()
  const borderoId = params.id as string

  const [bordero, setBordero] = useState<Bordero | null>(null)
  const [secretarias, setSecretarias] = useState<Secretaria[]>([])
  const [tipos, setTipos] = useState<Tipo[]>([])
  const [usuarios, setUsuarios] = useState<Usuario[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteReason, setDeleteReason] = useState("")
  const [deleting, setDeleting] = useState(false)

  const [formData, setFormData] = useState({
    bordero_cod: "",
    valor: "",
    data: "",
    nome_empresa: "",
    secretaria_id: "",
    tipo_id: "",
    observacao: "",
    responsavel_id: "",
  })

  useEffect(() => {
    fetchBordero()
    fetchSecretarias()
    fetchTipos()
    fetchUsuarios()
  }, [borderoId])

  const fetchBordero = async () => {
    try {
      const response = await fetch(`/api/borderos/${borderoId}`)
      if (!response.ok) {
        if (response.status === 404) {
          setBordero(null)
          setLoading(false)
          return
        }
        throw new Error("Erro ao buscar bordero")
      }

      const data = await response.json()
      setBordero(data)
      setFormData({
        bordero_cod: data.bordero_cod,
        valor: data.valor.toString(),
        data: data.data,
        nome_empresa: data.nome_empresa,
        secretaria_id: data.secretaria_id,
        tipo_id: data.tipo_id,
        observacao: data.observacao || "",
        responsavel_id: data.responsavel?.id || "",
      })
    } catch (error) {
      console.error("Erro ao buscar bordero:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar o bordero.",
        variant: "destructive",
      })
      setBordero(null)
      setLoading(false)
    }
  }

  const fetchSecretarias = async () => {
    try {
      const response = await fetch("/api/secretarias")
      if (!response.ok) throw new Error("Erro ao buscar secretarias")
      const data = await response.json()
      setSecretarias(data)
    } catch (error) {
      console.error("Erro ao buscar secretarias:", error)
    }
  }

  const fetchTipos = async () => {
    try {
      const response = await fetch("/api/tipos")
      if (!response.ok) throw new Error("Erro ao buscar tipos")
      const data = await response.json()
      setTipos(data)
    } catch (error) {
      console.error("Erro ao buscar tipos:", error)
    }
  }

  const fetchUsuarios = async () => {
    try {
      // Simulando usuários - em produção viria de uma API real
      const usuariosSimulados = [
        { id: "1", nome: "João Silva", email: "<EMAIL>" },
        { id: "2", nome: "Maria Santos", email: "<EMAIL>" },
        { id: "3", nome: "Pedro Costa", email: "<EMAIL>" },
        { id: "4", nome: "Ana Oliveira", email: "<EMAIL>" },
      ]
      setUsuarios(usuariosSimulados)
      setLoading(false)
    } catch (error) {
      console.error("Erro ao buscar usuários:", error)
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const response = await fetch(`/api/borderos/${borderoId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          valor: parseFloat(formData.valor),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao atualizar bordero")
      }

      notify.success(
        "Bordero Atualizado",
        "Bordero " + formData.bordero_cod + " foi atualizado com sucesso!"
      )

      router.push("/dashboard/borderos")
    } catch (error: any) {
      console.error("Erro ao atualizar bordero:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível atualizar o bordero.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleDelete = async () => {
    if (!deleteReason.trim()) {
      toast({
        title: "Erro",
        description: "Informe o motivo do cancelamento.",
        variant: "destructive",
      })
      return
    }

    setDeleting(true)

    try {
      const response = await fetch(`/api/borderos/${borderoId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          motivo: deleteReason,
        }),
      })

      if (!response.ok) {
        let errorMessage = "Erro ao excluir bordero"
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          console.error("Erro ao fazer parse da resposta de erro:", parseError)
          errorMessage = "Erro HTTP " + response.status + ": " + response.statusText
        }
        throw new Error(errorMessage)
      }

      // Tentar fazer parse da resposta de sucesso
      try {
        const successData = await response.json()
        console.log("Bordero cancelado com sucesso:", successData)
      } catch (parseError) {
        console.log("Resposta de sucesso não é JSON válido, mas operação foi bem-sucedida")
      }

      notify.success(
        "Bordero Cancelado",
        "Bordero " + (formData.bordero_cod || borderoId) + " foi cancelado com sucesso!"
      )

      // Fechar o diálogo
      setDeleteDialogOpen(false)
      setDeleteReason("")

      // Redirecionar após um pequeno delay
      setTimeout(() => {
        router.push("/dashboard/borderos")
      }, 1000)
    } catch (error: any) {
      console.error("Erro ao excluir bordero:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir o bordero.",
        variant: "destructive",
      })
    } finally {
      setDeleting(false)
    }
  }

  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "novo":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
            Novo
          </Badge>
        )
      case "analise":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
            Em Análise
          </Badge>
        )
      case "assinado":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
            Assinado
          </Badge>
        )
      case "pago":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">
            Pago
          </Badge>
        )
      case "corrigir":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
            Corrigir
          </Badge>
        )
      case "cancelado":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            Cancelado
          </Badge>
        )
      case "excluido":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            Excluído
          </Badge>
        )
      case "arquivado":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">
            Arquivado
          </Badge>
        )
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR")
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Editar Bordero</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-6 w-48" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-36" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-24 w-full" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-10 w-32" />
                <div className="flex gap-4">
                  <Skeleton className="h-10 w-20" />
                  <Skeleton className="h-10 w-32" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!bordero) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/borderos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">Editar Bordero</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Bordero não encontrado</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <div className="text-6xl">🔍</div>
              <div className="text-center space-y-2">
                <p className="text-lg font-medium">Bordero não encontrado</p>
                <p className="text-muted-foreground">
                  O bordero que você está tentando editar não existe ou foi removido.
                </p>
              </div>
              <Link href="/dashboard/borderos">
                <Button>Voltar para Lista de Borderos</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/borderos">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Editar Bordero</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informações do Bordero</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="bordero_cod">Código do Bordero</Label>
                <Input
                  id="bordero_cod"
                  value={formData.bordero_cod}
                  onChange={(e) => handleInputChange("bordero_cod", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="valor">Valor (R$)</Label>
                <CurrencyInput
                  id="valor"
                  placeholder="R$ 0,00"
                  value={formData.valor}
                  onChange={(value) => handleInputChange("valor", value.toString())}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="data">Data</Label>
                <EnhancedDatePicker
                  date={formData.data ? new Date(formData.data) : undefined}
                  onDateChange={(date) => handleInputChange("data", date ? date.toISOString().split('T')[0] : "")}
                  placeholder="Selecione uma data"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nome_empresa">Nome da Empresa</Label>
                <Input
                  id="nome_empresa"
                  value={formData.nome_empresa}
                  onChange={(e) => handleInputChange("nome_empresa", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="secretaria">Secretaria</Label>
                <Select value={formData.secretaria_id} onValueChange={(value) => handleInputChange("secretaria_id", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma secretaria" />
                  </SelectTrigger>
                  <SelectContent>
                    {secretarias.map((secretaria) => (
                      <SelectItem key={secretaria.id} value={secretaria.id}>
                        {secretaria.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo">Tipo</Label>
                <Select value={formData.tipo_id} onValueChange={(value) => handleInputChange("tipo_id", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    {tipos.map((tipo) => (
                      <SelectItem key={tipo.id} value={tipo.id}>
                        {tipo.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacao">Observação</Label>
              <Textarea
                id="observacao"
                value={formData.observacao}
                onChange={(e) => handleInputChange("observacao", e.target.value)}
                rows={4}
              />
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="destructive"
                onClick={() => setDeleteDialogOpen(true)}
                disabled={saving || deleting}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Cancelar Bordero
              </Button>

              <div className="flex gap-4">
                <Link href="/dashboard/borderos">
                  <Button type="button" variant="outline">
                    Cancelar
                  </Button>
                </Link>
                <Button type="submit" disabled={saving || deleting}>
                  {saving ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></div>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Salvar Alterações
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Diálogo de Confirmação de Exclusão */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmar Cancelamento</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja cancelar o bordero <strong>{bordero?.bordero_cod}</strong>?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deleteReason">Motivo do cancelamento *</Label>
              <Textarea
                id="deleteReason"
                placeholder="Informe o motivo do cancelamento..."
                value={deleteReason}
                onChange={(e) => setDeleteReason(e.target.value)}
                rows={3}
                required
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false)
                setDeleteReason("")
              }}
              disabled={deleting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleting || !deleteReason.trim()}
            >
              {deleting ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></div>
                  Cancelando...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Cancelar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
