"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Filter, Search } from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

interface Secretaria {
  id: string
  nome: string
}

interface Tipo {
  id: string
  nome: string
}

interface FiltrosProps {
  onFiltrosChange: (filtros: any) => void
  filtrosAtivos: any
}

export default function FiltrosAvancados({ onFiltrosChange, filtrosAtivos }: FiltrosProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [secretarias, setSecretarias] = useState<Secretaria[]>([])
  const [tipos, setTipos] = useState<Tipo[]>([])

  const [filtros, setFiltros] = useState({
    codigo: "",
    empresa: "",
    secretariaId: "",
    tipoId: "",
    status: "",
    valorMin: "",
    valorMax: "",
    dataInicio: "",
    dataFim: "",
  })

  useEffect(() => {
    fetchSecretarias()
    fetchTipos()
  }, [])

  useEffect(() => {
    setFiltros(filtrosAtivos)
  }, [filtrosAtivos])

  const fetchSecretarias = async () => {
    try {
      const response = await fetch("/api/secretarias")
      if (response.ok) {
        const data = await response.json()
        setSecretarias(data)
      }
    } catch (error) {
      console.error("Erro ao buscar secretarias:", error)
    }
  }

  const fetchTipos = async () => {
    try {
      const response = await fetch("/api/tipos")
      if (response.ok) {
        const data = await response.json()
        setTipos(data)
      }
    } catch (error) {
      console.error("Erro ao buscar tipos:", error)
    }
  }

  const handleFiltroChange = (campo: string, valor: string) => {
    // Converter "all" para string vazia para filtros de select
    const valorFinal = valor === "all" ? "" : valor
    const novosFiltros = { ...filtros, [campo]: valorFinal }
    setFiltros(novosFiltros)
    onFiltrosChange(novosFiltros) // Aplicar filtros automaticamente
  }

  const aplicarFiltros = () => {
    onFiltrosChange(filtros)
  }

  const limparFiltros = () => {
    const filtrosLimpos = {
      codigo: "",
      empresa: "",
      secretariaId: "",
      tipoId: "",
      status: "",
      valorMin: "",
      valorMax: "",
      dataInicio: "",
      dataFim: "",
    }
    setFiltros(filtrosLimpos)
    onFiltrosChange(filtrosLimpos)
  }

  const contarFiltrosAtivos = () => {
    return Object.values(filtros).filter(valor => valor !== "").length
  }

  const getFiltroLabel = (campo: string, valor: string) => {
    switch (campo) {
      case "secretariaId":
        const secretaria = secretarias.find(s => s.id === valor)
        return secretaria ? `Secretaria: ${secretaria.nome}` : ""
      case "tipoId":
        const tipo = tipos.find(t => t.id === valor)
        return tipo ? `Tipo: ${tipo.nome}` : ""
      case "status":
        const statusLabels: { [key: string]: string } = {
          novo: "Novo",
          analise: "Em Análise",
          assinado: "Assinado",
          pago: "Pago",
          corrigir: "Corrigir"
        }
        return `Status: ${statusLabels[valor] || valor}`
      case "codigo":
        return `Código: ${valor}`
      case "empresa":
        return `Empresa: ${valor}`
      case "valorMin":
        return `Valor mín: R$ ${valor}`
      case "valorMax":
        return `Valor máx: R$ ${valor}`
      case "dataInicio":
        return `Data início: ${new Date(valor).toLocaleDateString("pt-BR")}`
      case "dataFim":
        return `Data fim: ${new Date(valor).toLocaleDateString("pt-BR")}`
      default:
        return ""
    }
  }

  const filtrosAtivosCount = contarFiltrosAtivos()

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filtros Avançados
                {filtrosAtivosCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {filtrosAtivosCount}
                  </Badge>
                )}
              </div>
              <Button variant="ghost" size="sm">
                {isOpen ? "Ocultar" : "Mostrar"}
              </Button>
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-6">
            {/* Filtros Ativos */}
            {filtrosAtivosCount > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Filtros Aplicados:</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(filtros).map(([campo, valor]) => {
                    if (!valor) return null
                    const label = getFiltroLabel(campo, valor)
                    if (!label) return null

                    return (
                      <Badge key={campo} variant="outline" className="flex items-center gap-1">
                        {label}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-500"
                          onClick={() => handleFiltroChange(campo, "")}
                        />
                      </Badge>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Campos de Filtro */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="codigo">Código do Bordero</Label>
                <Input
                  id="codigo"
                  placeholder="Ex: BOR-2024-001"
                  value={filtros.codigo}
                  onChange={(e) => handleFiltroChange("codigo", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="empresa">Nome da Empresa</Label>
                <Input
                  id="empresa"
                  placeholder="Ex: Empresa ABC"
                  value={filtros.empresa}
                  onChange={(e) => handleFiltroChange("empresa", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={filtros.status || "all"} onValueChange={(value) => handleFiltroChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os status</SelectItem>
                    <SelectItem value="novo">Novo</SelectItem>
                    <SelectItem value="analise">Em Análise</SelectItem>
                    <SelectItem value="assinado">Assinado</SelectItem>
                    <SelectItem value="pago">Pago</SelectItem>
                    <SelectItem value="corrigir">Corrigir</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="secretaria">Secretaria</Label>
                <Select value={filtros.secretariaId || "all"} onValueChange={(value) => handleFiltroChange("secretariaId", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todas as secretarias" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as secretarias</SelectItem>
                    {secretarias.map((secretaria) => (
                      <SelectItem key={secretaria.id} value={secretaria.id}>
                        {secretaria.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo">Tipo</Label>
                <Select value={filtros.tipoId || "all"} onValueChange={(value) => handleFiltroChange("tipoId", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os tipos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os tipos</SelectItem>
                    {tipos.map((tipo) => (
                      <SelectItem key={tipo.id} value={tipo.id}>
                        {tipo.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="valorMin">Valor Mínimo (R$)</Label>
                <Input
                  id="valorMin"
                  type="number"
                  step="0.01"
                  placeholder="0,00"
                  value={filtros.valorMin}
                  onChange={(e) => handleFiltroChange("valorMin", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="valorMax">Valor Máximo (R$)</Label>
                <Input
                  id="valorMax"
                  type="number"
                  step="0.01"
                  placeholder="0,00"
                  value={filtros.valorMax}
                  onChange={(e) => handleFiltroChange("valorMax", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dataInicio">Data Início</Label>
                <Input
                  id="dataInicio"
                  type="date"
                  value={filtros.dataInicio}
                  onChange={(e) => handleFiltroChange("dataInicio", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dataFim">Data Fim</Label>
                <Input
                  id="dataFim"
                  type="date"
                  value={filtros.dataFim}
                  onChange={(e) => handleFiltroChange("dataFim", e.target.value)}
                />
              </div>
            </div>

            {/* Botões de Ação */}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={limparFiltros}>
                Limpar Filtros
              </Button>
              <Button onClick={aplicarFiltros}>
                <Search className="mr-2 h-4 w-4" />
                Aplicar Filtros
              </Button>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
