"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState, useRef } from "react"
import { getSupabaseClient } from "@/lib/supabase-client"
import type { User } from "@supabase/auth-helpers-nextjs"
import { useRouter } from "next/navigation"

type UserDetails = {
  id: string
  nome: string
  email: string
  nivel_acesso_id: string
  nivel_acesso?: string
  avatar_url?: string
}

type UserPermissions = {
  dashboard: boolean
  borderos: boolean
  secretarias: boolean
  direcionamentos: boolean
  tipos: boolean
  usuarios: boolean
  relatorios: boolean
  configuracoes: boolean
}

type SupabaseContext = {
  user: User | null
  userDetails: UserDetails | null
  userPermissions: UserPermissions | null
  loading: boolean
  signOut: () => Promise<void>
}

const Context = createContext<SupabaseContext | undefined>(undefined)

export default function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null)
  const [userPermissions, setUserPermissions] = useState<UserPermissions | null>(null)
  const [loading, setLoading] = useState(true)

  const router = useRouter()
  const supabase = getSupabaseClient()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Cache para dados do usuário e permissões
  const userDataCache = useRef<{
    userData: any;
    nivelData: any;
    timestamp: number;
  } | null>(null);
  
  // Tempo de expiração do cache (5 minutos)
  const CACHE_EXPIRATION = 5 * 60 * 1000;

  useEffect(() => {
    const getUser = async () => {
      try {
        setLoading(true)
        console.time('auth-flow');

        // Obter a sessão atual
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("Erro ao obter sessão:", sessionError)
          setLoading(false)
          console.timeEnd('auth-flow');
          return
        }

        if (!session) {
          setLoading(false)
          console.timeEnd('auth-flow');
          return
        }

        setUser(session.user)

        // Verificar se temos dados em cache e se ainda são válidos
        const now = Date.now();
        const cacheValid = userDataCache.current &&
                          (now - userDataCache.current.timestamp < CACHE_EXPIRATION);

        // Buscar detalhes do usuário
        if (session.user) {
          let userData, nivelData;
          
          if (cacheValid) {
            console.log("🔄 Usando dados em cache para usuário e permissões");
            userData = userDataCache.current?.userData;
            nivelData = userDataCache.current?.nivelData;
          } else {
            console.log("🔄 Buscando dados atualizados do usuário e permissões");
            // Buscar dados do usuário e nível de acesso em paralelo
            const [userResponse, nivelResponse] = await Promise.all([
              supabase
                .from("usuarios")
                .select("*")
                .eq("id", session.user.id)
                .single(),
              
              // Primeiro precisamos obter o nivel_acesso_id
              supabase
                .from("usuarios")
                .select("nivel_acesso_id")
                .eq("id", session.user.id)
                .single()
                .then(async (res) => {
                  if (res.error || !res.data) return { data: null, error: res.error };
                  
                  // Agora buscamos os detalhes do nível
                  return supabase
                    .from("niveis_acesso")
                    .select("nome, permissoes")
                    .eq("id", res.data.nivel_acesso_id as string)
                    .single();
                })
            ]);
            
            if (userResponse.error) {
              console.error("Erro ao buscar detalhes do usuário:", userResponse.error);
            } else {
              userData = userResponse.data;
            }
            
            if (nivelResponse.error) {
              console.error("Erro ao buscar nível de acesso:", nivelResponse.error);
            } else {
              nivelData = nivelResponse.data;
            }
            
            // Atualizar o cache
            if (userData && nivelData) {
              userDataCache.current = {
                userData,
                nivelData,
                timestamp: now
              };
            }
          }

          if (userData) {
            setUserDetails({
              id: userData.id as string,
              nome: userData.nome as string,
              email: userData.email as string,
              nivel_acesso_id: userData.nivel_acesso_id as string,
              nivel_acesso: nivelData?.nome as string,
              avatar_url: userData.avatar_url as string,
            })

            if (nivelData?.permissoes) {
              console.log("🔍 Permissões carregadas:", {
                usuario: userData.nome,
                nivel: nivelData.nome
              })
              setUserPermissions(nivelData.permissoes as UserPermissions)
            } else {
              console.log("⚠️ Nenhuma permissão encontrada para:", {
                usuario: userData.nome,
                nivel_id: userData.nivel_acesso_id
              })
            }
          }
        }
      } catch (error) {
        console.error("Erro ao inicializar autenticação:", error)
      } finally {
        setLoading(false)
        console.timeEnd('auth-flow');
      }
    }

    getUser()

    // Configurar listener para mudanças de autenticação
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event, session?.user?.id)

      if (event === "SIGNED_IN" && session) {
        setUser(session.user)
        router.refresh()

        // Buscar detalhes do usuário
        if (session.user) {
          const { data: userData, error: userError } = await supabase
            .from("usuarios")
            .select("*")
            .eq("id", session.user.id)
            .single()

          if (userError) {
            console.error("Erro ao buscar detalhes do usuário:", userError)
          } else if (userData) {
            // Buscar nível de acesso separadamente
            const { data: nivelData } = await supabase
              .from("niveis_acesso")
              .select("nome, permissoes")
              .eq("id", userData.nivel_acesso_id as string)
              .single()

            setUserDetails({
              id: userData.id as string,
              nome: userData.nome as string,
              email: userData.email as string,
              nivel_acesso_id: userData.nivel_acesso_id as string,
              nivel_acesso: nivelData?.nome as string,
              avatar_url: userData.avatar_url as string,
            })

            if (nivelData?.permissoes) {
              console.log("🔍 Permissões carregadas (auth change):", {
                usuario: userData.nome,
                nivel: nivelData.nome,
                permissoes: nivelData.permissoes
              })
              setUserPermissions(nivelData.permissoes as UserPermissions)
            } else {
              console.log("⚠️ Nenhuma permissão encontrada (auth change):", {
                usuario: userData.nome,
                nivel_id: userData.nivel_acesso_id,
                nivel_data: nivelData
              })
            }
          }
        }
      } else if (event === "SIGNED_OUT") {
        setUser(null)
        setUserDetails(null)
        setUserPermissions(null)
        router.refresh()
      }
    })

    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [supabase, router])

  // Função para logout
  const signOut = async () => {
    try {
      // Executar logout no Supabase
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error("Erro ao fazer logout:", error)
        throw error
      }

      // Limpar estado local
      setUser(null)
      setUserDetails(null)
      setUserPermissions(null)

      // Redirecionar para login
      router.push("/login")

      // Forçar refresh da página para limpar cache
      router.refresh()

    } catch (error) {
      console.error("Falha no logout:", error)
      // Mesmo com erro, redirecionar para login
      router.push("/login")
    }
  }

  // Evitar problemas de hidratação
  if (!mounted) {
    return (
      <Context.Provider value={{ user: null, userDetails: null, userPermissions: null, loading: true, signOut }}>
        {children}
      </Context.Provider>
    )
  }

  return (
    <Context.Provider value={{ user, userDetails, userPermissions, loading, signOut }}>{children}</Context.Provider>
  )
}

export const useSupabase = () => {
  const context = useContext(Context)
  if (context === undefined) {
    throw new Error("useSupabase must be used inside SupabaseProvider")
  }
  return context
}
