const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function seedData() {
  try {
    console.log('🌱 Iniciando inserção de dados de exemplo...')

    // 1. Buscar ou inserir secretarias
    console.log('📁 Verificando secretarias...')
    let secretarias = []

    // Primeiro, buscar secretarias existentes
    const { data: existingSecretarias } = await supabase
      .from('secretarias')
      .select('*')

    if (existingSecretarias && existingSecretarias.length > 0) {
      secretarias = existingSecretarias
      console.log('✅ Secretarias encontradas:', secretarias.length)
    } else {
      // Se não existir, criar novas
      const { data: newSecretarias, error: secretariasError } = await supabase
        .from('secretarias')
        .insert([
          { nome: 'Secretaria de Finanças', slug: 'financas', valores_total: 0 },
          { nome: 'Secretaria de Educação', slug: 'educacao', valores_total: 0 },
          { nome: 'Secretaria de Saúde', slug: 'saude', valores_total: 0 },
          { nome: 'Secretaria de Obras', slug: 'obras', valores_total: 0 }
        ])
        .select()

      if (secretariasError) {
        console.error('❌ Erro ao inserir secretarias:', secretariasError.message)
      } else {
        secretarias = newSecretarias
        console.log('✅ Secretarias criadas:', secretarias.length)
      }
    }

    // 2. Buscar ou inserir tipos
    console.log('📋 Verificando tipos...')
    let tipos = []

    // Primeiro, buscar tipos existentes
    const { data: existingTipos } = await supabase
      .from('tipos')
      .select('*')

    if (existingTipos && existingTipos.length > 0) {
      tipos = existingTipos
      console.log('✅ Tipos encontrados:', tipos.length)
    } else {
      // Se não existir, criar novos
      const { data: newTipos, error: tiposError } = await supabase
        .from('tipos')
        .insert([
          { nome: 'Pagamento', descricao: 'Bordero de pagamento' },
          { nome: 'Recebimento', descricao: 'Bordero de recebimento' },
          { nome: 'Transferência', descricao: 'Bordero de transferência' }
        ])
        .select()

      if (tiposError) {
        console.error('❌ Erro ao inserir tipos:', tiposError.message)
      } else {
        tipos = newTipos
        console.log('✅ Tipos criados:', tipos.length)
      }
    }

    // 3. Inserir direcionamentos
    console.log('🎯 Inserindo direcionamentos...')
    const { data: direcionamentos, error: direcionamentosError } = await supabase
      .from('direcionamentos')
      .upsert([
        { nome: 'Pequeno Porte', valor_minimo: 0, valor_maximo: 10000 },
        { nome: 'Médio Porte', valor_minimo: 10000, valor_maximo: 100000 },
        { nome: 'Grande Porte', valor_minimo: 100000, valor_maximo: 999999999 }
      ])
      .select()

    if (direcionamentosError) {
      console.error('❌ Erro ao inserir direcionamentos:', direcionamentosError.message)
    } else {
      console.log('✅ Direcionamentos inseridos:', direcionamentos.length)
    }

    // 4. Inserir borderos de exemplo (usando os IDs das secretarias e tipos criados)
    console.log('📄 Inserindo borderos de exemplo...')

    if (secretarias && secretarias.length > 0 && tipos && tipos.length > 0) {
      const { data: borderos, error: borderosError } = await supabase
        .from('borderos')
        .upsert([
          {
            bordero_cod: 'BOR-2024-001',
            valor: 15000.50,
            data: new Date('2024-01-15').toISOString(),
            nome_empresa: 'Empresa ABC Ltda',
            secretaria_id: secretarias[0].id,
            tipo_id: tipos[0].id,
            observacao: 'Pagamento de fornecedores',
            status: 'novo'
          },
          {
            bordero_cod: 'BOR-2024-002',
            valor: 8500.00,
            data: new Date('2024-01-16').toISOString(),
            nome_empresa: 'Construtora XYZ',
            secretaria_id: secretarias[3]?.id || secretarias[0].id,
            tipo_id: tipos[0].id,
            observacao: 'Pagamento de obras públicas',
            status: 'novo'
          },
          {
            bordero_cod: 'BOR-2024-003',
            valor: 25000.75,
            data: new Date('2024-01-17').toISOString(),
            nome_empresa: 'Escola Municipal',
            secretaria_id: secretarias[1]?.id || secretarias[0].id,
            tipo_id: tipos[1]?.id || tipos[0].id,
            observacao: 'Recebimento de verbas federais',
            status: 'novo'
          }
        ])
        .select()

      if (borderosError) {
        console.error('❌ Erro ao inserir borderos:', borderosError.message)
      } else {
        console.log('✅ Borderos inseridos:', borderos.length)
      }
    } else {
      console.log('⚠️ Pulando borderos - secretarias ou tipos não foram criados')
    }

    // 5. Criar nível de acesso operador
    console.log('👤 Criando nível de acesso operador...')
    const { data: nivelOperador, error: nivelOperadorError } = await supabase
      .from('niveis_acesso')
      .upsert({
        nome: 'Operador',
        descricao: 'Acesso para operações básicas',
        permissoes: {
          dashboard: true,
          borderos: true,
          secretarias: false,
          direcionamentos: false,
          tipos: false,
          usuarios: false,
          relatorios: true,
          configuracoes: false
        }
      })
      .select()

    if (nivelOperadorError) {
      console.error('❌ Erro ao criar nível operador:', nivelOperadorError.message)
    } else {
      console.log('✅ Nível operador criado')
    }

    console.log('\n🎉 Dados de exemplo inseridos com sucesso!')
    console.log('📊 Resumo:')
    console.log('   - 4 Secretarias')
    console.log('   - 3 Tipos de bordero')
    console.log('   - 3 Direcionamentos')
    console.log('   - 3 Borderos de exemplo')
    console.log('   - 2 Níveis de acesso')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

seedData()
