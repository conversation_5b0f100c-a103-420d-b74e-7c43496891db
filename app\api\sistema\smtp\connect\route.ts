import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import nodemailer from "nodemailer"

export async function POST(request: Request) {
  try {
    // Verificar autenticação
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session }, error: authError } = await supabase.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const body = await request.json()
    const { host, port, secure, email_remetente, senha } = body

    if (!host || !port || !email_remetente || !senha) {
      return NextResponse.json({ 
        error: "Todos os campos são obrigatórios" 
      }, { status: 400 })
    }

    // Configurar transporter
    const transporter = nodemailer.createTransport({
      host,
      port: parseInt(port),
      secure: secure === true || secure === 'true',
      auth: {
        user: email_remetente,
        pass: senha,
      },
    })

    // Verificar apenas a conexão (sem enviar email)
    await transporter.verify()

    // Registrar teste de conexão no log
    try {
      const supabaseAdmin = createClient(
        process.env.SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      )

      await supabaseAdmin
        .from("sistema_logs")
        .insert({
          usuario_id: session.user.id,
          acao: "connect",
          entidade: "smtp",
          detalhes: {
            host,
            port: parseInt(port),
            secure: secure === true || secure === 'true',
            email_remetente,
            status: "success"
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    return NextResponse.json({
      message: "Conexão SMTP estabelecida com sucesso!",
      status: "connected"
    })
  } catch (error: any) {
    console.error("Erro ao conectar SMTP:", error)
    
    // Registrar erro no log
    try {
      const cookieStore = await cookies()
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        const supabaseAdmin = createClient(
          process.env.SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!,
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          }
        )

        await supabaseAdmin
          .from("sistema_logs")
          .insert({
            usuario_id: session.user.id,
            acao: "error",
            entidade: "smtp_connect",
            detalhes: {
              error: error.message,
              code: error.code
            }
          })
      }
    } catch (logError) {
      console.error("Erro ao registrar log de erro:", logError)
    }

    // Determinar tipo de erro para feedback específico
    let errorMessage = "Erro ao conectar com o servidor SMTP"
    
    if (error.code === 'EAUTH') {
      errorMessage = "Autenticação falhou - verifique email e senha"
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorMessage = "Servidor SMTP inacessível - verifique host e porta"
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = "Timeout na conexão - servidor pode estar indisponível"
    }

    return NextResponse.json({
      error: errorMessage,
      details: error.message,
      code: error.code
    }, { status: 500 })
  }
}
