"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Filter } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"

export function BorderoFilter() {
  const [secretarias, setSecretarias] = useState({
    educacao: false,
    saude: false,
    infraestrutura: false,
    financas: false,
    governo: false,
  })

  const [tipos, setTipos] = useState({
    obra: false,
    convenio: false,
    servico: false,
    material: false,
  })

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          <Filter className="h-3.5 w-3.5" />
          <span>Filtrar</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuLabel>Secretarias</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuCheckboxItem
          checked={secretarias.educacao}
          onCheckedChange={(value) => setSecretarias({ ...secretarias, educacao: !!value })}
        >
          Educação
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={secretarias.saude}
          onCheckedChange={(value) => setSecretarias({ ...secretarias, saude: !!value })}
        >
          Saúde
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={secretarias.infraestrutura}
          onCheckedChange={(value) => setSecretarias({ ...secretarias, infraestrutura: !!value })}
        >
          Infraestrutura
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={secretarias.financas}
          onCheckedChange={(value) => setSecretarias({ ...secretarias, financas: !!value })}
        >
          Finanças
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={secretarias.governo}
          onCheckedChange={(value) => setSecretarias({ ...secretarias, governo: !!value })}
        >
          Governo
        </DropdownMenuCheckboxItem>

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Tipos de Bordero</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuCheckboxItem
          checked={tipos.obra}
          onCheckedChange={(value) => setTipos({ ...tipos, obra: !!value })}
        >
          Obra
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={tipos.convenio}
          onCheckedChange={(value) => setTipos({ ...tipos, convenio: !!value })}
        >
          Convênio
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={tipos.servico}
          onCheckedChange={(value) => setTipos({ ...tipos, servico: !!value })}
        >
          Serviço
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={tipos.material}
          onCheckedChange={(value) => setTipos({ ...tipos, material: !!value })}
        >
          Material
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
