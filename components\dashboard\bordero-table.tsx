"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, Edit, CheckCircle, AlertCircle, Archive } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { useRouter } from "next/navigation"

interface Bordero {
  id: number
  bordero_cod: string
  nome_empresa: string
  valor: number
  data: string
  secretaria: {
    nome: string
  }
  tipo: {
    nome: string
  }
  status: string
}

interface BorderoTableProps {
  status: "novo" | "analise" | "assinado" | "pago" | "todos" | "excluido" | "arquivado"
  secretariaId?: number
  tipoId?: number
  filtros?: any
}

export function BorderoTable({ status, secretariaId, tipoId, filtros }: BorderoTableProps) {
  const router = useRouter()
  const notify = useNotify()
  const [borderos, setBorderos] = useState<Bordero[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogType, setDialogType] = useState<"visualizar" | "correcao">("visualizar")
  const [selectedBordero, setSelectedBordero] = useState<Bordero | null>(null)
  const [correcaoMotivo, setCorrecaoMotivo] = useState("")

  useEffect(() => {
    fetchBorderos()
  }, [status, secretariaId, tipoId, filtros])

  const fetchBorderos = async () => {
    try {
      setLoading(true)
      console.log(`Buscando borderos com status: ${status}`);

      // Tentar primeiro a API de borderos direta
      let url = `/api/borderos?status=${status}`;
      
      if (secretariaId) {
        url += `&secretariaId=${secretariaId}`;
      }

      if (tipoId) {
        url += `&tipoId=${tipoId}`;
      }

      // Adicionar filtros avançados
      if (filtros) {
        if (filtros.codigo) url += `&codigo=${encodeURIComponent(filtros.codigo)}`;
        if (filtros.empresa) url += `&empresa=${encodeURIComponent(filtros.empresa)}`;
        if (filtros.secretariaId && !secretariaId) url += `&secretariaId=${filtros.secretariaId}`;
        if (filtros.tipoId && !tipoId) url += `&tipoId=${filtros.tipoId}`;
        if (filtros.status && filtros.status !== status) url += `&status=${filtros.status}`;
        if (filtros.valorMin) url += `&valorMin=${filtros.valorMin}`;
        if (filtros.valorMax) url += `&valorMax=${filtros.valorMax}`;
        if (filtros.dataInicio) url += `&dataInicio=${filtros.dataInicio}`;
        if (filtros.dataFim) url += `&dataFim=${filtros.dataFim}`;
      }

      console.log("URL de busca (API borderos):", url);
      let response = await fetch(url);
      let data;
      let usedFallback = false;

      // Se a API de borderos falhar, tentar a API de relatórios como fallback
      if (!response.ok) {
        console.warn("API de borderos falhou, tentando API de relatórios como fallback");
        
        // Construir URL para API de relatórios
        let fallbackUrl = `/api/relatorios`;
        
        // Se não for "todos", adicione filtro de status
        if (status !== "todos") {
          fallbackUrl += `?status=${status}`;
        }

        // Adicionar outros filtros
        if (secretariaId) {
          fallbackUrl += fallbackUrl.includes('?') ? `&secretarias=${secretariaId}` : `?secretarias=${secretariaId}`;
        }

        if (tipoId) {
          fallbackUrl += fallbackUrl.includes('?') ? `&tipos=${tipoId}` : `?tipos=${tipoId}`;
        }

        // Adicionar filtros avançados para API de relatórios
        if (filtros) {
          if (filtros.codigo || filtros.empresa) {
            const searchTerm = filtros.codigo || filtros.empresa;
            fallbackUrl += fallbackUrl.includes('?') ? `&search=${encodeURIComponent(searchTerm)}` : `?search=${encodeURIComponent(searchTerm)}`;
          }
          if (filtros.secretariaId && !secretariaId) fallbackUrl += fallbackUrl.includes('?') ? `&secretarias=${filtros.secretariaId}` : `?secretarias=${filtros.secretariaId}`;
          if (filtros.tipoId && !tipoId) fallbackUrl += fallbackUrl.includes('?') ? `&tipos=${filtros.tipoId}` : `?tipos=${filtros.tipoId}`;
          if (filtros.valorMin) fallbackUrl += fallbackUrl.includes('?') ? `&valorMin=${filtros.valorMin}` : `?valorMin=${filtros.valorMin}`;
          if (filtros.valorMax) fallbackUrl += fallbackUrl.includes('?') ? `&valorMax=${filtros.valorMax}` : `?valorMax=${filtros.valorMax}`;
          if (filtros.dataInicio) fallbackUrl += fallbackUrl.includes('?') ? `&dateFrom=${filtros.dataInicio}` : `?dateFrom=${filtros.dataInicio}`;
          if (filtros.dataFim) fallbackUrl += fallbackUrl.includes('?') ? `&dateTo=${filtros.dataFim}` : `?dateTo=${filtros.dataFim}`;
        }

        console.log("URL de fallback (API relatórios):", fallbackUrl);
        response = await fetch(fallbackUrl);
        
        if (!response.ok) {
          console.error("Ambas as APIs falharam:", response.status, response.statusText);
          throw new Error("Erro ao buscar borderos");
        }
        
        usedFallback = true;
      }

      data = await response.json();
      console.log("Dados recebidos:", data);
      
      // Se estamos usando a API de relatórios, os borderos estão em data.borderos
      let borderosList;
      
      if (usedFallback) {
        // Filtrar os borderos pelo status correto se estamos usando a API de relatórios
        borderosList = data.borderos || [];
        if (status !== "todos") {
          borderosList = borderosList.filter((b: any) => b.status === status);
        }
      } else {
        borderosList = Array.isArray(data) ? data : [];
      }
      
      console.log(`Encontrados ${borderosList.length} borderos com status ${status}`);
      
      // Se ainda não temos dados, tentar uma última abordagem - buscar todos e filtrar manualmente
      if (borderosList.length === 0 && status !== "todos") {
        console.log("Tentando buscar todos os borderos e filtrar manualmente");
        const allResponse = await fetch("/api/relatorios");
        if (allResponse.ok) {
          const allData = await allResponse.json();
          if (allData.borderos && Array.isArray(allData.borderos)) {
            borderosList = allData.borderos.filter((b: any) => b.status === status);
            console.log(`Encontrados ${borderosList.length} borderos após filtro manual`);
          }
        }
      }
      
      setBorderos(borderosList);
    } catch (error) {
      console.error("Erro ao buscar borderos:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os borderos.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateBorderoStatus = async (id: number, newStatus: string, dadosStatus?: string) => {
    try {
      const response = await fetch(`/api/borderos/${id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          dadosStatus,
          usuarioId: 1, // Simulando usuário logado
        }),
      })

      if (!response.ok) {
        throw new Error("Erro ao atualizar status")
      }

      // Mapear status para mensagens mais amigáveis
      const statusMessages: { [key: string]: string } = {
        analise: "Em Análise",
        assinado: "Assinado",
        pago: "Pago",
        arquivado: "Arquivado",
        corrigir: "Correção Solicitada"
      }

      const statusMessage = statusMessages[newStatus] || newStatus

      notify.success(
        "Status Atualizado",
        `Bordero ${borderos.find(b => b.id === id)?.bordero_cod} foi marcado como ${statusMessage}.`
      )

      // Atualizar a lista de borderos
      fetchBorderos()

      // Fechar o diálogo se estiver aberto
      setDialogOpen(false)
      setCorrecaoMotivo("")
    } catch (error) {
      console.error("Erro ao atualizar status:", error)
      toast({
        title: "Erro",
        description: "Não foi possível atualizar o status do bordero.",
        variant: "destructive",
      })
    }
  }

  const handleSolicitarCorrecao = () => {
    if (!selectedBordero) return

    if (!correcaoMotivo.trim()) {
      toast({
        title: "Erro",
        description: "Informe o motivo da correção.",
        variant: "destructive",
      })
      return
    }

    updateBorderoStatus(selectedBordero.id, "corrigir", correcaoMotivo)
  }

  // Função para renderizar o badge de status
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "novo":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800">
            Novo
          </Badge>
        )
      case "analise":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">
            Em Análise
          </Badge>
        )
      case "assinado":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
            Assinado
          </Badge>
        )
      case "pago":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800">
            Pago
          </Badge>
        )
      case "corrigir":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">
            Corrigir
          </Badge>
        )
      case "cancelado":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Cancelado
          </Badge>
        )
      case "excluido":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Excluído
          </Badge>
        )
      case "arquivado":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">
            Arquivado
          </Badge>
        )
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-sky-600 rounded-full border-t-transparent"></div>
      </div>
    )
  }

  return (
    <>
      {/* Layout Desktop */}
      <div className="hidden md:block overflow-x-auto">
        <Table className="w-full table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[160px]">Código</TableHead>
              <TableHead className="w-[300px]">Empresa</TableHead>
              <TableHead className="w-[140px]">Valor</TableHead>
              <TableHead className="w-[110px]">Data</TableHead>
              <TableHead className="w-[200px]">Secretaria</TableHead>
              <TableHead className="w-[100px]">Tipo</TableHead>
              {status === "todos" && <TableHead className="w-[130px]">Status</TableHead>}
              <TableHead className="text-right w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {borderos.length === 0 ? (
              <TableRow>
                <TableCell colSpan={status === "todos" ? 8 : 7} className="text-center">
                  Nenhum bordero encontrado.
                </TableCell>
              </TableRow>
            ) : (
              borderos.map((bordero) => (
                <TableRow key={bordero.id} className="hover:bg-slate-100 dark:hover:bg-slate-800/30 transition-colors">
                  <TableCell className="font-medium font-mono text-sm truncate">{bordero.bordero_cod}</TableCell>
                  <TableCell className="truncate" title={bordero.nome_empresa}>
                    <div className="truncate font-medium">{bordero.nome_empresa}</div>
                  </TableCell>
                  <TableCell className="text-right font-semibold text-green-600 dark:text-green-400">
                    {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(bordero.valor)}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">{new Date(bordero.data).toLocaleDateString("pt-BR")}</TableCell>
                  <TableCell className="text-sm truncate" title={bordero.secretaria?.nome}>
                    <div className="truncate">{bordero.secretaria?.nome}</div>
                  </TableCell>
                  <TableCell className="text-sm truncate" title={bordero.tipo?.nome}>
                    <div className="truncate">{bordero.tipo?.nome}</div>
                  </TableCell>
                  {status === "todos" && <TableCell>{renderStatusBadge(bordero.status)}</TableCell>}
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Ações</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => {
                          router.push(`/dashboard/borderos/${bordero.id}`)
                        }}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>Visualizar</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          router.push(`/dashboard/borderos/${bordero.id}/editar`)
                        }}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Editar</span>
                        </DropdownMenuItem>
                        {bordero.status === "novo" && (
                          <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            <span>Marcar em Análise</span>
                          </DropdownMenuItem>
                        )}
                        {bordero.status === "analise" && (
                          <>
                            <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              <span>Aprovar</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedBordero(bordero)
                                setDialogType("correcao")
                                setDialogOpen(true)
                              }}
                            >
                              <AlertCircle className="mr-2 h-4 w-4" />
                              <span>Solicitar Correção</span>
                            </DropdownMenuItem>
                          </>
                        )}
                        {bordero.status === "assinado" && (
                          <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            <span>Marcar como Pago</span>
                          </DropdownMenuItem>
                        )}
                        {bordero.status === "pago" && (
                          <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>
                            <Archive className="mr-2 h-4 w-4" />
                            <span>Arquivar</span>
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Layout Mobile */}
      <div className="md:hidden space-y-4">
        {borderos.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nenhum bordero encontrado.
          </div>
        ) : (
          borderos.map((bordero) => (
            <Card key={bordero.id} className="p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div className="font-mono text-sm font-medium text-gray-600">
                    {bordero.bordero_cod}
                  </div>
                  <div className="font-semibold text-lg text-green-600">
                    {new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL"
                    }).format(bordero.valor)}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {status === "todos" && renderStatusBadge(bordero.status)}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => {
                        router.push(`/dashboard/borderos/${bordero.id}`)
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Visualizar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        router.push(`/dashboard/borderos/${bordero.id}/editar`)
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      {bordero.status === "novo" && (
                        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          <span>Marcar em Análise</span>
                        </DropdownMenuItem>
                      )}
                      {bordero.status === "analise" && (
                        <>
                          <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            <span>Aprovar</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedBordero(bordero)
                              setDialogType("correcao")
                              setDialogOpen(true)
                            }}
                          >
                            <AlertCircle className="mr-2 h-4 w-4" />
                            <span>Solicitar Correção</span>
                          </DropdownMenuItem>
                        </>
                      )}
                      {bordero.status === "assinado" && (
                        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          <span>Marcar como Pago</span>
                        </DropdownMenuItem>
                      )}
                      {bordero.status === "pago" && (
                        <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>
                          <Archive className="mr-2 h-4 w-4" />
                          <span>Arquivar</span>
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              <div className="space-y-2">
                <div>
                  <div className="text-sm font-medium text-gray-600">Empresa</div>
                  <div className="text-sm">{bordero.nome_empresa}</div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium text-gray-600">Data</div>
                    <div>{new Date(bordero.data).toLocaleDateString("pt-BR")}</div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-600">Secretaria</div>
                    <div className="truncate">{bordero.secretaria?.nome}</div>
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-gray-600">Tipo</div>
                  <div className="text-sm">{bordero.tipo?.nome}</div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {dialogType === "visualizar" ? "Detalhes do Bordero" : "Solicitar Correção"}
            </DialogTitle>
            <DialogDescription>
              {dialogType === "visualizar"
                ? `Informações detalhadas do bordero ${selectedBordero?.bordero_cod}`
                : `Informe o motivo da correção para o bordero ${selectedBordero?.bordero_cod}.`
              }
            </DialogDescription>
          </DialogHeader>

          {dialogType === "visualizar" ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Código</label>
                  <p className="text-sm">{selectedBordero?.bordero_cod}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <p className="text-sm">{renderStatusBadge(selectedBordero?.status || "")}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Empresa</label>
                  <p className="text-sm">{selectedBordero?.nome_empresa}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Valor</label>
                  <p className="text-sm">
                    {selectedBordero?.valor && new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL"
                    }).format(selectedBordero.valor)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Data</label>
                  <p className="text-sm">
                    {selectedBordero?.data && new Date(selectedBordero.data).toLocaleDateString("pt-BR")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Secretaria</label>
                  <p className="text-sm">{selectedBordero?.secretaria?.nome}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tipo</label>
                  <p className="text-sm">{selectedBordero?.tipo?.nome}</p>
                </div>
              </div>
            </div>
          ) : (
            <Textarea
              placeholder="Descreva o motivo da correção..."
              value={correcaoMotivo}
              onChange={(e) => setCorrecaoMotivo(e.target.value)}
              className="min-h-[100px]"
            />
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              {dialogType === "visualizar" ? "Fechar" : "Cancelar"}
            </Button>
            {dialogType === "correcao" && (
              <Button onClick={handleSolicitarCorrecao}>Enviar Solicitação</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
