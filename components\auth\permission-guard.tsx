"use client"

import { usePermissions, type Permission } from "@/hooks/use-permissions"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { PermissionLoading } from "@/components/ui/fast-loading"

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  fallback?: React.ReactNode
  redirectTo?: string
}

export function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  redirectTo = "/dashboard"
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions()
  const router = useRouter()
  const [timeoutReached, setTimeoutReached] = useState(false)

  // Determinar quais permissões verificar
  const permissionsToCheck = permission ? [permission] : permissions

  // Verificar se o usuário tem as permissões necessárias
  const hasRequiredPermissions = () => {
    if (permissionsToCheck.length === 0) return true

    if (permission) {
      return hasPermission(permission)
    }

    if (requireAll) {
      return hasAllPermissions(permissionsToCheck)
    }

    return hasAnyPermission(permissionsToCheck)
  }

  // Timeout para evitar loading infinito
  useEffect(() => {
    const timer = setTimeout(() => {
      if (loading) {
        console.warn("Timeout na verificação de permissões, permitindo acesso")
        setTimeoutReached(true)
      }
    }, 8000) // 8 segundos timeout

    return () => clearTimeout(timer)
  }, [loading])

  useEffect(() => {
    if (!loading && !hasRequiredPermissions()) {
      if (redirectTo) {
        router.push(`${redirectTo}?error=access_denied`)
      }
    }
  }, [loading, redirectTo, router])

  // Se timeout foi atingido, permitir acesso
  if (timeoutReached) {
    console.warn("Acesso permitido por timeout")
    return <>{children}</>
  }

  // Mostrar loading enquanto verifica permissões
  if (loading) {
    return (
      <PermissionLoading
        onTimeout={() => setTimeoutReached(true)}
      />
    )
  }

  // Se não tem permissão, mostrar fallback ou mensagem de erro
  if (!hasRequiredPermissions()) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Acesso Negado</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Você não tem permissão para acessar esta página.
              </p>
              <Button onClick={() => router.push("/dashboard")} variant="outline">
                Voltar ao Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Se tem permissão, renderizar o conteúdo
  return <>{children}</>
}

// Componente para verificar permissões inline
interface PermissionCheckProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  fallback?: React.ReactNode
}

export function PermissionCheck({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null
}: PermissionCheckProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions()

  if (loading) return null

  const permissionsToCheck = permission ? [permission] : permissions

  const hasRequiredPermissions = () => {
    if (permissionsToCheck.length === 0) return true

    if (permission) {
      return hasPermission(permission)
    }

    if (requireAll) {
      return hasAllPermissions(permissionsToCheck)
    }

    return hasAnyPermission(permissionsToCheck)
  }

  if (!hasRequiredPermissions()) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
