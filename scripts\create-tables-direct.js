const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function createTables() {
  try {
    console.log('🔧 Criando tabelas necessárias...\n')

    // 1. Criar tabela configuracoes_sistema
    console.log('1. Criando tabela configuracoes_sistema...')

    const configSql = `
      -- <PERSON><PERSON>r tabela configuracoes_sistema
      CREATE TABLE IF NOT EXISTS configuracoes_sistema (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
        modo_escuro BOOLEAN DEFAULT false,
        notificacoes_email BOOLEAN DEFAULT true,
        logo_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Habilitar RLS
      ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;

      -- Criar política para permitir acesso total
      DROP POLICY IF EXISTS "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema;
      CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
      FOR ALL USING (true);
    `

    try {
      // Tentar executar via rpc
      const { error: configError } = await supabase.rpc('exec_sql', { sql: configSql })
      if (configError) throw configError
      console.log('✅ Tabela configuracoes_sistema criada via RPC')
    } catch (rpcError) {
      console.log('⚠️ RPC não disponível, usando método alternativo...')

      // Método alternativo: inserir dados para forçar criação
      const { error: insertError } = await supabase
        .from('configuracoes_sistema')
        .insert({
          nome: 'CRM de Bordero',
          modo_escuro: false,
          notificacoes_email: true
        })

      if (insertError) {
        console.log('❌ Tabela configuracoes_sistema precisa ser criada manualmente')
        console.log('Erro:', insertError.message || 'Erro desconhecido')
        console.log('SQL para executar no Supabase:')
        console.log(configSql)
      } else {
        console.log('✅ Tabela configuracoes_sistema configurada')
      }
    }

    // 2. Verificar e atualizar tabela niveis_acesso
    console.log('\n2. Verificando tabela niveis_acesso...')

    const niveisAcessoSql = `
      -- Atualizar tabela niveis_acesso para incluir permissões
      ALTER TABLE niveis_acesso
      ADD COLUMN IF NOT EXISTS permissoes JSONB DEFAULT '{}';

      -- Atualizar níveis existentes com permissões padrão
      UPDATE niveis_acesso
      SET permissoes = CASE
        WHEN nome = 'Administrador' THEN '{
          "dashboard": true,
          "borderos": true,
          "secretarias": true,
          "direcionamentos": true,
          "tipos": true,
          "usuarios": true,
          "relatorios": true,
          "configuracoes": true
        }'::jsonb
        WHEN nome = 'Usuário' THEN '{
          "dashboard": true,
          "borderos": true,
          "secretarias": false,
          "direcionamentos": false,
          "tipos": false,
          "usuarios": false,
          "relatorios": false,
          "configuracoes": false
        }'::jsonb
        ELSE '{
          "dashboard": true,
          "borderos": false,
          "secretarias": false,
          "direcionamentos": false,
          "tipos": false,
          "usuarios": false,
          "relatorios": false,
          "configuracoes": false
        }'::jsonb
      END
      WHERE permissoes IS NULL OR permissoes = '{}';
    `

    try {
      const { error: niveisError } = await supabase.rpc('exec_sql', { sql: niveisAcessoSql })
      if (niveisError) throw niveisError
      console.log('✅ Tabela niveis_acesso atualizada via RPC')
    } catch (rpcError) {
      console.log('⚠️ Não foi possível atualizar via RPC')
      console.log('SQL para executar manualmente:', niveisAcessoSql)
    }

    // 3. Inserir configuração padrão se não existir
    console.log('\n3. Inserindo configuração padrão...')

    const { data: existingConfig } = await supabase
      .from('configuracoes_sistema')
      .select('id')
      .limit(1)

    if (!existingConfig || existingConfig.length === 0) {
      const { error: insertConfigError } = await supabase
        .from('configuracoes_sistema')
        .insert({
          nome: 'CRM de Bordero',
          modo_escuro: false,
          notificacoes_email: true
        })

      if (insertConfigError) {
        console.log('❌ Erro ao inserir configuração padrão:', insertConfigError.message)
      } else {
        console.log('✅ Configuração padrão inserida')
      }
    } else {
      console.log('✅ Configuração já existe')
    }

    // 4. Verificar níveis de acesso
    console.log('\n4. Verificando níveis de acesso...')

    const { data: niveisData, error: niveisSelectError } = await supabase
      .from('niveis_acesso')
      .select('*')

    if (niveisSelectError) {
      console.log('❌ Erro ao buscar níveis de acesso:', niveisSelectError.message)
    } else {
      console.log(`✅ Encontrados ${niveisData.length} níveis de acesso:`)
      niveisData.forEach(nivel => {
        console.log(`   - ${nivel.nome}: ${JSON.stringify(nivel.permissoes || {})}`)
      })
    }

    console.log('\n🎉 Configuração concluída!')
    console.log('\n📋 Se alguma tabela não foi criada automaticamente, execute o SQL manualmente no Supabase Dashboard.')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

createTables()
