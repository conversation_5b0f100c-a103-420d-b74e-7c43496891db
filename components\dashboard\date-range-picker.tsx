"use client"

import * as React from "react"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import type { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface DateRangePickerProps {
  date: DateRange | undefined
  onDateChange: (date: DateRange | undefined) => void
}

export function DateRangePicker({ date, onDateChange }: DateRangePickerProps) {
  const [selectedPreset, setSelectedPreset] = React.useState<string | undefined>(undefined)

  const handlePresetChange = (preset: string) => {
    setSelectedPreset(preset)

    const today = new Date()
    let from: Date | undefined
    let to: Date | undefined

    switch (preset) {
      case "hoje":
        from = today
        to = today
        break
      case "semana":
        from = new Date(today)
        from.setDate(today.getDate() - 7)
        to = today
        break
      case "mes":
        from = new Date(today)
        from.setMonth(today.getMonth() - 1)
        to = today
        break
      case "trimestre":
        from = new Date(today)
        from.setMonth(today.getMonth() - 3)
        to = today
        break
      case "ano":
        from = new Date(today)
        from.setFullYear(today.getFullYear() - 1)
        to = today
        break
      case "todos":
        from = undefined
        to = undefined
        break
      default:
        from = undefined
        to = undefined
    }

    onDateChange({ from, to })
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="grid gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={"outline"}
              className={cn("w-[300px] justify-start text-left font-normal", !date && "text-muted-foreground")}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "PPP", { locale: ptBR })} - {format(date.to, "PPP", { locale: ptBR })}
                  </>
                ) : (
                  format(date.from, "PPP", { locale: ptBR })
                )
              ) : (
                <span>Selecione um período</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="flex flex-col gap-2 p-2">
              <Select value={selectedPreset} onValueChange={handlePresetChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um período predefinido" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hoje">Hoje</SelectItem>
                  <SelectItem value="semana">Últimos 7 dias</SelectItem>
                  <SelectItem value="mes">Último mês</SelectItem>
                  <SelectItem value="trimestre">Último trimestre</SelectItem>
                  <SelectItem value="ano">Último ano</SelectItem>
                  <SelectItem value="todos">Todo o período</SelectItem>
                </SelectContent>
              </Select>
              <div className="border-t pt-2">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={date?.from}
                  selected={date}
                  onSelect={onDateChange}
                  numberOfMonths={2}
                  locale={ptBR}
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
