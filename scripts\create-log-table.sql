-- C<PERSON><PERSON> tabel<PERSON> de logs de atividade
CREATE TABLE IF NOT EXISTS log_atividades (
    id SERIAL PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    acao VARCHAR(255) NOT NULL,
    entidade VARCHAR(255) NOT NULL,
    entidade_id VARCHAR(255),
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

-- Habilitar RLS (Row Level Security)
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir leitura para usuários autenticados
CREATE POLICY IF NOT EXISTS "Usuários podem ver logs de atividades" ON log_atividades
  FOR SELECT USING (auth.role() = 'authenticated');

-- Criar política para permitir inserção para usuários autenticados
CREATE POLICY IF NOT EXISTS "Usuários podem criar logs de atividades" ON log_atividades
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');
