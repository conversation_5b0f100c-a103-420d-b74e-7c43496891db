import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { pusherServer } from "@/lib/pusher"
import { logBorderoComplete } from "@/lib/bordero-logger"

export async function GET(request: Request) {
  try {
    console.log("API borderos - Recebida requisição GET");
    const { searchParams } = new URL(request.url)
    
    // Log de todos os parâmetros recebidos
    console.log("Parâmetros recebidos:", Object.fromEntries(searchParams.entries()));
    
    const status = searchParams.get("status")
    const secretariaId = searchParams.get("secretariaId")
    const tipoId = searchParams.get("tipoId")
    const codigo = searchParams.get("codigo")
    const empresa = searchParams.get("empresa")
    const valorMin = searchParams.get("valorMin")
    const valorMax = searchParams.get("valorMax")
    const dataInicio = searchParams.get("dataInicio")
    const dataFim = searchParams.get("dataFim")
    const ignoreUserLimits = searchParams.get("ignoreUserLimits") === "true"

    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session: userSession }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError) {
      console.error("Erro ao obter sessão:", authError)
      return NextResponse.json({ error: "Erro de autenticação" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o usuário tem direcionamentos (limites de valor)
    let userValueLimits = null;
    if (userSession && !ignoreUserLimits) {
      console.log("🔍 Verificando direcionamentos do usuário:", userSession.user.id);

      const { data: userDirecionamentos, error: direcionamentosError } = await supabase
        .from("direcionamento_usuarios")
        .select(`
          direcionamento:direcionamentos(
            id,
            nome,
            valor_minimo,
            valor_maximo
          )
        `)
        .eq("usuario_id", userSession.user.id);

      if (direcionamentosError) {
        console.error("❌ Erro ao buscar direcionamentos do usuário:", direcionamentosError);
      } else {
        console.log("📋 Direcionamentos encontrados:", userDirecionamentos);

        if (userDirecionamentos && userDirecionamentos.length > 0) {
          // Encontrar o valor máximo que o usuário pode ver
          const valoresMaximos = userDirecionamentos
            .filter(d => d.direcionamento && d.direcionamento.valor_maximo)
            .map(d => (d.direcionamento as any).valor_maximo);

          console.log("📋 Valores máximos encontrados:", valoresMaximos);

          if (valoresMaximos.length > 0) {
            const maxValue = Math.max(...valoresMaximos);

            if (maxValue > 0) {
              userValueLimits = maxValue;
              console.log(`💰 Usuário ${userSession.user.id} tem limite de valor: R$ ${userValueLimits}`);
            } else {
              console.log("⚠️ Valor máximo é 0 ou inválido");
            }
          } else {
            console.log("⚠️ Nenhum valor máximo válido encontrado nos direcionamentos");
          }
        } else {
          console.log("📝 Usuário não tem direcionamentos configurados - pode ver todos os valores");
        }
      }
    } else {
      console.log("🚫 Verificação de direcionamentos ignorada ou usuário não autenticado");
    }

    let query = supabase.from("borderos").select(`
        *,
        secretaria:secretarias(*),
        tipo:tipos(*),
        responsavel:usuarios(*),
        devolucoes:bordero_devolucoes(
          usuario:usuarios(*)
        )
      `)

    // Adicionar logging para debug
    console.log("API borderos - status solicitado:", status);
    
    if (status && status !== "todos") {
      query = query.eq("status", status);
      console.log(`Filtrando por status específico: ${status}`);
    } else if (status === "todos") {
      // Quando status é "todos", excluir apenas borderos arquivados e excluídos
      query = query.not("status", "in", "(arquivado,excluido)");
      console.log("Filtrando todos os status exceto arquivado e excluido");
    } else {
      // Se nenhum status for especificado, não aplicar filtro de status
      console.log("Nenhum filtro de status aplicado");
    }

    if (secretariaId) {
      query = query.eq("secretaria_id", secretariaId)
    }

    if (tipoId) {
      query = query.eq("tipo_id", tipoId)
    }

    // Filtros avançados
    if (codigo) {
      query = query.ilike("bordero_cod", `%${codigo}%`)
    }

    if (empresa) {
      query = query.ilike("nome_empresa", `%${empresa}%`)
    }

    if (valorMin) {
      query = query.gte("valor", parseFloat(valorMin))
    }

    if (valorMax) {
      query = query.lte("valor", parseFloat(valorMax))
    }

    if (dataInicio) {
      query = query.gte("data", dataInicio)
    }

    if (dataFim) {
      query = query.lte("data", dataFim)
    }

    // Aplicar limite de valor baseado nos direcionamentos do usuário
    if (userValueLimits) {
      console.log(`🔒 Aplicando filtro de valor máximo: R$ ${userValueLimits}`);
      console.log(`🔒 Filtro SQL: valor <= ${userValueLimits}`);
      query = query.lte("valor", userValueLimits);
    } else {
      console.log("🔓 Nenhum limite de valor aplicado - usuário pode ver todos os borderos");
    }

    // Ordenar por data de criação (mais recentes primeiro)
    query = query.order("created_at", { ascending: false })

    console.log("Executando query no Supabase");
    const { data: borderos, error } = await query

    if (error) {
      console.error("Erro ao buscar borderos:", error)
      return NextResponse.json({ error: error.message, details: error }, { status: 500 })
    }

    // Verificar se os dados retornados são válidos
    if (!borderos) {
      console.warn("Nenhum dado retornado da query");
      return NextResponse.json([], { status: 200 })
    }

    console.log(`Encontrados ${borderos.length} borderos`);

    // Log detalhado para debug - sempre mostrar valores para verificar filtro
    if (borderos.length > 0) {
      console.log("📊 Valores dos borderos retornados:");
      borderos.forEach((b: any, index: number) => {
        if (index < 20) { // Mostrar apenas os primeiros 20 para não poluir o log
          console.log(`  - ${b.bordero_cod}: R$ ${b.valor} (Status: ${b.status})`);
        }
      });

      const valores = borderos.map((b: any) => b.valor);
      const valorMaximo = Math.max(...valores);
      const valorMinimo = Math.min(...valores);
      console.log(`💰 Valor máximo encontrado: R$ ${valorMaximo}`);
      console.log(`💰 Valor mínimo encontrado: R$ ${valorMinimo}`);

      if (userValueLimits) {
        const acimaDolimite = borderos.filter((b: any) => b.valor > userValueLimits);
        if (acimaDolimite.length > 0) {
          console.log(`⚠️ PROBLEMA: ${acimaDolimite.length} borderos acima do limite de R$ ${userValueLimits}:`);
          acimaDolimite.forEach((b: any) => {
            console.log(`  - ${b.bordero_cod}: R$ ${b.valor}`);
          });
        } else {
          console.log(`✅ Todos os borderos estão dentro do limite de R$ ${userValueLimits}`);
        }
      }
    }

    // Obter o usuário atual para logging
    const {
      data: { session: logSession },
    } = await supabase.auth.getSession()

    // Log de visualização removido temporariamente

    return NextResponse.json(Array.isArray(borderos) ? borderos : [], { status: 200 })
  } catch (error) {
    console.error("Erro ao buscar borderos:", error)
    return NextResponse.json([], { status: 200 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { borderoCod, valor, data, nomeEmpresa, secretariaId, tipoId, observacao, devolucoes, responsavelId } = body

    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    // Obter o usuário atual
    const {
      data: { session: userSession },
    } = await supabaseAuth.auth.getSession()

    if (!userSession) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar Service Role para operações no banco
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Validações detalhadas
    const validationErrors = []

    if (!borderoCod) validationErrors.push("Código do bordero é obrigatório")
    if (!valor) validationErrors.push("Valor é obrigatório")
    if (!data) validationErrors.push("Data é obrigatória")
    if (!nomeEmpresa) validationErrors.push("Nome da empresa é obrigatório")
    if (!secretariaId) validationErrors.push("Secretaria é obrigatória")
    if (!tipoId) validationErrors.push("Tipo é obrigatório")

    if (validationErrors.length > 0) {
      console.error("Erros de validação:", validationErrors)
      return NextResponse.json({
        error: "Campos obrigatórios não preenchidos",
        details: validationErrors,
        fields: {
          borderoCod: !borderoCod,
          valor: !valor,
          data: !data,
          nomeEmpresa: !nomeEmpresa,
          secretariaId: !secretariaId,
          tipoId: !tipoId
        }
      }, { status: 400 })
    }

    if (valor <= 0) {
      console.error("Valor inválido:", valor)
      return NextResponse.json({
        error: "Valor deve ser maior que zero",
        details: [`Valor informado: ${valor}`]
      }, { status: 400 })
    }

    // Verificar se secretaria existe
    const { data: secretariaExists, error: secretariaCheckError } = await supabase
      .from("secretarias")
      .select("id")
      .eq("id", secretariaId)
      .maybeSingle()

    if (secretariaCheckError) {
      console.error("Erro ao verificar secretaria:", secretariaCheckError)
      return NextResponse.json({
        error: "Erro ao verificar secretaria",
        details: [secretariaCheckError.message]
      }, { status: 500 })
    }

    if (!secretariaExists) {
      return NextResponse.json({
        error: "Secretaria não encontrada",
        details: [`ID da secretaria: ${secretariaId}`]
      }, { status: 400 })
    }

    // Verificar se tipo existe
    const { data: tipoExists, error: tipoCheckError } = await supabase
      .from("tipos")
      .select("id")
      .eq("id", tipoId)
      .maybeSingle()

    if (tipoCheckError) {
      console.error("Erro ao verificar tipo:", tipoCheckError)
      return NextResponse.json({
        error: "Erro ao verificar tipo",
        details: [tipoCheckError.message]
      }, { status: 500 })
    }

    if (!tipoExists) {
      return NextResponse.json({
        error: "Tipo não encontrado",
        details: [`ID do tipo: ${tipoId}`]
      }, { status: 400 })
    }

    // Verificar se o borderoCod já existe
    const { data: existingBordero, error: checkError } = await supabase
      .from("borderos")
      .select("id")
      .eq("bordero_cod", borderoCod)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar bordero existente:", checkError)
      return NextResponse.json({ error: "Erro ao verificar bordero existente" }, { status: 500 })
    }

    if (existingBordero) {
      return NextResponse.json({ error: "Código de bordero já existe" }, { status: 400 })
    }

    // Criar o bordero
    const { data: bordero, error: createError } = await supabase
      .from("borderos")
      .insert({
        bordero_cod: borderoCod,
        valor: Number.parseFloat(valor),
        data: new Date(data).toISOString(),
        nome_empresa: nomeEmpresa,
        secretaria_id: secretariaId,
        tipo_id: tipoId,
        observacao,
        responsavel_id: responsavelId || userSession.user.id,
        status: "novo",
      })
      .select()
      .single()

    if (createError) {
      console.error("Erro ao criar bordero:", createError)
      return NextResponse.json({
        error: "Erro ao criar bordero",
        details: [createError.message],
        code: createError.code,
        hint: createError.hint
      }, { status: 500 })
    }

    // Buscar dados do usuário para o log
    const { data: usuario } = await supabase
      .from("usuarios")
      .select("nome")
      .eq("id", userSession.user.id)
      .single()

    // Registrar log de criação
    try {
      await logBorderoComplete(supabase, {
        borderoId: bordero.id,
        usuarioId: userSession.user.id,
        acao: "criar",
        detalhes: {
          tipo: 'criacao',
          bordero_cod: borderoCod,
          nome_empresa: nomeEmpresa,
          valor: Number.parseFloat(valor),
          criado_em: new Date().toISOString(),
          criado_por: usuario?.nome || 'Usuário desconhecido'
        },
        ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown"
      })
    } catch (logError) {
      console.error("Erro ao registrar logs:", logError)
    }

    // Adicionar devolucoes se existirem
    if (devolucoes && devolucoes.length > 0) {
      const devolucoesData = devolucoes.map((usuarioId: string) => ({
        bordero_id: bordero.id,
        usuario_id: usuarioId,
      }))

      const { error: devolucoesError } = await supabase.from("bordero_devolucoes").insert(devolucoesData)

      if (devolucoesError) {
        console.error("Erro ao adicionar devoluções:", devolucoesError)
      }
    }

    // Atualizar o valor total da secretaria
    const { data: secretaria, error: secretariaFetchError } = await supabase
      .from("secretarias")
      .select("valores_total")
      .eq("id", secretariaId)
      .single()

    if (!secretariaFetchError && secretaria) {
      const novoValorTotal = (secretaria.valores_total || 0) + Number.parseFloat(valor)

      const { error: secretariaUpdateError } = await supabase
        .from("secretarias")
        .update({ valores_total: novoValorTotal })
        .eq("id", secretariaId)

      if (secretariaUpdateError) {
        console.error("Erro ao atualizar valor total da secretaria:", secretariaUpdateError)
      }
    }

    // Determinar o direcionamento com base no valor
    const { data: direcionamentos, error: direcionamentoError } = await supabase
      .from("direcionamentos")
      .select(`
        *,
        usuarios:direcionamento_usuarios(
          usuario:usuarios(*)
        )
      `)
      .lte("valor_minimo", Number.parseFloat(valor))
      .gte("valor_maximo", Number.parseFloat(valor))

    if (!direcionamentoError && direcionamentos && direcionamentos.length > 0) {
      const direcionamento = direcionamentos[0]

      // Enviar notificações para os usuários do direcionamento
      for (const du of direcionamento.usuarios) {
        // Inserir notificação no banco
        const { data: notificacao, error: notifError } = await supabase
          .from("notificacoes")
          .insert({
            titulo: "Novo Bordero Cadastrado",
            mensagem: `O bordero ${borderoCod} no valor de R$ ${valor} foi direcionado para você.`,
            usuario_id: du.usuario.id,
            lida: false,
          })
          .select()
          .single()

        if (notifError) {
          console.error("Erro ao criar notificação:", notifError)
        } else {
          // Enviar notificação em tempo real
          await pusherServer.trigger("notificacoes", "nova-notificacao", {
            id: notificacao.id,
            titulo: "Novo Bordero Cadastrado",
            mensagem: `O bordero ${borderoCod} no valor de R$ ${valor} foi direcionado para você.`,
            usuario_id: du.usuario.id,
            lida: false,
            createdAt: new Date().toISOString(),
          })
        }
      }
    }

    return NextResponse.json(bordero)
  } catch (error) {
    console.error("Erro ao criar bordero:", error)
    return NextResponse.json({ error: "Erro ao criar bordero" }, { status: 500 })
  }
}
