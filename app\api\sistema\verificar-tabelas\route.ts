import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const tablesToCheck = [
      "usuarios",
      "niveis_acesso", 
      "secretarias",
      "tipos",
      "borderos",
      "bordero_logs",
      "bordero_comentarios",
      "log_atividades",
      "configuracoes_sistema",
      "direcionamentos",
      "notificacoes"
    ]

    const results = []

    for (const tableName of tablesToCheck) {
      try {
        // Verificar se a tabela existe e contar registros
        const { data, error, count } = await supabase
          .from(tableName)
          .select("*", { count: "exact", head: true })

        if (error) {
          results.push({
            name: tableName,
            exists: false,
            count: 0,
            error: error.message
          })
        } else {
          results.push({
            name: tableName,
            exists: true,
            count: count || 0
          })
        }
      } catch (tableError) {
        results.push({
          name: tableName,
          exists: false,
          count: 0,
          error: `Erro ao verificar: ${tableError}`
        })
      }
    }

    // Verificar configurações críticas
    const criticalChecks = []

    // Verificar se existe pelo menos um usuário admin
    try {
      const { data: adminUsers, error: adminError } = await supabase
        .from("usuarios")
        .select("id")
        .eq("nivel_acesso_id", "admin")
        .limit(1)

      if (!adminError && adminUsers && adminUsers.length > 0) {
        criticalChecks.push({
          check: "admin_user",
          status: "ok",
          message: "Usuário administrador encontrado"
        })
      } else {
        criticalChecks.push({
          check: "admin_user", 
          status: "warning",
          message: "Nenhum usuário administrador encontrado"
        })
      }
    } catch (error) {
      criticalChecks.push({
        check: "admin_user",
        status: "error", 
        message: "Erro ao verificar usuários admin"
      })
    }

    // Verificar configurações do sistema
    try {
      const { data: config, error: configError } = await supabase
        .from("configuracoes_sistema")
        .select("*")
        .limit(1)

      if (!configError && config && config.length > 0) {
        criticalChecks.push({
          check: "system_config",
          status: "ok",
          message: "Configurações do sistema encontradas"
        })
      } else {
        criticalChecks.push({
          check: "system_config",
          status: "warning", 
          message: "Configurações do sistema não encontradas"
        })
      }
    } catch (error) {
      criticalChecks.push({
        check: "system_config",
        status: "error",
        message: "Erro ao verificar configurações"
      })
    }

    return NextResponse.json({
      success: true,
      tables: results,
      criticalChecks,
      summary: {
        totalTables: tablesToCheck.length,
        existingTables: results.filter(r => r.exists).length,
        missingTables: results.filter(r => !r.exists).length,
        emptyTables: results.filter(r => r.exists && r.count === 0).length
      }
    })

  } catch (error) {
    console.error("Erro ao verificar tabelas:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
