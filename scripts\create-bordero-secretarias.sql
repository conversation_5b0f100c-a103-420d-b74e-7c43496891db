-- Criar tabela de relacionamento bordero_secretarias (many-to-many)
CREATE TABLE IF NOT EXISTS bordero_secretarias (
    id SERIAL PRIMARY KEY,
    bordero_id INTEGER REFERENCES borderos(id) ON DELETE CASCADE,
    secretaria_id INTEGER REFERENCES secretarias(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bordero_id, secretaria_id)
);

-- Habilitar RLS
ALTER TABLE bordero_secretarias ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir acesso total
DROP POLICY IF EXISTS "Permitir acesso total a bordero_secretarias" ON bordero_secretarias;
CREATE POLICY "Permitir acesso total a bordero_secretarias" ON bordero_secretarias
FOR ALL USING (true);

-- Migrar dados existentes da coluna secretaria_id para a nova tabela
INSERT INTO bordero_secretarias (bordero_id, secretaria_id)
SELECT id, secretaria_id 
FROM borderos 
WHERE secretaria_id IS NOT NULL
ON CONFLICT (bordero_id, secretaria_id) DO NOTHING;

-- Comentar a coluna secretaria_id (manter por compatibilidade temporária)
COMMENT ON COLUMN borderos.secretaria_id IS 'DEPRECATED: Use bordero_secretarias table instead';

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_bordero_secretarias_bordero_id ON bordero_secretarias(bordero_id);
CREATE INDEX IF NOT EXISTS idx_bordero_secretarias_secretaria_id ON bordero_secretarias(secretaria_id);

-- Função para atualizar automaticamente a primeira secretaria na coluna legacy
CREATE OR REPLACE FUNCTION update_bordero_primary_secretaria()
RETURNS TRIGGER AS $$
BEGIN
    -- Atualizar a coluna secretaria_id com a primeira secretaria associada
    UPDATE borderos 
    SET secretaria_id = (
        SELECT secretaria_id 
        FROM bordero_secretarias 
        WHERE bordero_id = NEW.bordero_id 
        ORDER BY created_at ASC 
        LIMIT 1
    )
    WHERE id = NEW.bordero_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para manter compatibilidade
DROP TRIGGER IF EXISTS trigger_update_bordero_primary_secretaria ON bordero_secretarias;
CREATE TRIGGER trigger_update_bordero_primary_secretaria
    AFTER INSERT OR DELETE ON bordero_secretarias
    FOR EACH ROW
    EXECUTE FUNCTION update_bordero_primary_secretaria();
