import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("🔧 Criando tabelas de logs...")

    // SQL para criar as tabelas
    const createTablesSQL = `
      -- <PERSON><PERSON>r tabel<PERSON> de logs de atividade
      CREATE TABLE IF NOT EXISTS log_atividades (
          id SERIAL PRIMARY KEY,
          usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
          acao VARCHAR(255) NOT NULL,
          entidade VARCHAR(255) NOT NULL,
          entidade_id VARCHAR(255),
          detalhes JSONB,
          ip VARCHAR(45),
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- <PERSON><PERSON><PERSON> ta<PERSON><PERSON> de logs específicos de borderos
      CREATE TABLE IF NOT EXISTS bordero_logs (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        bordero_id UUID NOT NULL,
        usuario_id UUID REFERENCES usuarios(id),
        acao VARCHAR(50) NOT NULL,
        detalhes TEXT,
        data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Criar índices para melhor performance
      CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
      CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
      CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_bordero_id ON bordero_logs(bordero_id);
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_usuario_id ON bordero_logs(usuario_id);
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_data_hora ON bordero_logs(data_hora);

      -- Desabilitar RLS para simplificar
      ALTER TABLE log_atividades DISABLE ROW LEVEL SECURITY;
      ALTER TABLE bordero_logs DISABLE ROW LEVEL SECURITY;
    `

    // Tentar executar o SQL
    const { error } = await supabase.rpc('exec_sql', { sql: createTablesSQL })

    if (error) {
      console.error("Erro ao executar SQL:", error)
      
      // Tentar método alternativo - inserir registros de teste
      console.log("🔄 Tentando método alternativo...")
      
      // Testar log_atividades
      const { error: logError } = await supabase
        .from('log_atividades')
        .insert({
          usuario_id: '00000000-0000-0000-0000-000000000000',
          acao: 'teste',
          entidade: 'sistema',
          entidade_id: null,
          detalhes: { teste: 'Teste de criação da tabela' },
          ip: '127.0.0.1',
          user_agent: 'Setup API'
        })

      // Testar bordero_logs
      const { error: borderoLogError } = await supabase
        .from('bordero_logs')
        .insert({
          bordero_id: '00000000-0000-0000-0000-000000000000',
          usuario_id: '00000000-0000-0000-0000-000000000000',
          acao: 'teste',
          detalhes: 'Teste de criação da tabela',
          data_hora: new Date().toISOString()
        })

      if (logError && borderoLogError) {
        return NextResponse.json({
          success: false,
          message: "Tabelas não existem e não foi possível criá-las automaticamente",
          sql: createTablesSQL,
          errors: {
            log_atividades: logError.message,
            bordero_logs: borderoLogError.message
          }
        }, { status: 500 })
      }

      // Limpar registros de teste
      await supabase.from('log_atividades').delete().eq('acao', 'teste')
      await supabase.from('bordero_logs').delete().eq('acao', 'teste')
    }

    // Verificar se as tabelas foram criadas
    const { data: logTest } = await supabase
      .from('log_atividades')
      .select('id')
      .limit(1)

    const { data: borderoLogTest } = await supabase
      .from('bordero_logs')
      .select('id')
      .limit(1)

    return NextResponse.json({
      success: true,
      message: "Tabelas de logs configuradas com sucesso",
      tables: {
        log_atividades: logTest !== null,
        bordero_logs: borderoLogTest !== null
      }
    })

  } catch (error) {
    console.error("Erro ao configurar tabelas de logs:", error)
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
