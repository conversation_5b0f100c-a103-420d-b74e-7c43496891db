import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request, { params }: { params: Promise<{ tabela: string }> }) {
  try {
    const { tabela } = await params
    const url = new URL(request.url)
    const incluirDados = url.searchParams.get("dados") === "true"

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // Verificar autenticação
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Verificar se o usuário é administrador
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: currentUser } = await supabaseAdmin
      .from("usuarios")
      .select(`
        nivel_acesso:niveis_acesso(nome)
      `)
      .eq("id", session.user.id)
      .single()

    const isAdmin = currentUser?.nivel_acesso?.nome === "Administrador"
    
    if (!isAdmin) {
      return NextResponse.json({ error: "Apenas administradores podem exportar tabelas" }, { status: 403 })
    }

    // Validar tabela permitida
    const tabelasPermitidas = ["usuarios", "borderos", "secretarias", "tipos", "direcionamentos"]
    if (!tabelasPermitidas.includes(tabela)) {
      return NextResponse.json({ error: "Tabela não permitida para exportação" }, { status: 400 })
    }

    let sqlContent = ""

    // Gerar SQL baseado na tabela
    switch (tabela) {
      case "usuarios":
        sqlContent = await gerarSqlUsuarios(supabaseAdmin, incluirDados)
        break
      case "borderos":
        sqlContent = await gerarSqlBorderos(supabaseAdmin, incluirDados)
        break
      case "secretarias":
        sqlContent = await gerarSqlSecretarias(supabaseAdmin, incluirDados)
        break
      case "tipos":
        sqlContent = await gerarSqlTipos(supabaseAdmin, incluirDados)
        break
      case "direcionamentos":
        sqlContent = await gerarSqlDirecionamentos(supabaseAdmin, incluirDados)
        break
      default:
        return NextResponse.json({ error: "Tabela não suportada" }, { status: 400 })
    }

    // Registrar log de exportação
    try {
      await supabaseAdmin
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "exportar",
          entidade: "tabela",
          entidade_id: tabela,
          detalhes: {
            tabela,
            incluir_dados: incluirDados,
            timestamp: new Date().toISOString()
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exportação:", logError)
    }

    // Retornar arquivo SQL
    return new NextResponse(sqlContent, {
      headers: {
        "Content-Type": "application/sql",
        "Content-Disposition": `attachment; filename="${tabela}_${incluirDados ? 'com_dados' : 'estrutura'}_${new Date().toISOString().split('T')[0]}.sql"`
      }
    })

  } catch (error) {
    console.error("Erro ao exportar tabela:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

async function gerarSqlUsuarios(supabase: any, incluirDados: boolean): Promise<string> {
  let sql = `-- Exportação da tabela usuarios
-- Data: ${new Date().toISOString()}

-- Estrutura da tabela usuarios
CREATE TABLE IF NOT EXISTS usuarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    nivel_acesso_id UUID REFERENCES niveis_acesso(id),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Estrutura da tabela niveis_acesso
CREATE TABLE IF NOT EXISTS niveis_acesso (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    permissoes JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

`

  if (incluirDados) {
    // Exportar dados dos níveis de acesso primeiro
    const { data: niveisAcesso } = await supabase
      .from("niveis_acesso")
      .select("*")

    if (niveisAcesso && niveisAcesso.length > 0) {
      sql += "-- Dados da tabela niveis_acesso\n"
      for (const nivel of niveisAcesso) {
        const permissoes = JSON.stringify(nivel.permissoes).replace(/'/g, "''")
        sql += `INSERT INTO niveis_acesso (id, nome, descricao, permissoes, created_at, updated_at) VALUES ('${nivel.id}', '${nivel.nome}', '${nivel.descricao || ''}', '${permissoes}', '${nivel.created_at}', '${nivel.updated_at}');\n`
      }
      sql += "\n"
    }

    // Exportar dados dos usuários
    const { data: usuarios } = await supabase
      .from("usuarios")
      .select("*")

    if (usuarios && usuarios.length > 0) {
      sql += "-- Dados da tabela usuarios\n"
      for (const usuario of usuarios) {
        sql += `INSERT INTO usuarios (id, nome, email, nivel_acesso_id, avatar_url, created_at, updated_at) VALUES ('${usuario.id}', '${usuario.nome.replace(/'/g, "''")}', '${usuario.email}', '${usuario.nivel_acesso_id}', ${usuario.avatar_url ? `'${usuario.avatar_url}'` : 'NULL'}, '${usuario.created_at}', '${usuario.updated_at}');\n`
      }
    }
  }

  return sql
}

async function gerarSqlBorderos(supabase: any, incluirDados: boolean): Promise<string> {
  let sql = `-- Exportação da tabela borderos
-- Data: ${new Date().toISOString()}

-- Estrutura da tabela borderos
CREATE TABLE IF NOT EXISTS borderos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bordero_cod VARCHAR(50) UNIQUE NOT NULL,
    empresa VARCHAR(255) NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data_bordero DATE NOT NULL,
    observacao TEXT,
    status VARCHAR(50) DEFAULT 'pendente',
    secretaria_id UUID REFERENCES secretarias(id),
    tipo_id UUID REFERENCES tipos(id),
    direcionamento_id UUID REFERENCES direcionamentos(id),
    usuario_id UUID REFERENCES usuarios(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Estrutura da tabela bordero_comentarios
CREATE TABLE IF NOT EXISTS bordero_comentarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bordero_id UUID REFERENCES borderos(id) ON DELETE CASCADE,
    usuario_id UUID REFERENCES usuarios(id),
    comentario TEXT NOT NULL,
    mencoes UUID[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

`

  if (incluirDados) {
    // Exportar dados dos borderos
    const { data: borderos } = await supabase
      .from("borderos")
      .select("*")

    if (borderos && borderos.length > 0) {
      sql += "-- Dados da tabela borderos\n"
      for (const bordero of borderos) {
        const observacao = bordero.observacao ? `'${bordero.observacao.replace(/'/g, "''")}'` : 'NULL'
        sql += `INSERT INTO borderos (id, bordero_cod, empresa, valor, data_bordero, observacao, status, secretaria_id, tipo_id, direcionamento_id, usuario_id, created_at, updated_at) VALUES ('${bordero.id}', '${bordero.bordero_cod}', '${bordero.empresa.replace(/'/g, "''")}', ${bordero.valor}, '${bordero.data_bordero}', ${observacao}, '${bordero.status}', ${bordero.secretaria_id ? `'${bordero.secretaria_id}'` : 'NULL'}, ${bordero.tipo_id ? `'${bordero.tipo_id}'` : 'NULL'}, ${bordero.direcionamento_id ? `'${bordero.direcionamento_id}'` : 'NULL'}, '${bordero.usuario_id}', '${bordero.created_at}', '${bordero.updated_at}');\n`
      }
      sql += "\n"
    }

    // Exportar comentários
    const { data: comentarios } = await supabase
      .from("bordero_comentarios")
      .select("*")

    if (comentarios && comentarios.length > 0) {
      sql += "-- Dados da tabela bordero_comentarios\n"
      for (const comentario of comentarios) {
        const mencoes = comentario.mencoes ? `'{${comentario.mencoes.join(',')}}'` : 'NULL'
        sql += `INSERT INTO bordero_comentarios (id, bordero_id, usuario_id, comentario, mencoes, created_at, updated_at) VALUES ('${comentario.id}', '${comentario.bordero_id}', '${comentario.usuario_id}', '${comentario.comentario.replace(/'/g, "''")}', ${mencoes}, '${comentario.created_at}', '${comentario.updated_at}');\n`
      }
    }
  }

  return sql
}

async function gerarSqlSecretarias(supabase: any, incluirDados: boolean): Promise<string> {
  let sql = `-- Exportação da tabela secretarias
-- Data: ${new Date().toISOString()}

-- Estrutura da tabela secretarias
CREATE TABLE IF NOT EXISTS secretarias (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    ativa BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

`

  if (incluirDados) {
    const { data: secretarias } = await supabase
      .from("secretarias")
      .select("*")

    if (secretarias && secretarias.length > 0) {
      sql += "-- Dados da tabela secretarias\n"
      for (const secretaria of secretarias) {
        const descricao = secretaria.descricao ? `'${secretaria.descricao.replace(/'/g, "''")}'` : 'NULL'
        sql += `INSERT INTO secretarias (id, nome, descricao, ativa, created_at, updated_at) VALUES ('${secretaria.id}', '${secretaria.nome.replace(/'/g, "''")}', ${descricao}, ${secretaria.ativa}, '${secretaria.created_at}', '${secretaria.updated_at}');\n`
      }
    }
  }

  return sql
}

async function gerarSqlTipos(supabase: any, incluirDados: boolean): Promise<string> {
  let sql = `-- Exportação da tabela tipos
-- Data: ${new Date().toISOString()}

-- Estrutura da tabela tipos
CREATE TABLE IF NOT EXISTS tipos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

`

  if (incluirDados) {
    const { data: tipos } = await supabase
      .from("tipos")
      .select("*")

    if (tipos && tipos.length > 0) {
      sql += "-- Dados da tabela tipos\n"
      for (const tipo of tipos) {
        const descricao = tipo.descricao ? `'${tipo.descricao.replace(/'/g, "''")}'` : 'NULL'
        sql += `INSERT INTO tipos (id, nome, descricao, ativo, created_at, updated_at) VALUES ('${tipo.id}', '${tipo.nome.replace(/'/g, "''")}', ${descricao}, ${tipo.ativo}, '${tipo.created_at}', '${tipo.updated_at}');\n`
      }
    }
  }

  return sql
}

async function gerarSqlDirecionamentos(supabase: any, incluirDados: boolean): Promise<string> {
  let sql = `-- Exportação da tabela direcionamentos
-- Data: ${new Date().toISOString()}

-- Estrutura da tabela direcionamentos
CREATE TABLE IF NOT EXISTS direcionamentos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    usuarios_acesso UUID[],
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

`

  if (incluirDados) {
    const { data: direcionamentos } = await supabase
      .from("direcionamentos")
      .select("*")

    if (direcionamentos && direcionamentos.length > 0) {
      sql += "-- Dados da tabela direcionamentos\n"
      for (const direcionamento of direcionamentos) {
        const descricao = direcionamento.descricao ? `'${direcionamento.descricao.replace(/'/g, "''")}'` : 'NULL'
        const usuariosAcesso = direcionamento.usuarios_acesso ? `'{${direcionamento.usuarios_acesso.join(',')}}'` : 'NULL'
        sql += `INSERT INTO direcionamentos (id, nome, descricao, usuarios_acesso, ativo, created_at, updated_at) VALUES ('${direcionamento.id}', '${direcionamento.nome.replace(/'/g, "''")}', ${descricao}, ${usuariosAcesso}, ${direcionamento.ativo}, '${direcionamento.created_at}', '${direcionamento.updated_at}');\n`
      }
    }
  }

  return sql
}
