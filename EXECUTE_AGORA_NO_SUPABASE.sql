-- Script para corrigir os problemas identificados no sistema

-- 0. Verificar a estrutura atual dos dados
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;

-- 1. Corrigir status dos borderos para garantir que todos estejam com status "novo"
-- Primeiro, verificar quantos borderos não estão com status "novo"
SELECT status, COUNT(*)
FROM borderos
GROUP BY status
ORDER BY status;

-- Verificar se há borderos com status nulo ou vazio
SELECT COUNT(*) as borderos_sem_status
FROM borderos
WHERE status IS NULL OR status = '';

-- Atualizar todos os borderos importados para status "novo"
UPDATE borderos
SET status = 'novo'
WHERE status IS NULL OR status = '' OR status NOT IN ('novo', 'analise', 'assinado', 'pago');

-- Verificar se há borderos com status inválido (deve ser 'novo', 'analise', 'assinado', 'pago')
SELECT status, COUNT(*)
FROM borderos
GROUP BY status
ORDER BY status;

-- Verificar se há borderos com status 'novo'
SELECT COUNT(*) as borderos_novos
FROM borderos
WHERE status = 'novo';

-- Verificar se há borderos com status 'analise'
SELECT COUNT(*) as borderos_analise
FROM borderos
WHERE status = 'analise';

-- Verificar se há borderos com status 'assinado'
SELECT COUNT(*) as borderos_assinados
FROM borderos
WHERE status = 'assinado';

-- Verificar se há borderos com status 'pago'
SELECT COUNT(*) as borderos_pagos
FROM borderos
WHERE status = 'pago';

-- 2. Recalcular os valores totais das secretarias
-- Primeiro, verificar os valores atuais
SELECT id, nome, valores_total
FROM secretarias
ORDER BY id;

-- Zerar os valores totais
UPDATE secretarias
SET valores_total = 0;

-- Recalcular com base nos borderos existentes
WITH valores_por_secretaria AS (
  SELECT
    secretaria_id,
    SUM(valor) as total_valor
  FROM borderos
  WHERE secretaria_id IS NOT NULL
  GROUP BY secretaria_id
)
UPDATE secretarias s
SET valores_total = COALESCE(v.total_valor, 0)
FROM valores_por_secretaria v
WHERE s.id = v.secretaria_id;

-- Verificar os valores após a atualização
SELECT id, nome, valores_total
FROM secretarias
ORDER BY id;

-- 3. Verificar e corrigir inconsistências nos dados
-- Verificar borderos sem secretaria
SELECT COUNT(*) as borderos_sem_secretaria
FROM borderos
WHERE secretaria_id IS NULL;

-- Verificar borderos sem tipo
SELECT COUNT(*) as borderos_sem_tipo
FROM borderos
WHERE tipo_id IS NULL;

-- 4. Verificar se há discrepância entre os dados mostrados em diferentes páginas
-- Contar borderos por status
SELECT status, COUNT(*)
FROM borderos
GROUP BY status
ORDER BY status;

-- Verificar o total de borderos
SELECT COUNT(*) as total_borderos FROM borderos;

-- Verificar o valor total de todos os borderos
SELECT SUM(valor) as valor_total FROM borderos;

-- 5. Verificar e corrigir possíveis problemas de cache ou visualização
-- Adicionar um timestamp atualizado para forçar a atualização dos dados
UPDATE borderos
SET updated_at = NOW()
WHERE status = 'novo';

-- Verificar se há algum problema com a visualização dos borderos novos
SELECT COUNT(*) as total_borderos_novos
FROM borderos
WHERE status = 'novo';

-- Verificar se há algum problema com a visualização dos borderos em análise
SELECT COUNT(*) as total_borderos_analise
FROM borderos
WHERE status = 'analise';

-- Verificar se há algum problema com a visualização dos borderos assinados
SELECT COUNT(*) as total_borderos_assinados
FROM borderos
WHERE status = 'assinado';

-- Verificar se há algum problema com a visualização dos borderos pagos
SELECT COUNT(*) as total_borderos_pagos
FROM borderos
WHERE status = 'pago';

-- Verificar o valor total de todos os borderos
SELECT SUM(valor) as valor_total_borderos
FROM borderos;

-- Log da execução do script
INSERT INTO log_atividades (
  usuario_id,
  acao,
  entidade,
  detalhes
)
VALUES (
  (SELECT id FROM usuarios WHERE email = '<EMAIL>' LIMIT 1), -- Substitua pelo ID de um usuário administrador
  'correcao_sistema',
  'sistema',
  jsonb_build_object(
    'descricao', 'Correção automática de borderos e valores de secretarias',
    'timestamp', NOW(),
    'detalhes', 'Correção de status de borderos e recálculo de valores totais'
  )
);
