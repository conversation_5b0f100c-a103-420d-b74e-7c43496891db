import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

export async function POST(request: Request) {
  try {
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    // Verificar se o usuário está autenticado
    const {
      data: { user },
      error: authError
    } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const body = await request.json()
    const { acao, entidade, entidade_id, detalhes } = body

    // Validar dados
    if (!acao || !entidade) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 })
    }

    // Usar service role para inserir logs
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Obter informações do request
    const ip = request.headers.get("x-forwarded-for") ||
               request.headers.get("x-real-ip") ||
               "desconhecido"
    const userAgent = request.headers.get("user-agent") || "desconhecido"

    // Registrar o log
    const { error } = await supabase.from("log_atividades").insert({
      usuario_id: user.id,
      acao,
      entidade,
      entidade_id: entidade_id?.toString(),
      detalhes: detalhes || {},
      ip,
      user_agent: userAgent,
    })

    if (error) {
      console.error("Erro ao registrar log:", error)
      return NextResponse.json({ error: "Erro ao registrar log" }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao registrar log:", error)
    return NextResponse.json({ error: "Erro ao registrar log" }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    // Verificar se o usuário está autenticado
    const {
      data: { user },
      error: authError
    } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Usar service role para buscar logs
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Obter parâmetros de consulta
    const { searchParams } = new URL(request.url)
    const limit = Number.parseInt(searchParams.get("limit") || "50")
    const offset = Number.parseInt(searchParams.get("offset") || "0")
    const usuario = searchParams.get("usuario")
    const acao = searchParams.get("acao")
    const entidade = searchParams.get("entidade")
    const dataInicio = searchParams.get("dataInicio")
    const dataFim = searchParams.get("dataFim")

    // Tentar criar a tabela se ela não existir
    try {
      // Verificar se a tabela existe
      const { data: tableExists } = await supabase
        .from("log_atividades")
        .select("id")
        .limit(1)

      // Se chegou até aqui, a tabela existe
    } catch (tableError) {
      // Se a tabela não existir, criar ela
      if (tableError.code === '42P01') {
        try {
          // Criar tabela usando SQL direto
          const { error: createError } = await supabase.rpc('exec_sql', {
            sql: `
              CREATE TABLE IF NOT EXISTS log_atividades (
                  id SERIAL PRIMARY KEY,
                  usuario_id UUID,
                  acao VARCHAR(255) NOT NULL,
                  entidade VARCHAR(255) NOT NULL,
                  entidade_id VARCHAR(255),
                  detalhes JSONB DEFAULT '{}',
                  ip VARCHAR(45),
                  user_agent TEXT,
                  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );

              CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
              CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
              CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

              ALTER TABLE log_atividades DISABLE ROW LEVEL SECURITY;
            `
          })

          if (createError) {
            console.error("Erro ao criar tabela log_atividades:", createError)
          }
        } catch (sqlError) {
          console.error("Erro ao executar SQL:", sqlError)
        }
      }
    }

    // Construir consulta - primeiro vamos testar sem join
    let query = supabase
      .from("log_atividades")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1)

    // Aplicar filtros
    if (usuario) {
      query = query.eq("usuario_id", usuario)
    }

    if (acao) {
      query = query.eq("acao", acao)
    }

    if (entidade) {
      query = query.eq("entidade", entidade)
    }

    if (dataInicio) {
      query = query.gte("created_at", dataInicio)
    }

    if (dataFim) {
      query = query.lte("created_at", dataFim)
    }

    // Executar consulta
    const { data: logs, error } = await query

    if (error) {
      // Se a tabela não existir, retornar dados vazios em vez de erro
      if (error.code === '42P01') {
        return NextResponse.json({
          logs: [],
          count: 0,
          message: "Tabela de logs não configurada. Execute o SQL de setup no Supabase."
        })
      }

      console.error("Erro ao buscar logs:", error)
      return NextResponse.json({ error: "Erro ao buscar logs" }, { status: 500 })
    }

    // Buscar usuários para os logs
    let logsComUsuarios = logs || []
    if (logs && logs.length > 0) {
      const usuarioIds = [...new Set(logs.map(log => log.usuario_id).filter(Boolean))]

      if (usuarioIds.length > 0) {
        const { data: usuarios, error: usuariosError } = await supabase
          .from("usuarios")
          .select("id, nome, email")
          .in("id", usuarioIds)

        if (!usuariosError && usuarios) {
          const usuariosMap = new Map(usuarios.map(u => [u.id, u]))

          logsComUsuarios = logs.map(log => ({
            ...log,
            usuarios: usuariosMap.get(log.usuario_id) || null
          }))
        }
      }
    }

    // Buscar contagem total separadamente
    const { count, error: countError } = await supabase
      .from("log_atividades")
      .select("*", { count: "exact", head: true })

    if (countError) {
      console.error("Erro ao buscar contagem:", countError)
    }

    return NextResponse.json({
      logs: logsComUsuarios,
      count: count || 0
    })
  } catch (error) {
    console.error("Erro ao buscar logs:", error)
    return NextResponse.json({ error: "Erro ao buscar logs" }, { status: 500 })
  }
}
