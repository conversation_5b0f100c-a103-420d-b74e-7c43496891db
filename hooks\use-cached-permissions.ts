"use client"

import { useState, useEffect, useCallback } from "react"
import { useAuth } from "@/hooks/use-auth"
import { getPermissoes, hasPermission, canAccessRoute, type NivelAcesso, type Permisso<PERSON> } from "@/lib/permissions"

// Cache structure to store permissions
interface PermissionsCache {
  [userId: string]: {
    nivelAcesso: NivelAcesso
    permissoes: Permissoes
    timestamp: number
  }
}

// In-memory cache
const permissionsCache: PermissionsCache = {}

// Cache expiration time (10 minutes)
const CACHE_EXPIRATION = 10 * 60 * 1000

export function useCachedPermissions() {
  const { user, loading, isAuthenticated } = useAuth()
  const [nivelAcesso, setNivelAcesso] = useState<NivelAcesso>('visualizador')
  const [permissionsLoading, setPermissionsLoading] = useState(true)
  const [permissoes, setPermissoes] = useState<Permissoes | null>(null)

  // Function to fetch user's nivel de acesso from the database
  const fetchNivelAcesso = useCallback(async (userId: string) => {
    try {
      // Check if we have a valid cache entry
      const cacheEntry = permissionsCache[userId]
      if (cacheEntry && (Date.now() - cacheEntry.timestamp) < CACHE_EXPIRATION) {
        console.log("Using cached permissions for user", userId)
        setNivelAcesso(cacheEntry.nivelAcesso)
        setPermissoes(cacheEntry.permissoes)
        setPermissionsLoading(false)
        return cacheEntry.nivelAcesso
      }

      console.log("Fetching permissions for user", userId)
      try {
        // If no cache or expired, fetch from API
        const response = await fetch(`/api/usuarios/${userId}/permissions`)
        
        // Mesmo se a resposta não for ok, tentamos processar o JSON
        // pois nossa API pode retornar um nível padrão mesmo em caso de erro
        const data = await response.json()
        
        // Se tiver um erro explícito e não tiver nivelAcesso, usamos o padrão
        if (!response.ok && !data.nivelAcesso) {
          console.warn("API returned error, using default permissions", data.error || response.statusText)
          const defaultNivel = 'visualizador'
          const defaultPerms = getPermissoes(defaultNivel)
          
          setNivelAcesso(defaultNivel)
          setPermissoes(defaultPerms)
          
          // Ainda atualizamos o cache, mas com uma expiração mais curta
          permissionsCache[userId] = {
            nivelAcesso: defaultNivel,
            permissoes: defaultPerms,
            timestamp: Date.now() - (CACHE_EXPIRATION / 2) // Expira em metade do tempo
          }
          
          return defaultNivel
        }
        
        // Processamento normal quando tudo está ok
        const nivel = data.nivelAcesso as NivelAcesso || 'visualizador'
        
        // Get permissions based on nivel
        const perms = getPermissoes(nivel)
        
        // Update state
        setNivelAcesso(nivel)
        setPermissoes(perms)
        
        // Update cache
        permissionsCache[userId] = {
          nivelAcesso: nivel,
          permissoes: perms,
          timestamp: Date.now()
        }
        
        return nivel
      } catch (error) {
        console.error("Error processing permissions response:", error)
        // Em caso de erro, usamos o nível padrão
        const defaultNivel = 'visualizador'
        const defaultPerms = getPermissoes(defaultNivel)
        
        setNivelAcesso(defaultNivel)
        setPermissoes(defaultPerms)
        
        return defaultNivel
      }
    } catch (error) {
      console.error("Error fetching user permissions:", error)
      // Default to visualizador on error
      const defaultNivel = 'visualizador'
      setNivelAcesso(defaultNivel)
      setPermissoes(getPermissoes(defaultNivel))
      return defaultNivel
    } finally {
      setPermissionsLoading(false)
    }
  }, [])

  useEffect(() => {
    if (!loading && isAuthenticated && user) {
      fetchNivelAcesso(user.id)
    } else if (!loading && !isAuthenticated) {
      setPermissionsLoading(false)
    }
  }, [loading, isAuthenticated, user, fetchNivelAcesso])

  const checkPermission = useCallback((modulo: keyof Permissoes, acao: string): boolean => {
    if (!isAuthenticated || !permissoes) return false
    return hasPermission(nivelAcesso, modulo, acao)
  }, [isAuthenticated, nivelAcesso, permissoes])

  const checkRouteAccess = useCallback((route: string): boolean => {
    if (!isAuthenticated) return false
    return canAccessRoute(nivelAcesso, route)
  }, [isAuthenticated, nivelAcesso])

  // Function to manually clear the cache (useful for testing or after role changes)
  const clearCache = useCallback((userId?: string) => {
    if (userId) {
      delete permissionsCache[userId]
    } else if (user) {
      delete permissionsCache[user.id]
    }
  }, [user])

  return {
    nivelAcesso,
    permissoes,
    loading: loading || permissionsLoading,
    isAuthenticated,
    hasPermission: checkPermission,
    canAccessRoute: checkRouteAccess,
    clearCache
  }
}