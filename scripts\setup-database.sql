-- <PERSON><PERSON><PERSON> para configurar o banco de dados do CRM
-- Execute este script no Supabase SQL Editor

-- <PERSON><PERSON><PERSON> tabela de níveis de acesso
CREATE TABLE IF NOT EXISTS niveis_acesso (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    permissoes JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>r tabel<PERSON> de usu<PERSON>rio<PERSON>
CREATE TABLE IF NOT EXISTS usuarios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    nome VARCHAR(255) NOT NULL,
    nivel_acesso_id UUID REFERENCES niveis_acesso(id),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON>r tabela de secretarias
CREATE TABLE IF NOT EXISTS secretarias (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    valores_total DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de tipos
CREATE TABLE IF NOT EXISTS tipos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de direcionamentos
CREATE TABLE IF NOT EXISTS direcionamentos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    valor_minimo DECIMAL(15,2) NOT NULL,
    valor_maximo DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de borderos
CREATE TABLE IF NOT EXISTS borderos (
    id SERIAL PRIMARY KEY,
    bordero_cod VARCHAR(255) UNIQUE NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data TIMESTAMP WITH TIME ZONE NOT NULL,
    nome_empresa VARCHAR(255) NOT NULL,
    secretaria_id INTEGER REFERENCES secretarias(id),
    tipo_id INTEGER REFERENCES tipos(id),
    observacao TEXT,
    status VARCHAR(50) DEFAULT 'novo',
    dados_status TEXT,
    data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responsavel_id UUID REFERENCES usuarios(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de notificações
CREATE TABLE IF NOT EXISTS notificacoes (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(255) NOT NULL,
    mensagem TEXT NOT NULL,
    lida BOOLEAN DEFAULT FALSE,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar tabela de logs de atividade
CREATE TABLE IF NOT EXISTS log_atividades (
    id SERIAL PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    acao VARCHAR(255) NOT NULL,
    entidade VARCHAR(255) NOT NULL,
    entidade_id VARCHAR(255),
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserir nível de acesso administrador
INSERT INTO niveis_acesso (id, nome, descricao, permissoes) 
VALUES (
    gen_random_uuid(),
    'Administrador',
    'Acesso completo ao sistema',
    '{
        "dashboard": true,
        "borderos": true,
        "secretarias": true,
        "direcionamentos": true,
        "tipos": true,
        "usuarios": true,
        "relatorios": true,
        "configuracoes": true
    }'::jsonb
) ON CONFLICT DO NOTHING;

-- Inserir nível de acesso operador
INSERT INTO niveis_acesso (nome, descricao, permissoes) 
VALUES (
    'Operador',
    'Acesso para operações básicas',
    '{
        "dashboard": true,
        "borderos": true,
        "secretarias": false,
        "direcionamentos": false,
        "tipos": false,
        "usuarios": false,
        "relatorios": true,
        "configuracoes": false
    }'::jsonb
) ON CONFLICT DO NOTHING;

-- Inserir secretarias de exemplo
INSERT INTO secretarias (nome, slug) VALUES 
('Secretaria de Finanças', 'financas'),
('Secretaria de Educação', 'educacao'),
('Secretaria de Saúde', 'saude'),
('Secretaria de Obras', 'obras')
ON CONFLICT (slug) DO NOTHING;

-- Inserir tipos de exemplo
INSERT INTO tipos (nome, descricao) VALUES 
('Pagamento', 'Bordero de pagamento'),
('Recebimento', 'Bordero de recebimento'),
('Transferência', 'Bordero de transferência')
ON CONFLICT DO NOTHING;

-- Inserir direcionamentos de exemplo
INSERT INTO direcionamentos (nome, valor_minimo, valor_maximo) VALUES 
('Pequeno Porte', 0, 10000),
('Médio Porte', 10000, 100000),
('Grande Porte', 100000, 999999999)
ON CONFLICT DO NOTHING;

-- Criar função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Criar triggers para atualizar updated_at
CREATE TRIGGER update_usuarios_updated_at BEFORE UPDATE ON usuarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_secretarias_updated_at BEFORE UPDATE ON secretarias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tipos_updated_at BEFORE UPDATE ON tipos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_direcionamentos_updated_at BEFORE UPDATE ON direcionamentos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_borderos_updated_at BEFORE UPDATE ON borderos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_niveis_acesso_updated_at BEFORE UPDATE ON niveis_acesso FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Habilitar RLS (Row Level Security) para as tabelas
ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE niveis_acesso ENABLE ROW LEVEL SECURITY;
ALTER TABLE secretarias ENABLE ROW LEVEL SECURITY;
ALTER TABLE tipos ENABLE ROW LEVEL SECURITY;
ALTER TABLE direcionamentos ENABLE ROW LEVEL SECURITY;
ALTER TABLE borderos ENABLE ROW LEVEL SECURITY;
ALTER TABLE notificacoes ENABLE ROW LEVEL SECURITY;
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;

-- Criar políticas básicas (permitir tudo para usuários autenticados)
CREATE POLICY "Permitir tudo para usuários autenticados" ON usuarios FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON niveis_acesso FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON secretarias FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON tipos FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON direcionamentos FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON borderos FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON notificacoes FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Permitir tudo para usuários autenticados" ON log_atividades FOR ALL USING (auth.role() = 'authenticated');
