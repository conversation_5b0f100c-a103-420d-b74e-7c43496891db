import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("Tentando criar tabela log_atividades...")

    // Tentar inserir um registro para forçar a criação da tabela
    const { error } = await supabase
      .from('log_atividades')
      .insert({
        usuario_id: null,
        acao: 'teste',
        entidade: 'sistema',
        entidade_id: null,
        detalhes: { teste: true },
        ip: '127.0.0.1',
        user_agent: 'Setup API'
      })

    if (error) {
      console.log("Erro esperado (tabela não existe):", error.message)
      
      // A tabela não existe, vamos retornar instruções
      return NextResponse.json({
        success: false,
        message: "Tabela log_atividades não existe",
        sql: `
-- Execute este SQL no Supabase SQL Editor:

CREATE TABLE IF NOT EXISTS log_atividades (
    id SERIAL PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    acao VARCHAR(255) NOT NULL,
    entidade VARCHAR(255) NOT NULL,
    entidade_id VARCHAR(255),
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

-- Desabilitar RLS temporariamente para teste
ALTER TABLE log_atividades DISABLE ROW LEVEL SECURITY;
        `
      })
    } else {
      console.log("Tabela já existe!")
      
      // Deletar o registro de teste
      await supabase
        .from('log_atividades')
        .delete()
        .eq('acao', 'teste')

      return NextResponse.json({
        success: true,
        message: "Tabela log_atividades já existe"
      })
    }

  } catch (error) {
    console.error("Erro:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
