import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { pusherServer } from "@/lib/pusher"

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { status, dadosStatus, usuarioId } = body

    // Usar service key para operações administrativas
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar o bordero atual para obter informações
    const { data: borderoAtual, error: fetchError } = await supabase
      .from("borderos")
      .select("*")
      .eq("id", id)
      .maybeSingle()

    if (fetchError) {
      console.error("Erro ao buscar bordero:", fetchError)
      return NextResponse.json({ error: "Erro ao buscar bordero" }, { status: 500 })
    }

    if (!borderoAtual) {
      console.error("Bordero não encontrado:", id)
      return NextResponse.json({ error: "Bordero não encontrado" }, { status: 404 })
    }

    // Atualizar o status do bordero
    const updateData: any = {
      status,
      dados_status: status === "corrigir" ? dadosStatus : null,
    }

    // Só incluir responsavel_id se for um UUID válido
    if (usuarioId && typeof usuarioId === 'string' && usuarioId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      updateData.responsavel_id = usuarioId
    }

    const { data: bordero, error: updateError } = await supabase
      .from("borderos")
      .update(updateData)
      .eq("id", id)
      .select()
      .maybeSingle()

    if (updateError) {
      console.error("Erro ao atualizar status do bordero:", updateError)
      return NextResponse.json({ error: "Erro ao atualizar status do bordero" }, { status: 500 })
    }

    if (!bordero) {
      console.error("Bordero não foi atualizado:", id)
      return NextResponse.json({ error: "Bordero não foi atualizado" }, { status: 404 })
    }

    // Registrar log de alteração de status
    try {
      await supabase
        .from("bordero_logs")
        .insert({
          bordero_id: id,
          usuario_id: usuarioId || "00000000-0000-0000-0000-000000000000",
          acao: "alterar_status",
          detalhes: `Status alterado de "${borderoAtual.status}" para "${status}"${dadosStatus ? `. Motivo: ${dadosStatus}` : ""}`,
          data_hora: new Date().toISOString()
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    console.log(`Status do bordero ${id} alterado de ${borderoAtual.status} para ${status}`)

    // Criar notificação simples para o responsável (se existir)
    if (borderoAtual.responsavel_id) {
      const notificacaoTitulo = `Bordero ${borderoAtual.bordero_cod} - Status Atualizado`
      let notificacaoMensagem = `O status do bordero foi alterado para ${status}.`

      if (status === "corrigir" && dadosStatus) {
        notificacaoMensagem = `O bordero precisa de correções: ${dadosStatus}`
      }

      try {
        // Inserir notificação no banco
        const { data: notificacao, error: notifError } = await supabase
          .from("notificacoes")
          .insert({
            titulo: notificacaoTitulo,
            mensagem: notificacaoMensagem,
            usuario_id: borderoAtual.responsavel_id,
            lida: false,
          })
          .select()
          .single()

        if (!notifError && notificacao) {
          // Enviar notificação em tempo real
          await pusherServer.trigger("notificacoes", "nova-notificacao", {
            id: notificacao.id,
            titulo: notificacaoTitulo,
            mensagem: notificacaoMensagem,
            usuario_id: borderoAtual.responsavel_id,
            lida: false,
            createdAt: new Date().toISOString(),
          })
        }
      } catch (notifError) {
        console.error("Erro ao criar notificação:", notifError)
      }
    }

    return NextResponse.json(bordero)
  } catch (error) {
    console.error("Erro ao atualizar status do bordero:", error)
    return NextResponse.json({ error: "Erro ao atualizar status do bordero" }, { status: 500 })
  }
}
