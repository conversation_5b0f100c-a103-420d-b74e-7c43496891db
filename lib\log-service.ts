import type { LogAcao, LogEntidade, LogDetalhes } from "@/types/logs"

// Função para uso no cliente
export async function registrarLogCliente(
  acao: LogAcao,
  entidade: LogEntidade,
  entidadeId?: string | number,
  detalhes?: LogDetalhes,
) {
  try {
    const response = await fetch("/api/logs", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        acao,
        entidade,
        entidadeId,
        detalhes,
      }),
    })

    if (!response.ok) {
      throw new Error(`Erro ao registrar log: ${response.status}`)
    }
  } catch (error) {
    console.error("Erro ao registrar log:", error)
  }
}
