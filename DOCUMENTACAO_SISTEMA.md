# 📋 Documentação do Sistema CRM de Bordero

## 🎯 Visão Geral

O **CRM de Bordero** é um sistema completo de gerenciamento de borderos (documentos fiscais) desenvolvido em **Next.js 15** com **TypeScript**, **Tailwind CSS**, **Prisma ORM** e **Supabase** como banco de dados PostgreSQL.

### 🚀 Tecnologias Utilizadas

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui
- **Backend**: Next.js API Routes
- **Banco de Dados**: PostgreSQL (Supabase)
- **ORM**: Prisma
- **Autenticação**: Supabase Auth
- **Deployment**: Vercel (recomendado)

---

## 🗄️ Estrutura do Banco de Dados

### 📊 Tabelas Principais

#### 1. **usuarios** (Usuários do Sistema)
```sql
CREATE TABLE usuarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    nome VARCHAR(255) NOT NULL,
    nivel_acesso_id UUID REFERENCES niveis_acesso(id),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Gerenciamento completo de usuários
- Sistema de níveis de acesso
- Autenticação via Supabase Auth
- Avatar personalizado
- Auditoria com timestamps

#### 2. **niveis_acesso** (Níveis de Permissão)
```sql
CREATE TABLE niveis_acesso (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    permissoes JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Sistema flexível de permissões
- Permissões armazenadas em JSON
- Níveis: Admin, Gerente, Operador, Visualizador
- Controle granular de acesso

#### 3. **secretarias** (Departamentos/Secretarias)
```sql
CREATE TABLE secretarias (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    valores_total DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Organização por departamentos
- Controle de valores totais
- Slug único para URLs amigáveis
- Relacionamento com usuários e borderos

#### 4. **tipos** (Tipos de Bordero)
```sql
CREATE TABLE tipos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Categorização de borderos
- Descrição detalhada
- CRUD completo
- Relacionamento com borderos

#### 5. **direcionamentos** (Direcionamentos de Valor)
```sql
CREATE TABLE direcionamentos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    valor_minimo DECIMAL(15,2) NOT NULL,
    valor_maximo DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Definição de faixas de valores
- Associação com usuários responsáveis
- Automação de direcionamento
- Multi-seleção de usuários

#### 6. **borderos** (Documento Principal)
```sql
CREATE TABLE borderos (
    id SERIAL PRIMARY KEY,
    bordero_cod VARCHAR(255) UNIQUE NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    data TIMESTAMP WITH TIME ZONE NOT NULL,
    nome_empresa VARCHAR(255) NOT NULL,
    secretaria_id INTEGER REFERENCES secretarias(id),
    tipo_id INTEGER REFERENCES tipos(id),
    observacao TEXT,
    status VARCHAR(50) DEFAULT 'novo',
    dados_status TEXT,
    data_alteracao TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responsavel_id UUID REFERENCES usuarios(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Funcionalidades:**
- Código único de bordero
- Controle de status (novo, em_andamento, concluido, cancelado)
- Observações e dados de status
- Responsável designado
- Auditoria completa

### 📋 Tabelas de Relacionamento

#### 7. **secretaria_usuarios** (Usuários por Secretaria)
```sql
CREATE TABLE secretaria_usuarios (
    id SERIAL PRIMARY KEY,
    secretaria_id INTEGER REFERENCES secretarias(id),
    usuario_id UUID REFERENCES usuarios(id),
    UNIQUE(secretaria_id, usuario_id)
);
```

#### 8. **direcionamento_usuarios** (Usuários por Direcionamento)
```sql
CREATE TABLE direcionamento_usuarios (
    id SERIAL PRIMARY KEY,
    direcionamento_id INTEGER REFERENCES direcionamentos(id),
    usuario_id UUID REFERENCES usuarios(id),
    UNIQUE(direcionamento_id, usuario_id)
);
```

#### 9. **bordero_devolucoes** (Devoluções de Bordero)
```sql
CREATE TABLE bordero_devolucoes (
    id SERIAL PRIMARY KEY,
    bordero_id INTEGER REFERENCES borderos(id),
    usuario_id UUID REFERENCES usuarios(id),
    UNIQUE(bordero_id, usuario_id)
);
```

### 🔔 Tabelas de Sistema

#### 10. **notificacoes** (Sistema de Notificações)
```sql
CREATE TABLE notificacoes (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(255) NOT NULL,
    mensagem TEXT NOT NULL,
    lida BOOLEAN DEFAULT FALSE,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 11. **log_atividades** (Log de Atividades)
```sql
CREATE TABLE log_atividades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    usuario_id UUID REFERENCES usuarios(id),
    acao VARCHAR(100) NOT NULL,
    entidade VARCHAR(100) NOT NULL,
    entidade_id UUID,
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 12. **bordero_logs** (Log Específico de Borderos)
```sql
CREATE TABLE bordero_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bordero_id UUID REFERENCES borderos(id),
    usuario_id UUID REFERENCES usuarios(id),
    acao VARCHAR(100) NOT NULL,
    detalhes JSONB,
    data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 13. **configuracoes_sistema** (Configurações Globais)
```sql
CREATE TABLE configuracoes_sistema (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
    modo_escuro BOOLEAN DEFAULT false,
    notificacoes_email BOOLEAN DEFAULT true,
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 🔧 Funcionalidades do Sistema

### 👥 Gestão de Usuários
- **Cadastro/Edição**: Formulário completo com validação
- **Níveis de Acesso**: Sistema hierárquico de permissões
- **Autenticação**: Login seguro via Supabase
- **Avatar**: Upload e gerenciamento de imagens
- **Multi-seleção**: Associação com secretarias e direcionamentos

### 📄 Gestão de Borderos
- **CRUD Completo**: Criar, visualizar, editar, excluir
- **Status Tracking**: Acompanhamento do ciclo de vida
- **Filtros Avançados**: Por data, valor, status, secretaria
- **Direcionamento Automático**: Baseado em faixas de valor
- **Histórico**: Log completo de alterações
- **Devoluções**: Sistema de devolução com rastreamento

### 🏢 Gestão de Secretarias
- **Departamentos**: Organização por secretarias
- **Valores Totais**: Controle financeiro por departamento
- **Usuários Associados**: Gestão de equipes
- **Relatórios**: Análise por secretaria

### 📊 Tipos e Direcionamentos
- **Tipos de Bordero**: Categorização flexível
- **Direcionamentos**: Automação baseada em valor
- **Configuração**: Interface amigável para setup

### 🔔 Sistema de Notificações
- **Notificações em Tempo Real**: Alertas importantes
- **Marcação de Leitura**: Controle de visualização
- **Filtros**: Por usuário e status

### 📈 Relatórios e Analytics
- **Dashboard**: Visão geral do sistema
- **Gráficos**: Análise visual de dados
- **Exportação**: Relatórios em diversos formatos
- **Filtros Personalizados**: Análise detalhada

### 🔒 Segurança e Auditoria
- **Row Level Security (RLS)**: Segurança a nível de linha
- **Log de Atividades**: Rastreamento completo
- **Controle de Acesso**: Permissões granulares
- **Backup Automático**: Via Supabase

### ⚙️ Configurações
- **Tema**: Modo claro/escuro automático
- **Notificações**: Configuração de alertas
- **Logo**: Personalização da marca
- **Setup**: Verificação e criação de tabelas

---

## 🚀 Como Executar o Sistema

### 1. **Pré-requisitos**
```bash
Node.js 18+
npm ou yarn
Conta no Supabase
```

### 2. **Instalação**
```bash
git clone [repositorio]
cd crm-bordero
npm install
```

### 3. **Configuração**
```bash
# Copiar arquivo de ambiente
cp .env.example .env.local

# Configurar variáveis
SUPABASE_URL=sua_url_supabase
SUPABASE_ANON_KEY=sua_chave_anonima
SUPABASE_SERVICE_ROLE_KEY=sua_chave_service_role
```

### 4. **Setup do Banco**
```bash
# Executar no Supabase SQL Editor
# Copiar conteúdo de: EXECUTE_AGORA_NO_SUPABASE.sql
```

### 5. **Execução**
```bash
npm run dev
# Acesse: http://localhost:3000
```

---

## 📱 Estrutura de Páginas

### 🏠 Dashboard (`/dashboard`)
- Visão geral do sistema
- Estatísticas principais
- Gráficos e métricas

### 📋 Borderos (`/dashboard/borderos`)
- Lista de borderos
- Filtros avançados
- Ações em lote

### 👥 Usuários (`/dashboard/usuarios`)
- Gestão de usuários
- Níveis de acesso
- Associações

### 🏢 Secretarias (`/dashboard/secretarias`)
- Departamentos
- Valores totais
- Equipes

### ⚙️ Configurações (`/dashboard/configuracoes`)
- Tipos de bordero
- Direcionamentos
- Configurações do sistema

### 🔧 Setup (`/dashboard/setup`)
- Verificação de tabelas
- Criação automática
- Diagnósticos

---

## 🔄 Fluxo de Trabalho

### 1. **Criação de Bordero**
1. Usuário acessa `/dashboard/borderos`
2. Clica em "Novo Bordero"
3. Preenche formulário
4. Sistema direciona automaticamente
5. Log de atividade é criado

### 2. **Processamento**
1. Bordero é direcionado ao responsável
2. Status é atualizado
3. Notificações são enviadas
4. Histórico é mantido

### 3. **Finalização**
1. Responsável processa bordero
2. Status é alterado para "concluído"
3. Valores são atualizados
4. Relatórios são gerados

---

## 🛠️ Manutenção

### 📊 Monitoramento
- Logs de atividade
- Métricas de performance
- Alertas de erro

### 🔄 Backup
- Backup automático via Supabase
- Exportação de dados
- Restore point-in-time

### 🔧 Atualizações
- Versionamento semântico
- Migrations automáticas
- Rollback seguro

---

## 🔌 APIs Principais

### 📋 Borderos API
```typescript
// GET /api/borderos - Listar borderos
// POST /api/borderos - Criar bordero
// PUT /api/borderos/[id] - Atualizar bordero
// DELETE /api/borderos/[id] - Excluir bordero
// GET /api/borderos/[id] - Obter bordero específico
```

### 👥 Usuários API
```typescript
// GET /api/usuarios - Listar usuários
// POST /api/usuarios - Criar usuário
// PUT /api/usuarios/[id] - Atualizar usuário
// DELETE /api/usuarios/[id] - Excluir usuário
```

### 🏢 Secretarias API
```typescript
// GET /api/secretarias - Listar secretarias
// POST /api/secretarias - Criar secretaria
// PUT /api/secretarias/[id] - Atualizar secretaria
// DELETE /api/secretarias/[id] - Excluir secretaria
```

### ⚙️ Setup API
```typescript
// POST /api/setup-verify-tables - Verificar tabelas
// POST /api/setup-logs-tables - Criar tabelas de log
// POST /api/setup-configuracoes-sistema - Configurar sistema
```

---

## 📊 Exemplos de Uso

### 1. **Criar um Bordero**
```javascript
const novoBordero = {
  borderoCod: "BRD-2025-001",
  valor: 15000.00,
  data: "2025-01-15T10:00:00Z",
  nomeEmpresa: "Empresa XYZ Ltda",
  secretariaId: 1,
  tipoId: 2,
  observacao: "Bordero urgente para processamento"
}

const response = await fetch('/api/borderos', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(novoBordero)
})
```

### 2. **Filtrar Borderos**
```javascript
const filtros = {
  dataInicio: "2025-01-01",
  dataFim: "2025-01-31",
  secretariaId: 1,
  status: "novo",
  valorMinimo: 1000,
  valorMaximo: 50000
}

const borderos = await fetch(`/api/borderos?${new URLSearchParams(filtros)}`)
```

### 3. **Atualizar Status**
```javascript
const atualizacao = {
  status: "em_andamento",
  dadosStatus: "Iniciado processamento",
  responsavelId: "uuid-do-usuario"
}

await fetch(`/api/borderos/${borderoId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(atualizacao)
})
```

---

## 🔐 Níveis de Acesso

### 🔴 **Admin** (Acesso Total)
- Gerenciar usuários e permissões
- Configurar sistema
- Acessar todos os borderos
- Executar setup e manutenção
- Visualizar logs completos

### 🟡 **Gerente** (Gestão Departamental)
- Gerenciar borderos da secretaria
- Visualizar relatórios departamentais
- Gerenciar equipe da secretaria
- Aprovar/rejeitar borderos

### 🟢 **Operador** (Operações)
- Criar e editar borderos
- Processar borderos direcionados
- Visualizar borderos próprios
- Atualizar status

### 🔵 **Visualizador** (Somente Leitura)
- Visualizar borderos
- Gerar relatórios básicos
- Consultar histórico
- Sem permissões de edição

---

## 🚨 Troubleshooting

### ❌ **Problemas Comuns**

#### 1. **Tabelas não encontradas**
```bash
# Solução: Executar setup
1. Acesse /dashboard/setup
2. Clique em "Verificar"
3. Execute SQL fornecido no Supabase
```

#### 2. **Erro de permissão**
```bash
# Verificar RLS policies no Supabase
# Executar: EXECUTE_AGORA_NO_SUPABASE.sql
```

#### 3. **Bordero não direciona**
```bash
# Verificar direcionamentos configurados
# Verificar faixas de valor
# Verificar usuários associados
```

#### 4. **Notificações não aparecem**
```bash
# Verificar tabela notificacoes
# Verificar permissões do usuário
# Verificar conexão WebSocket
```

### 🔧 **Comandos de Diagnóstico**

```sql
-- Verificar estrutura das tabelas
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public';

-- Verificar registros de borderos
SELECT COUNT(*) as total_borderos FROM borderos;

-- Verificar usuários ativos
SELECT COUNT(*) as usuarios_ativos FROM usuarios;

-- Verificar logs recentes
SELECT * FROM log_atividades
ORDER BY created_at DESC LIMIT 10;
```

---

## 📈 Métricas e KPIs

### 📊 **Dashboard Principal**
- Total de borderos por período
- Valor total processado
- Borderos por status
- Performance por secretaria
- Usuários mais ativos

### 📋 **Relatórios Disponíveis**
- Borderos por secretaria
- Borderos por tipo
- Performance de usuários
- Análise temporal
- Borderos em atraso

### 🎯 **KPIs Importantes**
- Tempo médio de processamento
- Taxa de conclusão
- Valor médio por bordero
- Borderos por usuário/dia
- Taxa de devolução

---

## 🔄 Roadmap Futuro

### 🚀 **Próximas Funcionalidades**
- [ ] API REST completa
- [ ] App mobile (React Native)
- [ ] Integração com sistemas externos
- [ ] Relatórios avançados (PDF/Excel)
- [ ] Workflow customizável
- [ ] Assinatura digital
- [ ] Chat interno
- [ ] Calendário de vencimentos

### 🛠️ **Melhorias Técnicas**
- [ ] Cache Redis
- [ ] Queue system (Bull/Bee)
- [ ] Microservices
- [ ] Docker containers
- [ ] CI/CD pipeline
- [ ] Monitoring (Sentry)
- [ ] Performance optimization
- [ ] Load balancing

---

## 📞 Suporte

### 🆘 **Canais de Suporte**
- **Documentação**: Este arquivo
- **Diagnósticos**: `/dashboard/setup`
- **Logs**: Tabela `log_atividades`
- **Email**: <EMAIL>
- **GitHub**: [repositório do projeto]

### 🕐 **Horário de Atendimento**
- Segunda a Sexta: 8h às 18h
- Emergências: 24/7 via email
- Resposta em até 4 horas úteis

---

**Versão**: 1.0.0
**Última Atualização**: Janeiro 2025
**Desenvolvido por**: Equipe CRM Bordero
**Licença**: MIT
