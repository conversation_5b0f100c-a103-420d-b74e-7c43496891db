"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Clock, User, FileText, CheckCircle, AlertCircle, Archive, Edit, Trash2 } from "lucide-react"

interface LogEntry {
  id: string
  acao: string
  detalhes: any // Pode ser string ou objeto
  data_hora: string
  usuario: {
    nome: string
    email: string
  } | null
}

interface BorderoTimelineProps {
  borderoId: string
  refreshTrigger?: number // Para forçar atualização
}

const actionIcons: { [key: string]: any } = {
  criar: FileText,
  editar: Edit,
  "alterar_status": CheckCircle,
  atualizar: Edit,
  cancelar: Trash2,
  excluir: Trash2,
  arquivar: Archive,
  visualizar: Clock,
  comentario: User,
}

const actionColors: { [key: string]: string } = {
  criar: "bg-blue-100 text-blue-600 border-blue-200",
  editar: "bg-yellow-100 text-yellow-600 border-yellow-200",
  "alterar_status": "bg-green-100 text-green-600 border-green-200",
  atualizar: "bg-yellow-100 text-yellow-600 border-yellow-200",
  cancelar: "bg-red-100 text-red-600 border-red-200",
  excluir: "bg-red-100 text-red-600 border-red-200",
  arquivar: "bg-gray-100 text-gray-600 border-gray-200",
  visualizar: "bg-purple-100 text-purple-600 border-purple-200",
  comentario: "bg-indigo-100 text-indigo-600 border-indigo-200",
}

export function BorderoTimeline({ borderoId, refreshTrigger }: BorderoTimelineProps) {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  const renderDetalhes = (detalhes: any): string => {
    if (typeof detalhes === 'string') {
      return detalhes
    }

    if (typeof detalhes === 'object' && detalhes !== null) {
      // Casos específicos para diferentes tipos de detalhes
      if (detalhes.comentario) {
        return `Comentário adicionado: "${detalhes.comentario.substring(0, 100)}${detalhes.comentario.length > 100 ? '...' : ''}"`
      }

      if (detalhes.alteracoes && Array.isArray(detalhes.alteracoes)) {
        return detalhes.alteracoes.join(', ')
      }

      if (detalhes.status_anterior && detalhes.status_novo) {
        return `Status alterado de "${detalhes.status_anterior}" para "${detalhes.status_novo}"`
      }

      if (detalhes.bordero_cod) {
        return `Bordero ${detalhes.bordero_cod} foi processado`
      }

      if (detalhes.message) {
        return detalhes.message
      }

      // Fallback para outros objetos
      const keys = Object.keys(detalhes)
      if (keys.length === 1) {
        return `${keys[0]}: ${detalhes[keys[0]]}`
      }

      return keys.map(key => `${key}: ${detalhes[key]}`).join(', ')
    }

    return 'Ação realizada'
  }

  useEffect(() => {
    setMounted(true)
    fetchLogs()
  }, [borderoId, refreshTrigger])

  const fetchLogs = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/borderos/${borderoId}/logs`)
      if (!response.ok) throw new Error("Erro ao buscar logs")

      const data = await response.json()
      setLogs(data)
    } catch (error) {
      console.error("Erro ao buscar logs:", error)

      // Se falhar, buscar dados do bordero para criar logs básicos
      try {
        const borderoResponse = await fetch(`/api/borderos/${borderoId}`)
        if (borderoResponse.ok) {
          const borderoData = await borderoResponse.json()

          // Criar logs básicos baseados nos dados do bordero
          const basicLogs = [
            {
              id: "basic_created",
              acao: "criar",
              detalhes: "Bordero criado no sistema",
              data_hora: borderoData.created_at,
              usuario: {
                nome: borderoData.responsavel?.nome || "Sistema",
                email: borderoData.responsavel?.email || "<EMAIL>"
              }
            }
          ]

          // Adicionar log de atualização se foi modificado
          if (borderoData.updated_at !== borderoData.created_at) {
            basicLogs.unshift({
              id: "basic_updated",
              acao: "editar",
              detalhes: "Bordero foi atualizado",
              data_hora: borderoData.updated_at,
              usuario: {
                nome: borderoData.responsavel?.nome || "Sistema",
                email: borderoData.responsavel?.email || "<EMAIL>"
              }
            })
          }

          setLogs(basicLogs)
        }
      } catch (borderoError) {
        console.error("Erro ao buscar dados do bordero:", borderoError)
      }
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateString: string) => {
    if (!mounted) return dateString

    try {
      return new Date(dateString).toLocaleString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      })
    } catch (error) {
      return dateString
    }
  }

  const getActionLabel = (acao: string) => {
    const labels: { [key: string]: string } = {
      criar: "Criado",
      editar: "Editado",
      atualizar: "Atualizado",
      "alterar_status": "Status Alterado",
      cancelar: "Cancelado",
      excluir: "Excluído",
      arquivar: "Arquivado",
      visualizar: "Visualizado",
      comentario: "Comentário",
    }
    return labels[acao] || acao
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Timeline do Bordero</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Timeline do Bordero</CardTitle>
      </CardHeader>
      <CardContent>
        {logs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            Nenhum log encontrado para este bordero.
          </div>
        ) : (
          <div className="space-y-4">
            {logs.map((log, index) => {
              const Icon = actionIcons[log.acao] || Clock
              const isLast = index === logs.length - 1

              return (
                <div key={log.id} className="relative">
                  {/* Linha conectora */}
                  {!isLast && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-border"></div>
                  )}

                  <div className="flex items-start gap-4">
                    {/* Ícone da ação */}
                    <div className={`flex-shrink-0 w-12 h-12 rounded-full border-2 flex items-center justify-center ${actionColors[log.acao] || "bg-gray-100 text-gray-600 border-gray-200"}`}>
                      <Icon className="h-5 w-5" />
                    </div>

                    {/* Conteúdo */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {getActionLabel(log.acao)}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {formatDateTime(log.data_hora)}
                        </span>
                      </div>

                      <p className="text-sm text-foreground mb-2">
                        {renderDetalhes(log.detalhes)}
                      </p>

                      {log.usuario && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <User className="h-3 w-3" />
                          <span>{log.usuario.nome}</span>
                          <span>({log.usuario.email})</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
