import { createClient } from "@supabase/supabase-js"
import type { LogAcao, LogEntidade, LogDetalhes } from "@/types/logs"

// Função para registrar logs no servidor (APIs)
export async function registrarLogServidor(
  usuarioId: string,
  acao: LogAcao,
  entidade: LogEntidade,
  entidadeId?: string | number,
  detalhes?: LogDetalhes,
  request?: Request,
) {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Obter informações do request se disponível
    let ip = undefined
    let userAgent = undefined

    if (request) {
      // Tentar obter o IP real do cliente
      ip = request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido"
      userAgent = request.headers.get("user-agent") || "desconhecido"
    }

    // Registrar o log
    const { error } = await supabase.from("log_atividades").insert({
      usuario_id: usuarioId,
      acao,
      entidade,
      entidade_id: entidadeId?.toString(),
      detalhes: detalhes || {},
      ip,
      user_agent: userAgent,
    })

    if (error) {
      console.error("Erro ao registrar log:", error)
    }
  } catch (error) {
    console.error("Erro ao registrar log:", error)
  }
}

// Função para registrar logs no cliente (via API)
export async function registrarLogCliente(
  acao: LogAcao,
  entidade: LogEntidade,
  entidadeId?: string | number,
  detalhes?: LogDetalhes,
) {
  try {
    await fetch("/api/logs", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        acao,
        entidade,
        entidade_id: entidadeId?.toString(),
        detalhes: detalhes || {},
      }),
    })
  } catch (error) {
    console.error("Erro ao registrar log:", error)
  }
}

// Manter compatibilidade com código existente
export const registrarLog = registrarLogServidor
