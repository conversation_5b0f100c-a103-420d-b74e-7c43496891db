"use client"

import * as React from "react"
import { <PERSON>, <PERSON>O<PERSON>, Check, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface PasswordCriteria {
  minLength: boolean
  hasUppercase: boolean
  hasLowercase: boolean
  hasNumber: boolean
  hasSpecialChar: boolean
}

interface EnhancedPasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showStrength?: boolean
  showCriteria?: boolean
  confirmPassword?: string
  onStrengthChange?: (strength: "weak" | "medium" | "strong") => void
}

export const EnhancedPasswordInput = React.forwardRef<HTMLInputElement, EnhancedPasswordInputProps>(
  ({ className, showStrength = false, showCriteria = false, confirmPassword, onStrengthChange, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [criteria, setCriteria] = React.useState<PasswordCriteria>({
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecialChar: false
    })

    const password = props.value as string || ""

    React.useEffect(() => {
      const newCriteria = {
        minLength: password.length >= 8,
        hasUppercase: /[A-Z]/.test(password),
        hasLowercase: /[a-z]/.test(password),
        hasNumber: /\d/.test(password),
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password)
      }
      setCriteria(newCriteria)

      // Calcular força da senha
      const metCriteria = Object.values(newCriteria).filter(Boolean).length
      let strength: "weak" | "medium" | "strong" = "weak"
      
      if (metCriteria >= 4) strength = "strong"
      else if (metCriteria >= 2) strength = "medium"
      
      onStrengthChange?.(strength)
    }, [password, onStrengthChange])

    const getStrengthColor = () => {
      const metCriteria = Object.values(criteria).filter(Boolean).length
      if (metCriteria >= 4) return "text-green-600"
      if (metCriteria >= 2) return "text-yellow-600"
      return "text-red-600"
    }

    const getStrengthText = () => {
      const metCriteria = Object.values(criteria).filter(Boolean).length
      if (metCriteria >= 4) return "Forte"
      if (metCriteria >= 2) return "Média"
      return "Fraca"
    }

    const passwordsMatch = confirmPassword !== undefined && password === confirmPassword && password.length > 0

    return (
      <div className="space-y-2">
        <div className="relative">
          <Input
            {...props}
            ref={ref}
            type={showPassword ? "text" : "password"}
            className={cn("pr-10", className)}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Indicador de força da senha */}
        {showStrength && password.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Força:</span>
            <span className={cn("text-sm font-medium", getStrengthColor())}>
              {getStrengthText()}
            </span>
          </div>
        )}

        {/* Indicador de confirmação de senha */}
        {confirmPassword !== undefined && password.length > 0 && confirmPassword.length > 0 && (
          <div className="flex items-center gap-2">
            {passwordsMatch ? (
              <>
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600">Senhas coincidem</span>
              </>
            ) : (
              <>
                <X className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600">Senhas não coincidem</span>
              </>
            )}
          </div>
        )}

        {/* Critérios da senha */}
        {showCriteria && password.length > 0 && (
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Critérios da senha:</p>
            <div className="grid grid-cols-1 gap-1 text-xs">
              <div className={cn("flex items-center gap-2", criteria.minLength ? "text-green-600" : "text-red-600")}>
                {criteria.minLength ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                <span>Mínimo 8 caracteres</span>
              </div>
              <div className={cn("flex items-center gap-2", criteria.hasUppercase ? "text-green-600" : "text-red-600")}>
                {criteria.hasUppercase ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                <span>Pelo menos uma letra maiúscula</span>
              </div>
              <div className={cn("flex items-center gap-2", criteria.hasLowercase ? "text-green-600" : "text-red-600")}>
                {criteria.hasLowercase ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                <span>Pelo menos uma letra minúscula</span>
              </div>
              <div className={cn("flex items-center gap-2", criteria.hasNumber ? "text-green-600" : "text-red-600")}>
                {criteria.hasNumber ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                <span>Pelo menos um número</span>
              </div>
              <div className={cn("flex items-center gap-2", criteria.hasSpecialChar ? "text-green-600" : "text-red-600")}>
                {criteria.hasSpecialChar ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                <span>Pelo menos um caractere especial</span>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }
)

EnhancedPasswordInput.displayName = "EnhancedPasswordInput"
