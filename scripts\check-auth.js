const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function checkAuth() {
  try {
    console.log('🔍 Verificando autenticação...\n')

    // 1. Listar todos os usuários no Auth
    console.log('1. Usuários no Supabase Auth:')
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.error('❌ Erro ao listar usuários do Auth:', authError.message)
      return
    }

    console.log(`   Total de usuários: ${authUsers.users.length}`)
    authUsers.users.forEach(user => {
      console.log(`   - ${user.email} (ID: ${user.id})`)
      console.log(`     Confirmado: ${user.email_confirmed_at ? 'Sim' : 'Não'}`)
      console.log(`     Criado em: ${new Date(user.created_at).toLocaleString('pt-BR')}`)
    })

    // 2. Verificar se o admin existe
    const adminUser = authUsers.users.find(u => u.email === '<EMAIL>')
    
    if (!adminUser) {
      console.log('\n❌ Usuário admin não encontrado no Auth!')
      console.log('🔧 Criando usuário admin...')
      
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'Admin@123',
        email_confirm: true
      })

      if (createError) {
        console.error('❌ Erro ao criar usuário:', createError.message)
        return
      }

      console.log('✅ Usuário admin criado com sucesso!')
      console.log(`   ID: ${newUser.user.id}`)
    } else {
      console.log('\n✅ Usuário admin encontrado no Auth')
      console.log(`   ID: ${adminUser.id}`)
      console.log(`   Email confirmado: ${adminUser.email_confirmed_at ? 'Sim' : 'Não'}`)
    }

    // 3. Verificar usuário na tabela usuarios
    console.log('\n2. Verificando tabela usuarios:')
    const { data: dbUsers, error: dbError } = await supabase
      .from('usuarios')
      .select('*, niveis_acesso(nome)')
      .eq('email', '<EMAIL>')

    if (dbError) {
      console.error('❌ Erro ao buscar na tabela usuarios:', dbError.message)
      return
    }

    if (dbUsers.length === 0) {
      console.log('❌ Usuário admin não encontrado na tabela usuarios!')
    } else {
      console.log('✅ Usuário admin encontrado na tabela usuarios')
      const dbUser = dbUsers[0]
      console.log(`   Nome: ${dbUser.nome}`)
      console.log(`   Email: ${dbUser.email}`)
      console.log(`   Nível: ${dbUser.niveis_acesso?.nome}`)
    }

    // 4. Testar login
    console.log('\n3. Testando login:')
    try {
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'Admin@123'
      })

      if (loginError) {
        console.error('❌ Erro no login:', loginError.message)
        
        // Se o erro for de email não confirmado, vamos confirmar
        if (loginError.message.includes('Email not confirmed')) {
          console.log('🔧 Confirmando email do usuário...')
          
          const { error: confirmError } = await supabase.auth.admin.updateUserById(
            adminUser.id,
            { email_confirm: true }
          )

          if (confirmError) {
            console.error('❌ Erro ao confirmar email:', confirmError.message)
          } else {
            console.log('✅ Email confirmado com sucesso!')
          }
        }
      } else {
        console.log('✅ Login realizado com sucesso!')
        console.log(`   User ID: ${loginData.user?.id}`)
        
        // Fazer logout para limpar a sessão
        await supabase.auth.signOut()
      }
    } catch (error) {
      console.error('❌ Erro inesperado no teste de login:', error.message)
    }

    console.log('\n🎉 Verificação concluída!')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

checkAuth()
