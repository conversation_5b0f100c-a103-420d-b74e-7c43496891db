/**
 * Utilitários para formatação de moeda
 */

/**
 * Formatar valor para moeda brasileira (R$)
 */
export function formatCurrency(value: number | string): string {
  const numericValue = typeof value === 'string' ? parseFloat(value) : value
  
  if (isNaN(numericValue)) {
    return 'R$ 0,00'
  }

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numericValue)
}

/**
 * Formatar valor para moeda brasileira (alias para compatibilidade)
 */
export const formatReal = formatCurrency

/**
 * Converter string de moeda para número
 */
export function parseCurrency(value: string): number {
  if (!value) return 0
  
  // Remove tudo exceto números, vírgula e ponto
  const cleanValue = value
    .replace(/[^\d,.-]/g, '') // Remove tudo exceto dígitos, vírgula, ponto e hífen
    .replace(/\./g, '') // Remove pontos (separadores de milhares)
    .replace(',', '.') // Converte vírgula para ponto decimal
  
  return parseFloat(cleanValue) || 0
}

/**
 * Aplicar máscara de moeda em tempo real
 */
export function applyCurrencyMask(value: string): string {
  // Remove tudo exceto números
  const numbers = value.replace(/\D/g, '')
  
  if (!numbers) return ''
  
  // Converte para centavos
  const cents = parseInt(numbers)
  const reais = cents / 100
  
  // Formata como moeda
  return formatCurrency(reais)
}

/**
 * Remover máscara de moeda e retornar valor numérico
 */
export function removeCurrencyMask(value: string): number {
  return parseCurrency(value)
}

/**
 * Validar se o valor é uma moeda válida
 */
export function isValidCurrency(value: string): boolean {
  const numericValue = parseCurrency(value)
  return !isNaN(numericValue) && numericValue >= 0
}
