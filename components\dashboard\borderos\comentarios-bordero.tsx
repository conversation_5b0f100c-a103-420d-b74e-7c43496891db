"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import {
  MessageCircle,
  Send,
  Loader2
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { CommentCard } from "@/components/ui/comment-card"
import { EmojiPicker } from "@/components/ui/emoji-picker"
import { useSupabase } from "@/lib/supabase-provider"

interface Comentario {
  id: string
  comentario: string
  mencoes: string[]
  created_at: string
  updated_at?: string
  usuario: {
    id: string
    nome: string
    email: string
  }
}

interface ComentariosBorderoProps {
  borderoId: string
  borderoCode: string
}

export function ComentariosBordero({ borderoId, borderoCode }: ComentariosBorderoProps) {
  const { user } = useSupabase()
  const [comentarios, setComentarios] = useState<Comentario[]>([])
  const [loading, setLoading] = useState(true)
  const [novoComentario, setNovoComentario] = useState("")
  const [enviandoComentario, setEnviandoComentario] = useState(false)

  useEffect(() => {
    fetchComentarios()
  }, [borderoId])

  const fetchComentarios = async () => {
    try {
      setLoading(true)

      const response = await fetch(`/api/borderos/${borderoId}/comentarios`)

      if (response.ok) {
        const data = await response.json()
        setComentarios(data)
      } else {
        console.error("Erro ao carregar comentários")
      }
    } catch (error) {
      console.error("Erro ao carregar comentários:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os comentários.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEnviarComentario = async () => {
    if (!novoComentario.trim()) return

    setEnviandoComentario(true)

    try {
      // Detectar menções @usuario no comentário
      const mencoes = detectarMencoes(novoComentario)

      const response = await fetch(`/api/borderos/${borderoId}/comentarios`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          comentario: novoComentario,
          mencoes
        })
      })

      if (!response.ok) {
        throw new Error('Erro ao enviar comentário')
      }

      setNovoComentario("")
      await fetchComentarios() // Recarregar comentários

      toast({
        title: "Sucesso",
        description: "Comentário adicionado com sucesso!"
      })
    } catch (error) {
      console.error("Erro ao enviar comentário:", error)
      toast({
        title: "Erro",
        description: "Não foi possível enviar o comentário.",
        variant: "destructive"
      })
    } finally {
      setEnviandoComentario(false)
    }
  }

  const detectarMencoes = (texto: string): string[] => {
    // Regex simples para detectar @usuario
    const regex = /@(\w+)/g
    const mencoes = []
    let match

    while ((match = regex.exec(texto)) !== null) {
      mencoes.push(match[1])
    }

    return mencoes
  }

  const handleEditarComentario = async (comentarioId: string, novoTexto: string) => {
    try {
      const response = await fetch(`/api/borderos/${borderoId}/comentarios/${comentarioId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          comentario: novoTexto
        })
      })

      if (!response.ok) {
        throw new Error('Erro ao editar comentário')
      }

      await fetchComentarios() // Recarregar comentários

      toast({
        title: "Sucesso",
        description: "Comentário editado com sucesso!"
      })
    } catch (error) {
      console.error("Erro ao editar comentário:", error)
      toast({
        title: "Erro",
        description: "Não foi possível editar o comentário.",
        variant: "destructive"
      })
      throw error
    }
  }

  const handleExcluirComentario = async (comentarioId: string) => {
    try {
      const response = await fetch(`/api/borderos/${borderoId}/comentarios/${comentarioId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Erro ao excluir comentário')
      }

      await fetchComentarios() // Recarregar comentários

      toast({
        title: "Sucesso",
        description: "Comentário excluído com sucesso!"
      })
    } catch (error) {
      console.error("Erro ao excluir comentário:", error)
      toast({
        title: "Erro",
        description: "Não foi possível excluir o comentário.",
        variant: "destructive"
      })
      throw error
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    setNovoComentario(prev => prev + emoji)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Comentários
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Comentários ({comentarios.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Formulário para novo comentário */}
        <div className="space-y-3">
          <h4 className="font-medium">Adicionar Comentário</h4>
          <div className="relative">
            <Textarea
              placeholder="Digite seu comentário... (use @usuario para mencionar alguém)"
              value={novoComentario}
              onChange={(e) => setNovoComentario(e.target.value)}
              className="min-h-[80px] pr-10"
            />
            <div className="absolute bottom-2 right-2">
              <EmojiPicker onEmojiSelect={handleEmojiSelect}>
                <Button variant="ghost" size="sm" type="button" className="h-6 w-6 p-0">
                  <span className="text-sm">😀</span>
                </Button>
              </EmojiPicker>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-500">
              Dica: Use @usuario para mencionar alguém ou clique em 😀 para adicionar emojis
            </p>
            <Button
              onClick={handleEnviarComentario}
              disabled={!novoComentario.trim() || enviandoComentario}
              size="sm"
            >
              {enviandoComentario ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Enviar
                </>
              )}
            </Button>
          </div>
        </div>

        <Separator />

        {/* Lista de comentários */}
        <div className="space-y-4">
          {comentarios.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum comentário ainda.</p>
              <p className="text-sm">Seja o primeiro a comentar!</p>
            </div>
          ) : (
            comentarios.map((comentario) => (
              <CommentCard
                key={comentario.id}
                comentario={comentario}
                currentUserId={user?.id || ''}
                onEdit={handleEditarComentario}
                onDelete={handleExcluirComentario}
              />
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
