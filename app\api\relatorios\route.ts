import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request) {
  try {
    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usu<PERSON>rio não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const url = new URL(request.url)
    const searchParams = url.searchParams

    // Parâmetros de filtro
    const dateFrom = searchParams.get("dateFrom")
    const dateTo = searchParams.get("dateTo")
    const secretarias = searchParams.get("secretarias")?.split(",").filter(Boolean) || []
    const tipos = searchParams.get("tipos")?.split(",").filter(Boolean) || []
    const valorMin = searchParams.get("valorMin")
    const valorMax = searchParams.get("valorMax")
    const status = searchParams.get("status")?.split(",").filter(Boolean) || []
    const search = searchParams.get("search")

    // Construir query base
    let query = supabase
      .from("borderos")
      .select(`
        id,
        bordero_cod,
        nome_empresa,
        valor,
        data,
        status,
        secretaria:secretarias(id, nome),
        tipo:tipos(id, nome)
      `)

    // Aplicar filtros
    if (dateFrom && dateTo) {
      const fromDate = new Date(dateFrom)
      fromDate.setHours(0, 0, 0, 0)

      const toDate = new Date(dateTo)
      toDate.setHours(23, 59, 59, 999)

      query = query.gte("data", fromDate.toISOString()).lte("data", toDate.toISOString())
    }

    if (secretarias.length > 0) {
      query = query.in("secretaria_id", secretarias)
    }

    if (tipos.length > 0) {
      query = query.in("tipo_id", tipos)
    }

    if (valorMin) {
      query = query.gte("valor", parseFloat(valorMin))
    }

    if (valorMax) {
      query = query.lte("valor", parseFloat(valorMax))
    }

    if (status.length > 0) {
      query = query.in("status", status)
    }

    if (search) {
      query = query.or(`bordero_cod.ilike.%${search}%,nome_empresa.ilike.%${search}%`)
    }

    // Executar query
    const { data: borderos, error } = await query

    if (error) {
      console.error("Erro ao buscar borderos:", error)
      return NextResponse.json({ error: "Erro ao buscar dados" }, { status: 500 })
    }

    // Processar dados para relatórios
    const processedData = {
      borderos: borderos || [],
      chartData: processChartData(borderos || []),
      resumoSecretarias: processResumoSecretarias(borderos || []),
      borderosPorTipo: processBorderosPorTipo(borderos || [])
    }

    return NextResponse.json(processedData)
  } catch (error) {
    console.error("Erro na API de relatórios:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

// Função para processar dados do gráfico
function processChartData(borderos: any[]) {
  const secretariasMap = new Map<string, number>()

  borderos.forEach((bordero) => {
    const secretariaNome = bordero.secretaria?.nome || "Sem secretaria"
    const valorAtual = secretariasMap.get(secretariaNome) || 0
    secretariasMap.set(secretariaNome, valorAtual + bordero.valor)
  })

  return Array.from(secretariasMap.entries()).map(([secretaria, valor]) => ({
    secretaria,
    valor,
  }))
}

// Função para processar resumo por secretarias
function processResumoSecretarias(borderos: any[]) {
  const secretariasResumoMap = new Map<string, { valorTotal: number; borderos: number }>()

  borderos.forEach((bordero) => {
    const secretariaNome = bordero.secretaria?.nome || "Sem secretaria"
    const dadosAtuais = secretariasResumoMap.get(secretariaNome) || { valorTotal: 0, borderos: 0 }
    secretariasResumoMap.set(secretariaNome, {
      valorTotal: dadosAtuais.valorTotal + bordero.valor,
      borderos: dadosAtuais.borderos + 1,
    })
  })

  return Array.from(secretariasResumoMap.entries()).map(([secretaria, dados]) => {
    const mediaPorBordero = dados.borderos > 0 ? dados.valorTotal / dados.borderos : 0

    return {
      Secretaria: secretaria,
      ValorTotal: new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(dados.valorTotal),
      Borderos: dados.borderos,
      MediaPorBordero: new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(mediaPorBordero),
    }
  })
}

// Função para processar borderos por tipo
function processBorderosPorTipo(borderos: any[]) {
  const tiposMap = new Map<string, { quantidade: number; valorTotal: number }>()

  borderos.forEach((bordero) => {
    const tipoNome = bordero.tipo?.nome || "Sem tipo"
    const dadosAtuais = tiposMap.get(tipoNome) || { quantidade: 0, valorTotal: 0 }
    tiposMap.set(tipoNome, {
      quantidade: dadosAtuais.quantidade + 1,
      valorTotal: dadosAtuais.valorTotal + bordero.valor,
    })
  })

  return Array.from(tiposMap.entries()).map(([tipo, dados]) => ({
    tipo,
    quantidade: dados.quantidade,
    valorTotal: new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(dados.valorTotal),
  }))
}
