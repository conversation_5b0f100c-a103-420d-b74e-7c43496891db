import { formatDistanceToNow, format } from "date-fns"
import { ptBR } from "date-fns/locale"

/**
 * Formata uma data de forma segura, tratando valores inválidos
 */
export function safeFormatDate(dateValue: string | Date | null | undefined, formatStr: string = "dd/MM/yyyy"): string {
  if (!dateValue) {
    return 'Data não disponível'
  }

  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue
    
    if (isNaN(date.getTime())) {
      return 'Data inválida'
    }

    return format(date, formatStr, { locale: ptBR })
  } catch (error) {
    console.error('Erro ao formatar data:', error, dateValue)
    return 'Data inválida'
  }
}

/**
 * Formata uma data e hora de forma segura
 */
export function safeFormatDateTime(dateValue: string | Date | null | undefined): string {
  return safeFormatDate(dateValue, "dd/MM/yyyy HH:mm")
}

/**
 * Formata distância de tempo de forma segura (ex: "há 2 horas")
 */
export function safeFormatDistanceToNow(dateValue: string | Date | null | undefined): string {
  if (!dateValue) {
    return 'Data não disponível'
  }

  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue
    
    if (isNaN(date.getTime())) {
      return 'Data inválida'
    }

    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: ptBR
    })
  } catch (error) {
    console.error('Erro ao formatar distância de tempo:', error, dateValue)
    return 'Data inválida'
  }
}

/**
 * Converte uma data para string ISO de forma segura
 */
export function safeToISOString(dateValue: string | Date | null | undefined): string | null {
  if (!dateValue) {
    return null
  }

  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue
    
    if (isNaN(date.getTime())) {
      return null
    }

    return date.toISOString()
  } catch (error) {
    console.error('Erro ao converter para ISO:', error, dateValue)
    return null
  }
}

/**
 * Verifica se uma data é válida
 */
export function isValidDate(dateValue: string | Date | null | undefined): boolean {
  if (!dateValue) {
    return false
  }

  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue
    return !isNaN(date.getTime())
  } catch (error) {
    return false
  }
}

/**
 * Formata data para input HTML (YYYY-MM-DD)
 */
export function formatForInput(dateValue: string | Date | null | undefined): string {
  if (!dateValue) {
    return ''
  }

  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue
    
    if (isNaN(date.getTime())) {
      return ''
    }

    return format(date, "yyyy-MM-dd")
  } catch (error) {
    console.error('Erro ao formatar para input:', error, dateValue)
    return ''
  }
}
