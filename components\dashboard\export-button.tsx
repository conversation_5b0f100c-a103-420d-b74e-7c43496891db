"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Download } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ExportButtonProps {
  data: any[]
  detailedData?: any[]
  filename: string
  summaryData?: {
    title: string
    data: any[]
  }[]
}

export function ExportButton({ data, detailedData, filename, summaryData }: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({})

  const handleSelectItem = (id: string) => {
    setSelectedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }))
  }

  const handleSelectAll = () => {
    const allSelected = detailedData?.every((item) => selectedItems[item.id])

    if (allSelected) {
      // Desmarcar todos
      setSelectedItems({})
    } else {
      // Marcar todos
      const newSelected: Record<string, boolean> = {}
      detailedData?.forEach((item) => {
        newSelected[item.id] = true
      })
      setSelectedItems(newSelected)
    }
  }

  const exportToExcel = async () => {
    try {
      setIsExporting(true)

      // Importação dinâmica para reduzir o tamanho do bundle
      const XLSX = await import("xlsx")

      // Filtrar dados detalhados se existirem e houver seleção
      const filteredDetailedData = detailedData?.filter((item) => selectedItems[item.id]) || []

      // Criar uma planilha para os dados principais
      const worksheet = XLSX.utils.json_to_sheet(data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, "Resumo")

      // Adicionar planilhas para dados de resumo se existirem
      if (summaryData) {
        summaryData.forEach((summary) => {
          const summarySheet = XLSX.utils.json_to_sheet(summary.data)
          XLSX.utils.book_append_sheet(workbook, summarySheet, summary.title)
        })
      }

      // Adicionar planilha para dados detalhados se existirem
      if (filteredDetailedData.length > 0) {
        const detailedSheet = XLSX.utils.json_to_sheet(filteredDetailedData)
        XLSX.utils.book_append_sheet(workbook, detailedSheet, "Detalhes")
      }

      // Gerar o arquivo e fazer o download
      XLSX.writeFile(workbook, `${filename}.xlsx`)

      toast({
        title: "Exportação concluída",
        description: "Os dados foram exportados com sucesso para Excel.",
      })

      setDialogOpen(false)
    } catch (error) {
      console.error("Erro ao exportar para Excel:", error)
      toast({
        title: "Erro na exportação",
        description: "Não foi possível exportar os dados para Excel.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const exportToPDF = async () => {
    try {
      setIsExporting(true)

      // Importação dinâmica para reduzir o tamanho do bundle
      const { jsPDF } = await import("jspdf")
      const { autoTable } = await import("jspdf-autotable")

      // Filtrar dados detalhados se existirem e houver seleção
      const filteredDetailedData = detailedData?.filter((item) => selectedItems[item.id]) || []

      // Criar um novo documento PDF
      const doc = new jsPDF()

      // Adicionar título principal
      doc.setFontSize(18)
      doc.text(`Relatório: ${filename}`, 14, 15)
      doc.setFontSize(12)
      doc.text(`Data: ${new Date().toLocaleDateString("pt-BR")}`, 14, 22)

      let yPos = 30

      // Adicionar dados de resumo
      doc.setFontSize(14)
      doc.text("Resumo Geral", 14, yPos)
      yPos += 8

      // Extrair cabeçalhos e dados para a tabela principal
      const headers = Object.keys(data[0] || {})
      const rows = data.map((item) => Object.values(item))

      // Adicionar tabela principal
      autoTable(doc, {
        head: [headers],
        body: rows,
        startY: yPos,
        theme: "grid",
        styles: { fontSize: 8, cellPadding: 2 },
        headStyles: { fillColor: [59, 130, 246] },
      })

      yPos = (doc as any).lastAutoTable.finalY + 10

      // Adicionar dados de resumo adicionais se existirem
      if (summaryData) {
        for (const summary of summaryData) {
          // Verificar se precisa adicionar uma nova página
          if (yPos > 250) {
            doc.addPage()
            yPos = 20
          }

          doc.setFontSize(14)
          doc.text(summary.title, 14, yPos)
          yPos += 8

          const summaryHeaders = Object.keys(summary.data[0] || {})
          const summaryRows = summary.data.map((item) => Object.values(item))

          autoTable(doc, {
            head: [summaryHeaders],
            body: summaryRows,
            startY: yPos,
            theme: "grid",
            styles: { fontSize: 8, cellPadding: 2 },
            headStyles: { fillColor: [59, 130, 246] },
          })

          yPos = (doc as any).lastAutoTable.finalY + 10
        }
      }

      // Adicionar dados detalhados se existirem
      if (filteredDetailedData.length > 0) {
        // Adicionar uma nova página para os detalhes
        doc.addPage()

        doc.setFontSize(16)
        doc.text("Detalhes dos Borderos", 14, 15)

        const detailedHeaders = Object.keys(filteredDetailedData[0] || {})
        const detailedRows = filteredDetailedData.map((item) => Object.values(item))

        autoTable(doc, {
          head: [detailedHeaders],
          body: detailedRows,
          startY: 25,
          theme: "grid",
          styles: { fontSize: 8, cellPadding: 2 },
          headStyles: { fillColor: [59, 130, 246] },
        })
      }

      // Salvar o PDF
      doc.save(`${filename}.pdf`)

      toast({
        title: "Exportação concluída",
        description: "Os dados foram exportados com sucesso para PDF.",
      })

      setDialogOpen(false)
    } catch (error) {
      console.error("Erro ao exportar para PDF:", error)
      toast({
        title: "Erro na exportação",
        description: "Não foi possível exportar os dados para PDF.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Se não houver dados detalhados, usar o dropdown simples
  if (!detailedData || detailedData.length === 0) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" disabled={isExporting} className="gap-1">
            <Download className="h-3.5 w-3.5" />
            <span>Exportar</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={exportToExcel}>Exportar para Excel</DropdownMenuItem>
          <DropdownMenuItem onClick={exportToPDF}>Exportar para PDF</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // Se houver dados detalhados, usar o diálogo de seleção
  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" disabled={isExporting} className="gap-1">
          <Download className="h-3.5 w-3.5" />
          <span>Exportar</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Exportar Relatório</DialogTitle>
          <DialogDescription>Selecione os borderos que deseja incluir no relatório detalhado.</DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              id="select-all"
              checked={detailedData.length > 0 && detailedData.every((item) => selectedItems[item.id])}
              onCheckedChange={handleSelectAll}
            />
            <Label htmlFor="select-all" className="font-medium">
              Selecionar todos
            </Label>
          </div>

          <ScrollArea className="h-[300px] rounded-md border p-4">
            <div className="space-y-2">
              {detailedData.map((item) => (
                <div key={item.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`item-${item.id}`}
                    checked={selectedItems[item.id] || false}
                    onCheckedChange={() => handleSelectItem(item.id)}
                  />
                  <Label htmlFor={`item-${item.id}`} className="flex-1">
                    {item.codigo || item.bordero_cod} - {item.empresa || item.nome_empresa}
                  </Label>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setDialogOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={exportToExcel} disabled={isExporting} className="gap-1">
            <Download className="h-3.5 w-3.5" />
            <span>Excel</span>
          </Button>
          <Button onClick={exportToPDF} disabled={isExporting} className="gap-1">
            <Download className="h-3.5 w-3.5" />
            <span>PDF</span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
