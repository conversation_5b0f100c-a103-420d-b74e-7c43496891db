const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createLogAtividadesTable() {
  try {
    console.log('🔧 Criando tabela de logs de atividades...')

    // Verificar se a tabela já existe
    const { data: existingTable, error: checkError } = await supabase
      .from('log_atividades')
      .select('*')
      .limit(1)

    if (!checkError) {
      console.log('✅ Tabela log_atividades já existe')
      return
    }

    console.log('📝 Criando tabela log_atividades...')

    // Criar a tabela usando SQL direto
    const createTableSQL = `
      -- Criar tabel<PERSON> de logs de atividade
      CREATE TABLE IF NOT EXISTS log_atividades (
          id SERIAL PRIMARY KEY,
          usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
          acao VARCHAR(255) NOT NULL,
          entidade VARCHAR(255) NOT NULL,
          entidade_id VARCHAR(255),
          detalhes JSONB,
          ip VARCHAR(45),
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Criar índices para melhor performance
      CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
      CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
      CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

      -- Habilitar RLS (Row Level Security)
      ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;

      -- Criar política para permitir leitura para usuários autenticados
      CREATE POLICY IF NOT EXISTS "Usuários podem ver logs de atividades" ON log_atividades
        FOR SELECT USING (auth.role() = 'authenticated');

      -- Criar política para permitir inserção para usuários autenticados
      CREATE POLICY IF NOT EXISTS "Usuários podem criar logs de atividades" ON log_atividades
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    `

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL })

    if (createError) {
      console.error('❌ Erro ao criar tabela:', createError.message)
      
      // Tentar método alternativo
      console.log('🔄 Tentando método alternativo...')
      
      // Inserir um registro de teste para forçar a criação da tabela
      const { error: insertError } = await supabase
        .from('log_atividades')
        .insert({
          usuario_id: '00000000-0000-0000-0000-000000000000',
          acao: 'teste',
          entidade: 'sistema',
          entidade_id: null,
          detalhes: { teste: 'Teste de criação da tabela' },
          ip: '127.0.0.1',
          user_agent: 'Script de setup'
        })

      if (insertError && !insertError.message.includes('violates foreign key constraint')) {
        console.error('❌ Erro ao criar tabela (método alternativo):', insertError.message)
        console.log('\n⚠️ A tabela log_atividades precisa ser criada manualmente no Supabase.')
        console.log('\n📝 Execute o seguinte SQL no Supabase:')
        console.log(createTableSQL)
        return
      }

      // Deletar o registro de teste se foi inserido
      await supabase
        .from('log_atividades')
        .delete()
        .eq('acao', 'teste')
    }

    console.log('✅ Tabela log_atividades criada com sucesso!')

    // Testar inserção de um log de exemplo
    console.log('\n🧪 Testando inserção de log...')
    
    // Buscar um usuário existente
    const { data: usuarios } = await supabase
      .from('usuarios')
      .select('id')
      .limit(1)

    if (usuarios && usuarios.length > 0) {
      const { data: logTeste, error: logError } = await supabase
        .from('log_atividades')
        .insert({
          usuario_id: usuarios[0].id,
          acao: 'criar',
          entidade: 'sistema',
          entidade_id: null,
          detalhes: { teste: 'Log de teste - criação da tabela' },
          ip: '127.0.0.1',
          user_agent: 'Script de setup'
        })
        .select()
        .single()

      if (logError) {
        console.error('❌ Erro ao inserir log de teste:', logError.message)
      } else {
        console.log('✅ Log de teste inserido:', logTeste.id)
        
        // Deletar o log de teste
        await supabase
          .from('log_atividades')
          .delete()
          .eq('id', logTeste.id)
        
        console.log('🧹 Log de teste removido')
      }
    } else {
      console.log('⚠️ Não foi possível testar - sem usuários')
    }

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
  }
}

// Executar o script
createLogAtividadesTable()
  .then(() => {
    console.log('\n🎉 Script concluído!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
