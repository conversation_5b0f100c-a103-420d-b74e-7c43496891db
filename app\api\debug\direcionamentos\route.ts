import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("userId")

    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session: userSession }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !userSession) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const targetUserId = userId || userSession.user.id

    console.log("🔍 Debug - Verificando direcionamentos para usuário:", targetUserId)

    // Buscar direcionamentos do usuário
    const { data: userDirecionamentos, error: direcionamentosError } = await supabase
      .from("direcionamento_usuarios")
      .select(`
        id,
        direcionamento:direcionamentos(
          id,
          nome,
          valor_minimo,
          valor_maximo
        )
      `)
      .eq("usuario_id", targetUserId)

    if (direcionamentosError) {
      console.error("❌ Erro ao buscar direcionamentos:", direcionamentosError)
      return NextResponse.json({ 
        error: "Erro ao buscar direcionamentos",
        details: direcionamentosError 
      }, { status: 500 })
    }

    // Buscar informações do usuário
    const { data: usuario, error: usuarioError } = await supabase
      .from("usuarios")
      .select("id, nome, email")
      .eq("id", targetUserId)
      .single()

    if (usuarioError) {
      console.error("❌ Erro ao buscar usuário:", usuarioError)
    }

    // Buscar todos os direcionamentos para comparação
    const { data: todosDirecionamentos, error: todosError } = await supabase
      .from("direcionamentos")
      .select(`
        *,
        usuarios:direcionamento_usuarios(
          usuario:usuarios(id, nome, email)
        )
      `)
      .order("valor_minimo")

    if (todosError) {
      console.error("❌ Erro ao buscar todos os direcionamentos:", todosError)
    }

    // Calcular limite de valor
    let userValueLimits = null
    if (userDirecionamentos && userDirecionamentos.length > 0) {
      const maxValue = Math.max(
        ...userDirecionamentos
          .filter(d => d.direcionamento)
          .map(d => (d.direcionamento as any).valor_maximo)
      )
      
      if (maxValue > 0) {
        userValueLimits = maxValue
      }
    }

    const result = {
      usuario: usuario || { id: targetUserId, nome: "Usuário não encontrado", email: "N/A" },
      direcionamentos_usuario: userDirecionamentos || [],
      limite_valor: userValueLimits,
      todos_direcionamentos: todosDirecionamentos || [],
      debug_info: {
        total_direcionamentos_usuario: userDirecionamentos?.length || 0,
        total_direcionamentos_sistema: todosDirecionamentos?.length || 0,
        tem_limite: userValueLimits !== null,
        valor_limite_formatado: userValueLimits ? `R$ ${userValueLimits.toLocaleString('pt-BR')}` : "Sem limite"
      }
    }

    console.log("📊 Debug resultado:", JSON.stringify(result, null, 2))

    return NextResponse.json(result)
  } catch (error) {
    console.error("💥 Erro no debug de direcionamentos:", error)
    return NextResponse.json({ 
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
