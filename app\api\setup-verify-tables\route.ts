import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("🔍 Verificando status das tabelas...")

    const results = {
      success: true,
      tables: {} as any,
      summary: {
        total: 0,
        existing: 0,
        missing: 0,
        errors: 0
      }
    }

    // Lista de tabelas essenciais para verificar
    const tablesToCheck = [
      'usuarios',
      'niveis_acesso',
      'secretarias',
      'tipos',
      'borderos',
      'configuracoes_sistema',
      'log_atividades',
      'bordero_logs'
    ]

    for (const tableName of tablesToCheck) {
      results.summary.total++

      try {
        // Tentar fazer uma consulta simples para verificar se a tabela existe
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })
          .limit(1)

        if (error) {
          console.log(`❌ Erro na tabela ${tableName}:`, error.message)
          results.tables[tableName] = {
            exists: false,
            error: error.message,
            status: 'error',
            records: 0
          }
          results.summary.errors++
          if (error.message.includes('does not exist')) {
            results.summary.missing++
          }
        } else {
          console.log(`✅ Tabela ${tableName}: OK (${count || 0} registros)`)
          results.tables[tableName] = {
            exists: true,
            error: null,
            status: 'ok',
            records: count || 0
          }
          results.summary.existing++
        }
      } catch (err: any) {
        console.log(`❌ Erro crítico na tabela ${tableName}:`, err.message)
        results.tables[tableName] = {
          exists: false,
          error: err.message,
          status: 'critical_error',
          records: 0
        }
        results.summary.errors++
      }
    }

    // Verificar se há problemas críticos
    if (results.summary.missing > 0 || results.summary.errors > 0) {
      results.success = false
    }

    console.log("📊 Resumo da verificação:")
    console.log(`   Total: ${results.summary.total}`)
    console.log(`   Existentes: ${results.summary.existing}`)
    console.log(`   Ausentes: ${results.summary.missing}`)
    console.log(`   Erros: ${results.summary.errors}`)

    return NextResponse.json(results)
  } catch (error: any) {
    console.error("❌ Erro crítico na verificação:", error)
    return NextResponse.json({
      success: false,
      error: error.message,
      tables: {},
      summary: { total: 0, existing: 0, missing: 0, errors: 1 }
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("🔧 Tentando criar tabelas ausentes...")

    // SQL para criar tabelas essenciais
    const createTablesSQL = [
      // Níveis de acesso
      `
      CREATE TABLE IF NOT EXISTS niveis_acesso (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        nome VARCHAR(100) NOT NULL,
        descricao TEXT,
        permissoes JSONB NOT NULL DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,

      // Usuários
      `
      CREATE TABLE IF NOT EXISTS usuarios (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        nome VARCHAR(255) NOT NULL,
        nivel_acesso_id UUID REFERENCES niveis_acesso(id),
        avatar_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,

      // Configurações do sistema
      `
      CREATE TABLE IF NOT EXISTS configuracoes_sistema (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
        modo_escuro BOOLEAN DEFAULT false,
        notificacoes_email BOOLEAN DEFAULT true,
        logo_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,

      // Log de atividades
      `
      CREATE TABLE IF NOT EXISTS log_atividades (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        usuario_id UUID REFERENCES usuarios(id),
        acao VARCHAR(100) NOT NULL,
        entidade VARCHAR(100) NOT NULL,
        entidade_id UUID,
        detalhes JSONB,
        ip VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,

      // Logs específicos de borderos
      `
      CREATE TABLE IF NOT EXISTS bordero_logs (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        bordero_id UUID REFERENCES borderos(id),
        usuario_id UUID REFERENCES usuarios(id),
        acao VARCHAR(100) NOT NULL,
        detalhes TEXT,
        data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      `,

      // Inserir dados padrão
      `
      INSERT INTO niveis_acesso (nome, descricao, permissoes)
      VALUES
        (
          'Administrador',
          'Acesso completo ao sistema',
          '{
            "dashboard": true,
            "borderos": true,
            "secretarias": true,
            "direcionamentos": true,
            "tipos": true,
            "usuarios": true,
            "relatorios": true,
            "configuracoes": true
          }'::jsonb
        ),
        (
          'Operador',
          'Acesso para operações básicas',
          '{
            "dashboard": true,
            "borderos": true,
            "secretarias": false,
            "direcionamentos": false,
            "tipos": false,
            "usuarios": false,
            "relatorios": true,
            "configuracoes": false
          }'::jsonb
        ),
        (
          'Visualizador',
          'Apenas visualização de dados',
          '{
            "dashboard": true,
            "borderos": false,
            "secretarias": false,
            "direcionamentos": false,
            "tipos": false,
            "usuarios": false,
            "relatorios": true,
            "configuracoes": false
          }'::jsonb
        )
      ON CONFLICT (nome) DO NOTHING;
      `
    ]

    const results = []

    for (const sql of createTablesSQL) {
      try {
        // Usar RPC para executar SQL
        const { data, error } = await supabase.rpc('exec_sql', { sql })

        if (error) {
          console.log("❌ Erro ao executar SQL:", error)
          results.push({ success: false, error: error.message })
        } else {
          console.log("✅ SQL executado com sucesso")
          results.push({ success: true })
        }
      } catch (err: any) {
        console.log("❌ Erro crítico:", err.message)
        results.push({ success: false, error: err.message })
      }
    }

    // Habilitar RLS e criar políticas
    const rlsSQL = [
      "ALTER TABLE niveis_acesso ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;",
      "CREATE POLICY IF NOT EXISTS \"Permitir acesso total a niveis_acesso\" ON niveis_acesso FOR ALL USING (auth.role() = 'authenticated');",
      "CREATE POLICY IF NOT EXISTS \"Permitir acesso total a usuarios\" ON usuarios FOR ALL USING (auth.role() = 'authenticated');",
      "CREATE POLICY IF NOT EXISTS \"Permitir acesso total a configuracoes_sistema\" ON configuracoes_sistema FOR ALL USING (auth.role() = 'authenticated');",
      "CREATE POLICY IF NOT EXISTS \"Permitir acesso total a log_atividades\" ON log_atividades FOR ALL USING (auth.role() = 'authenticated');",
      "CREATE POLICY IF NOT EXISTS \"Permitir acesso total a bordero_logs\" ON bordero_logs FOR ALL USING (auth.role() = 'authenticated');"
    ]

    for (const sql of rlsSQL) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql })
        if (error) {
          console.log("⚠️ Aviso RLS:", error.message)
        }
      } catch (err) {
        console.log("⚠️ Aviso RLS:", err)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Tentativa de criação de tabelas concluída",
      results
    })
  } catch (error: any) {
    console.error("❌ Erro na criação de tabelas:", error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
