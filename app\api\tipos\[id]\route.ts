import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await request.json()
    const { nome, descricao } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome do tipo é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe um tipo com este nome (exceto o atual)
    const { data: existingTipo, error: checkError } = await supabase
      .from("tipos")
      .select("id")
      .eq("nome", nome.trim())
      .neq("id", id)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar tipo:", checkError)
      return NextResponse.json({ error: "Erro ao verificar tipo" }, { status: 500 })
    }

    if (existingTipo) {
      return NextResponse.json({ error: "Já existe um tipo com este nome" }, { status: 400 })
    }

    const { data: tipo, error } = await supabase
      .from("tipos")
      .update({
        nome: nome.trim(),
        descricao: descricao?.trim() || "",
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Erro ao atualizar tipo:", error)
      return NextResponse.json({ error: "Erro ao atualizar tipo" }, { status: 500 })
    }

    return NextResponse.json(tipo)
  } catch (error) {
    console.error("Erro ao atualizar tipo:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // Obter usuário autenticado
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar dados do tipo antes de excluir
    const { data: tipo, error: fetchError } = await supabase
      .from("tipos")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError || !tipo) {
      return NextResponse.json({ error: "Tipo não encontrado" }, { status: 404 })
    }

    // Verificar se o tipo tem borderos associados
    const { data: borderos, error: borderoError } = await supabase
      .from("borderos")
      .select("id")
      .eq("tipo_id", id)
      .limit(1)

    if (borderoError) {
      console.error("Erro ao verificar borderos:", borderoError)
      return NextResponse.json({ error: "Erro ao verificar borderos" }, { status: 500 })
    }

    if (borderos && borderos.length > 0) {
      return NextResponse.json({
        error: "Não é possível excluir este tipo pois ele possui borderos associados"
      }, { status: 400 })
    }

    const { error } = await supabase
      .from("tipos")
      .delete()
      .eq("id", id)

    if (error) {
      console.error("Erro ao excluir tipo:", error)
      return NextResponse.json({ error: "Erro ao excluir tipo" }, { status: 500 })
    }

    // Registrar log de exclusão
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: user.id,
          acao: "excluir",
          entidade: "tipo",
          entidade_id: id,
          detalhes: {
            nome: tipo.nome,
            descricao: tipo.descricao
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "desconhecido",
          user_agent: request.headers.get("user-agent") || "desconhecido"
        })
    } catch (logError) {
      console.error("Erro ao registrar log de exclusão:", logError)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Erro ao excluir tipo:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
