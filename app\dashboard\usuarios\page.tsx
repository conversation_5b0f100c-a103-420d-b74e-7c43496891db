"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { EmailInput } from "@/components/ui/email-input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { Plus, Edit, Loader2, Trash2 } from "lucide-react"
import { PermissionGuard } from "@/components/auth/permission-guard"
import { EnhancedPasswordInput } from "@/components/ui/enhanced-password-input"
import { usePermissions } from "@/hooks/use-permissions"

interface Usuario {
  id: string
  nome: string
  email: string
  nivel_acesso_id: string
  created_at: string
  updated_at: string
  niveis_acesso?: {
    nome: string
  }
}

interface NivelAcesso {
  id: string
  nome: string
}

export default function UsuariosPage() {
  const notify = useNotify()
  const { toast } = useToast()
  const { isAdmin } = usePermissions()
  const [mounted, setMounted] = useState(false)
  const [usuarios, setUsuarios] = useState<Usuario[]>([])
  const [niveisAcesso, setNiveisAcesso] = useState<NivelAcesso[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingUsuario, setEditingUsuario] = useState<Usuario | null>(null)
  const [formData, setFormData] = useState({
    nome: "",
    email: "",
    nivel_acesso_id: "",
    nova_senha: ""
  })

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    fetchUsuarios()
    fetchNiveisAcesso()
  }, [])

  const fetchUsuarios = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/usuarios")
      if (!response.ok) throw new Error("Erro ao buscar usuários")

      const data = await response.json()
      setUsuarios(data)
    } catch (error) {
      console.error("Erro ao buscar usuários:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os usuários.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchNiveisAcesso = async () => {
    try {
      const response = await fetch("/api/niveis-acesso")
      if (!response.ok) throw new Error("Erro ao buscar níveis de acesso")

      const data = await response.json()
      setNiveisAcesso(data)
    } catch (error) {
      console.error("Erro ao buscar níveis de acesso:", error)
      // Fallback para dados simulados se a API falhar
      setNiveisAcesso([
        { id: "1", nome: "Administrador" },
        { id: "2", nome: "Operador" },
        { id: "3", nome: "Visualizador" },
      ])
    }
  }

  const handleSalvar = async () => {
    console.log("Dados do formulário:", formData)
    console.log("Níveis de acesso disponíveis:", niveisAcesso)

    if (!formData.nome.trim() || !formData.email.trim() || !formData.nivel_acesso_id) {
      toast({
        title: "Erro",
        description: "Todos os campos são obrigatórios.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      const url = editingUsuario ? `/api/usuarios/${editingUsuario.id}` : "/api/usuarios"
      const method = editingUsuario ? "PUT" : "POST"

      const body: any = {
        nome: formData.nome.trim(),
        email: formData.email.trim(),
        nivel_acesso_id: formData.nivel_acesso_id
      }

      // Adicionar senha apenas se fornecida e usuário for admin
      if (formData.nova_senha.trim() && isAdmin()) {
        if (editingUsuario) {
          body.nova_senha = formData.nova_senha.trim()
        } else {
          body.senha = formData.nova_senha.trim()
        }
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Erro ao ${editingUsuario ? 'atualizar' : 'criar'} usuário`)
      }

      const usuario = await response.json()

      // Verificar se houve warning ou sucesso especial
      if (usuario.warning) {
        notify.warning(
          "Atenção",
          usuario.warning
        )
      } else if (usuario.success) {
        notify.success(
          editingUsuario ? "Usuário Atualizado" : "Usuário Cadastrado",
          usuario.success
        )
      } else {
        notify.success(
          editingUsuario ? "Usuário Atualizado" : "Usuário Cadastrado",
          `Usuário "${usuario.nome}" foi ${editingUsuario ? 'atualizado' : 'cadastrado'} com sucesso!`
        )
      }

      setDialogOpen(false)
      setEditingUsuario(null)
      setFormData({ nome: "", email: "", nivel_acesso_id: "", nova_senha: "" })
      fetchUsuarios()
    } catch (error: any) {
      console.error(`Erro ao ${editingUsuario ? 'atualizar' : 'criar'} usuário:`, error)

      // Usar a mensagem específica do servidor se disponível
      const errorMessage = error.message || `Não foi possível ${editingUsuario ? 'atualizar' : 'criar'} o usuário.`

      toast({
        title: "Erro ao criar usuário",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditar = (usuario: Usuario) => {
    setEditingUsuario(usuario)
    setFormData({
      nome: usuario.nome,
      email: usuario.email,
      nivel_acesso_id: usuario.nivel_acesso_id,
      nova_senha: ""
    })
    setDialogOpen(true)
  }

  const handleExcluir = async (usuario: Usuario) => {
    try {
      const response = await fetch(`/api/usuarios/${usuario.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao excluir usuário")
      }

      notify.success(
        "Usuário Excluído",
        `Usuário "${usuario.nome}" foi excluído com sucesso!`
      )

      fetchUsuarios()
    } catch (error: any) {
      console.error("Erro ao excluir usuário:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir o usuário.",
        variant: "destructive",
      })
    }
  }

  const getNivelAcessoNome = (nivelId: string) => {
    const nivel = niveisAcesso.find(n => n.id === nivelId)
    return nivel?.nome || "Não definido"
  }

  // Evitar problemas de hidratação
  if (!mounted) {
    return (
      <PermissionGuard permission="usuarios">
        <div className="space-y-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <h1 className="text-3xl font-bold tracking-tight">Usuários</h1>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </PermissionGuard>
    )
  }

  return (
    <PermissionGuard permission="usuarios">
      <div className="space-y-6" suppressHydrationWarning>
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Usuários</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sky-600 hover:bg-sky-700">
              <Plus className="mr-2 h-4 w-4" />
              Novo Usuário
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingUsuario ? "Editar Usuário" : "Adicionar Novo Usuário"}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome</Label>
                <Input
                  id="nome"
                  placeholder="Digite o nome do usuário"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <EmailInput
                  id="email"
                  placeholder="Digite o email do usuário"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nivelAcesso">Nível de Acesso</Label>
                <Select value={formData.nivel_acesso_id || undefined} onValueChange={(value) => setFormData(prev => ({ ...prev, nivel_acesso_id: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o nível de acesso" />
                  </SelectTrigger>
                  <SelectContent>
                    {niveisAcesso.map((nivel) => (
                      <SelectItem key={nivel.id} value={nivel.id}>
                        {nivel.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Campo de senha - para administradores */}
              {isAdmin() && (
                <div className="space-y-2">
                  <Label htmlFor="nova_senha">
                    {editingUsuario ? "Nova Senha (opcional)" : "Senha (opcional)"}
                  </Label>
                  <EnhancedPasswordInput
                    id="nova_senha"
                    placeholder={editingUsuario ? "Digite uma nova senha para o usuário" : "Digite uma senha ou deixe em branco para senha padrão"}
                    value={formData.nova_senha}
                    onChange={(e) => setFormData(prev => ({ ...prev, nova_senha: e.target.value }))}
                    showStrength={true}
                    showCriteria={true}
                  />
                  <p className="text-sm text-muted-foreground">
                    {editingUsuario
                      ? "Deixe em branco para manter a senha atual"
                      : "Deixe em branco para usar senha padrão: TempPass123!"
                    }
                  </p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setDialogOpen(false)
                  setEditingUsuario(null)
                  setFormData({ nome: "", email: "", nivel_acesso_id: "", nova_senha: "" })
                }}
                disabled={saving}
              >
                Cancelar
              </Button>
              <Button
                className="bg-sky-600 hover:bg-sky-700"
                onClick={handleSalvar}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usuários Cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Nível de Acesso</TableHead>
                  <TableHead>Data de Criação</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usuarios.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      Nenhum usuário cadastrado.
                    </TableCell>
                  </TableRow>
                ) : (
                  usuarios.map((usuario) => (
                    <TableRow key={usuario.id}>
                      <TableCell className="font-medium">{usuario.nome}</TableCell>
                      <TableCell>{usuario.email}</TableCell>
                      <TableCell>{getNivelAcessoNome(usuario.nivel_acesso_id)}</TableCell>
                      <TableCell>{new Date(usuario.created_at).toLocaleDateString("pt-BR")}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditar(usuario)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir o usuário "{usuario.nome}"?
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleExcluir(usuario)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Excluir
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      </div>
    </PermissionGuard>
  )
}
