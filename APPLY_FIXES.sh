#!/bin/bash

# Script para aplicar todas as correções para o problema de permissão
# durante a importação de borderos, o problema de duplicação e o botão de ações

echo "Iniciando aplicação das correções..."

# 1. Aplicar o script SQL para corrigir o problema de permissão
echo "Aplicando correções SQL..."
psql -f FIX_PERMISSION_ERROR.sql

# 2. Substituir o arquivo de importação de borderos
echo "Atualizando o arquivo de importação de borderos..."
mv app/api/borderos/import/route.ts.new app/api/borderos/import/route.ts

# 3. Corrigir o problema de duplicação na importação
echo "Corrigindo o problema de duplicação na importação..."
# Backup do arquivo original
cp app/dashboard/borderos/importar/page.tsx app/dashboard/borderos/importar/page.tsx.bak

# Aplicar as correções usando sed
# Substituir a função executeImport
sed -i '/const executeImport = async () => {/,/};/c\
const executeImport = async () => {\
  // Resetar estados antes de iniciar a importação\
  setImporting(true);\
  setPaused(false);\
  setCancelled(false);\
  setProgressCount(0);\
  setSuccessCount(0);\
  setErrorCount(0);\
  setLogs([]);\
  setImportResults(null);\
  setImportProgress(0);\
  setStep('"'"'import'"'"');\
  \
  // Filtrar apenas borderos válidos e não duplicados\
  const validRows = mappedData.filter(row => row.status === '"'"'valid'"'"' && !row.duplicate);\
  totalValidRowsRef.current = validRows.length;\
  \
  if (validRows.length === 0) {\
    toast({\
      title: "Nenhum bordero válido",\
      description: "Não há borderos válidos para importar.",\
      variant: "destructive"\
    });\
    setImporting(false);\
    setStep('"'"'validation'"'"');\
    return;\
  }\
  \
  // Inicializar progresso\
  setProgressCount(0);\
  setImportProgress(0);\
  setLogs([{status:'"'"'success'"'"', message:`Iniciando importação de ${validRows.length} borderos...`}]);\
  \
  // Criar um novo AbortController para esta importação\
  if (abortControllerRef.current) {\
    try {\
      abortControllerRef.current.abort();\
    } catch (e) {\
      console.error("Erro ao abortar controlador anterior:", e);\
    }\
  }\
  \
  // Processar em lotes para melhor feedback visual\
  const BATCH_SIZE = 5; // Processar em lotes de 5 para atualizar a UI mais frequentemente\
  let hasErrors = false;\
  \
  try {\
    for (let i = 0; i < validRows.length; i++) {\
      if (cancelledRef.current) {\
        setLogs(logs => [{status:'"'"'error'"'"', message:'"'"'Importação cancelada pelo usuário.'"'"'}, ...logs]);\
        break;\
      }\
      while (pausedRef.current) await new Promise(res => setTimeout(res, 300));\
      \
      const row = validRows[i];\
      currentRecordRef.current = { bordero_cod: row.mapped.bordero_cod, nome_empresa: row.mapped.nome_empresa };\
      \
      // Atualizar progresso com animação suave\
      const progressPercentage = Math.round(((i+1) / validRows.length) * 100);\
      setProgressCount(i+1);\
      setImportProgress(prev => {\
        // Incrementar gradualmente para dar sensação de progresso contínuo\
        const increment = (progressPercentage - prev) / 2;\
        return prev + increment;\
      });\
      \
      // A cada BATCH_SIZE registros ou no último registro, atualizar o progresso exato\
      if (i % BATCH_SIZE === 0 || i === validRows.length - 1) {\
        setTimeout(() => setImportProgress(progressPercentage), 100);\
      }\
      \
      abortControllerRef.current = new AbortController();\
      try {\
        console.log(`[IMPORT] Enviando registro ${i+1}/${validRows.length}:`, row.mapped);\
        const response = await fetch('"'"'/api/borderos/import'"'"', {\
          method: '"'"'POST'"'"',\
          headers: { '"'"'Content-Type'"'"': '"'"'application/json'"'"' },\
          body: JSON.stringify({ borderos: [row.mapped] }),\
          signal: abortControllerRef.current.signal\
        });\
        \
        let result = null;\
        try {\
          result = await response.json();\
        } catch (jsonErr) {\
          console.error('"'"'[IMPORT] Erro ao parsear JSON da resposta:'"'"', jsonErr);\
          setLogs(logs => [{status:'"'"'error'"'"', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (Resposta não é JSON)`}, ...logs.slice(0,49)]);\
          setErrorCount(c => c+1);\
          hasErrors = true;\
          continue;\
        }\
        \
        if (response.ok && result.success > 0) {\
          setSuccessCount(c => c+1);\
          setLogs(logs => [{status:'"'"'success'"'"', message:`Importado: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa}`}, ...logs.slice(0,49)]);\
          console.log(`[IMPORT] Sucesso:`, result);\
        } else {\
          let errorMsg = '"'"''"'"';\
          if (typeof result?.error === '"'"'string'"'"') errorMsg = result.error;\
          else if (Array.isArray(result?.details) && result.details.length > 0 && result.details[0]?.error) errorMsg = result.details[0].error;\
          else if (result?.details) errorMsg = JSON.stringify(result.details);\
          else errorMsg = JSON.stringify(result);\
          \
          setErrorCount(c => c+1);\
          setLogs(logs => [{status:'"'"'error'"'"', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${errorMsg})`}, ...logs.slice(0,49)]);\
          console.error(`[IMPORT] Falha:`, errorMsg, result);\
          hasErrors = true;\
          \
          // Se o erro for de permissão da materialized view, mostrar mensagem específica\
          if (errorMsg.includes("must be owner of materialized view")) {\
            toast({\
              title: "Erro de Permissão",\
              description: "Erro de permissão ao atualizar estatísticas. Contate o administrador do sistema.",\
              variant: "destructive"\
            });\
          }\
        }\
      } catch (err) {\
        if (err.name === '"'"'AbortError'"'"') {\
          setLogs(logs => [{status:'"'"'error'"'"', message:'"'"'Importação cancelada pelo usuário (AbortController).'"'"'}, ...logs]);\
          console.warn('"'"'[IMPORT] Importação abortada pelo usuário.'"'"');\
          break;\
        }\
        \
        setErrorCount(c => c+1);\
        setLogs(logs => [{status:'"'"'error'"'"', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${err.message || err})`}, ...logs.slice(0,49)]);\
        console.error(`[IMPORT] Erro inesperado:`, err);\
        hasErrors = true;\
      }\
    }\
  } catch (error) {\
    console.error("[IMPORT] Erro crítico durante a importação:", error);\
    toast({\
      title: "Erro na Importação",\
      description: "Ocorreu um erro durante a importação. Verifique os logs para mais detalhes.",\
      variant: "destructive"\
    });\
    hasErrors = true;\
  } finally {\
    setImporting(false);\
    setImportResults({ \
      success: successCount, \
      errors: errorCount,\
      hasErrors: hasErrors\
    });\
    \
    // Se não houve nenhum sucesso e houve erros, voltar para a tela de validação\
    if (successCount === 0 && hasErrors) {\
      toast({\
        title: "Importação Falhou",\
        description: "A importação falhou completamente. Verifique os erros e tente novamente.",\
        variant: "destructive"\
      });\
      \
      // Aguardar um momento antes de voltar para a tela de validação\
      setTimeout(() => {\
        setStep('"'"'validation'"'"');\
      }, 3000);\
    }\
  }\
};' app/dashboard/borderos/importar/page.tsx

# Substituir a seção de resultados da importação
sed -i '/{importResults && (/,/)}/ c\
{importResults && (\
  <div className="space-y-4">\
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">\
      <Card className="p-4">\
        <div className="flex items-center gap-2">\
          <CheckCircle className="w-4 h-4 text-green-600" />\
          <span className="text-sm font-medium">Importados</span>\
        </div>\
        <div className="text-2xl font-bold text-green-600">\
          {typeof importResults.success === '"'"'number'"'"' ? importResults.success : 0}\
        </div>\
      </Card>\
      <Card className="p-4">\
        <div className="flex items-center gap-2">\
          <XCircle className="w-4 h-4 text-red-600" />\
          <span className="text-sm font-medium">Falhas</span>\
        </div>\
        <div className="text-2xl font-bold text-red-600">\
          {Array.isArray(importResults.errors) ? importResults.errors.length : (typeof importResults.errors === '"'"'number'"'"' ? importResults.errors : 0)}\
        </div>\
      </Card>\
    </div>\
\
    {importResults.errors && importResults.errors.length > 0 && (\
      <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">\
        <CardHeader>\
          <CardTitle className="text-red-800 dark:text-red-200 flex items-center gap-2">\
            <XCircle className="w-5 h-5" />\
            Detalhes das Falhas\
          </CardTitle>\
        </CardHeader>\
        <CardContent className="space-y-2">\
          {importResults.errors.map((error, index) => (\
            <div key={index} className="text-sm text-red-700 dark:text-red-300">\
              {error.message || "Erro desconhecido durante a importação."}\
            </div>\
          ))}\
        </CardContent>\
      </Card>\
    )}\
\
    <div className="flex gap-4">\
      <Button onClick={() => router.push('"'"'/dashboard/borderos'"'"')}>\
        Ver Borderos\
      </Button>\
      <Button variant="outline" onClick={handleResetImport}>\
        Nova Importação\
      </Button>\
      {importResults.hasErrors && (\
        <Button variant="outline" onClick={() => setStep('"'"'validation'"'"')}>\
          Voltar para Validação\
        </Button>\
      )}\
    </div>\
  </div>\
)}' app/dashboard/borderos/importar/page.tsx

# 4. Corrigir o botão de ações na tabela de borderos
echo "Corrigindo o botão de ações na tabela de borderos..."
# Backup do arquivo original
cp components/dashboard/bordero-table-with-pagination.tsx components/dashboard/bordero-table-with-pagination.tsx.bak

# Adicionar as importações necessárias
sed -i '8i\
import {\
  DropdownMenu,\
  DropdownMenuContent,\
  DropdownMenuItem,\
  DropdownMenuLabel,\
  DropdownMenuSeparator,\
  DropdownMenuTrigger,\
} from "@/components/ui/dropdown-menu"\
import { MoreHorizontal, Eye, Edit, CheckCircle, AlertCircle, Archive } from "lucide-react"\
import { useRouter } from "next/navigation"\
import { useNotify } from "@/components/providers/notification-provider"\
import {\
  Dialog,\
  DialogContent,\
  DialogDescription,\
  DialogFooter,\
  DialogHeader,\
  DialogTitle,\
} from "@/components/ui/dialog"\
import { Textarea } from "@/components/ui/textarea"\
import { Button } from "@/components/ui/button"' components/dashboard/bordero-table-with-pagination.tsx

# Adicionar os estados e funções necessárias
sed -i '28i\
  const router = useRouter()\
  const notify = useNotify()\
  const [dialogOpen, setDialogOpen] = useState(false)\
  const [dialogType, setDialogType] = useState<"visualizar" | "correcao">("visualizar")\
  const [selectedBordero, setSelectedBordero] = useState<any | null>(null)\
  const [correcaoMotivo, setCorrecaoMotivo] = useState("")' components/dashboard/bordero-table-with-pagination.tsx

# Adicionar a função para atualizar o status do bordero
sed -i '/const handleItemsPerPageChange/i\
  // Função para atualizar o status do bordero\
  const updateBorderoStatus = async (id: number, newStatus: string, dadosStatus?: string) => {\
    try {\
      const response = await fetch(`/api/borderos/${id}/status`, {\
        method: "PUT",\
        headers: {\
          "Content-Type": "application/json",\
        },\
        body: JSON.stringify({\
          status: newStatus,\
          dadosStatus,\
          usuarioId: 1, // Simulando usuário logado\
        }),\
      })\
\
      if (!response.ok) {\
        throw new Error("Erro ao atualizar status")\
      }\
\
      // Mapear status para mensagens mais amigáveis\
      const statusMessages: { [key: string]: string } = {\
        analise: "Em Análise",\
        assinado: "Assinado",\
        pago: "Pago",\
        arquivado: "Arquivado",\
        corrigir: "Correção Solicitada"\
      }\
\
      const statusMessage = statusMessages[newStatus] || newStatus\
\
      notify.success(\
        "Status Atualizado",\
        `Bordero ${borderos.find(b => b.id === id)?.bordero_cod} foi marcado como ${statusMessage}.`\
      )\
\
      // Atualizar a lista de borderos\
      fetchBorderos()\
\
      // Fechar o diálogo se estiver aberto\
      setDialogOpen(false)\
      setCorrecaoMotivo("")\
    } catch (error) {\
      console.error("Erro ao atualizar status:", error)\
      toast({\
        title: "Erro",\
        description: "Não foi possível atualizar o status do bordero.",\
        variant: "destructive",\
      })\
    }\
  }\
\
  const handleSolicitarCorrecao = () => {\
    if (!selectedBordero) return\
\
    if (!correcaoMotivo.trim()) {\
      toast({\
        title: "Erro",\
        description: "Informe o motivo da correção.",\
        variant: "destructive",\
      })\
      return\
    }\
\
    updateBorderoStatus(selectedBordero.id, "corrigir", correcaoMotivo)\
  }\
\
  // Função para renderizar o badge de status\
  const renderStatusBadge = (status: string) => {\
    switch (status) {\
      case "novo":\
        return (\
          <Badge className="bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800">\
            Novo\
          </Badge>\
        )\
      case "analise":\
        return (\
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">\
            Em Análise\
          </Badge>\
        )\
      case "assinado":\
        return (\
          <Badge className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">\
            Assinado\
          </Badge>\
        )\
      case "pago":\
        return (\
          <Badge className="bg-purple-50 text-purple-600 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800">\
            Pago\
          </Badge>\
        )\
      case "corrigir":\
        return (\
          <Badge className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">\
            Corrigir\
          </Badge>\
        )\
      case "cancelado":\
        return (\
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">\
            Cancelado\
          </Badge>\
        )\
      case "excluido":\
        return (\
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">\
            Excluído\
          </Badge>\
        )\
      case "arquivado":\
        return (\
          <Badge className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700">\
            Arquivado\
          </Badge>\
        )\
      default:\
        return <Badge variant="outline">Desconhecido</Badge>\
    }\
  }' components/dashboard/bordero-table-with-pagination.tsx

# Substituir o botão "Ações" por um DropdownMenu
sed -i '/<td className="py-3 px-4 text-right">/,/<\/td>/ c\
                        <td className="py-3 px-4 text-right">\
                          <DropdownMenu>\
                            <DropdownMenuTrigger asChild>\
                              <Button variant="ghost" size="sm">\
                                <MoreHorizontal className="h-4 w-4" />\
                                <span className="sr-only">Abrir menu</span>\
                              </Button>\
                            </DropdownMenuTrigger>\
                            <DropdownMenuContent align="end">\
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>\
                              <DropdownMenuSeparator />\
                              <DropdownMenuItem onClick={() => {\
                                router.push(`/dashboard/borderos/${bordero.id}`)\
                              }}>\
                                <Eye className="mr-2 h-4 w-4" />\
                                <span>Visualizar</span>\
                              </DropdownMenuItem>\
                              <DropdownMenuItem onClick={() => {\
                                router.push(`/dashboard/borderos/${bordero.id}/editar`)\
                              }}>\
                                <Edit className="mr-2 h-4 w-4" />\
                                <span>Editar</span>\
                              </DropdownMenuItem>\
                              {bordero.status === "novo" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>\
                                  <CheckCircle className="mr-2 h-4 w-4" />\
                                  <span>Marcar em Análise</span>\
                                </DropdownMenuItem>\
                              )}\
                              {bordero.status === "analise" && (\
                                <>\
                                  <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>\
                                    <CheckCircle className="mr-2 h-4 w-4" />\
                                    <span>Aprovar</span>\
                                  </DropdownMenuItem>\
                                  <DropdownMenuItem\
                                    onClick={() => {\
                                      setSelectedBordero(bordero)\
                                      setDialogType("correcao")\
                                      setDialogOpen(true)\
                                    }}\
                                  >\
                                    <AlertCircle className="mr-2 h-4 w-4" />\
                                    <span>Solicitar Correção</span>\
                                  </DropdownMenuItem>\
                                </>\
                              )}\
                              {bordero.status === "assinado" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>\
                                  <CheckCircle className="mr-2 h-4 w-4" />\
                                  <span>Marcar como Pago</span>\
                                </DropdownMenuItem>\
                              )}\
                              {bordero.status === "pago" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>\
                                  <Archive className="mr-2 h-4 w-4" />\
                                  <span>Arquivar</span>\
                                </DropdownMenuItem>\
                              )}\
                            </DropdownMenuContent>\
                          </DropdownMenu>\
                        </td>' components/dashboard/bordero-table-with-pagination.tsx

# Substituir o botão de ações no layout mobile
sed -i '/<button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0">/,/<\/button>/ c\
                          <DropdownMenu>\
                            <DropdownMenuTrigger asChild>\
                              <Button variant="ghost" size="sm">\
                                <MoreHorizontal className="h-4 w-4" />\
                              </Button>\
                            </DropdownMenuTrigger>\
                            <DropdownMenuContent align="end">\
                              <DropdownMenuLabel>Ações</DropdownMenuLabel>\
                              <DropdownMenuSeparator />\
                              <DropdownMenuItem onClick={() => {\
                                router.push(`/dashboard/borderos/${bordero.id}`)\
                              }}>\
                                <Eye className="mr-2 h-4 w-4" />\
                                <span>Visualizar</span>\
                              </DropdownMenuItem>\
                              <DropdownMenuItem onClick={() => {\
                                router.push(`/dashboard/borderos/${bordero.id}/editar`)\
                              }}>\
                                <Edit className="mr-2 h-4 w-4" />\
                                <span>Editar</span>\
                              </DropdownMenuItem>\
                              {bordero.status === "novo" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "analise")}>\
                                  <CheckCircle className="mr-2 h-4 w-4" />\
                                  <span>Marcar em Análise</span>\
                                </DropdownMenuItem>\
                              )}\
                              {bordero.status === "analise" && (\
                                <>\
                                  <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "assinado")}>\
                                    <CheckCircle className="mr-2 h-4 w-4" />\
                                    <span>Aprovar</span>\
                                  </DropdownMenuItem>\
                                  <DropdownMenuItem\
                                    onClick={() => {\
                                      setSelectedBordero(bordero)\
                                      setDialogType("correcao")\
                                      setDialogOpen(true)\
                                    }}\
                                  >\
                                    <AlertCircle className="mr-2 h-4 w-4" />\
                                    <span>Solicitar Correção</span>\
                                  </DropdownMenuItem>\
                                </>\
                              )}\
                              {bordero.status === "assinado" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "pago")}>\
                                  <CheckCircle className="mr-2 h-4 w-4" />\
                                  <span>Marcar como Pago</span>\
                                </DropdownMenuItem>\
                              )}\
                              {bordero.status === "pago" && (\
                                <DropdownMenuItem onClick={() => updateBorderoStatus(bordero.id, "arquivado")}>\
                                  <Archive className="mr-2 h-4 w-4" />\
                                  <span>Arquivar</span>\
                                </DropdownMenuItem>\
                              )}\
                            </DropdownMenuContent>\
                          </DropdownMenu>' components/dashboard/bordero-table-with-pagination.tsx

# Adicionar o componente de diálogo no final do componente
sed -i '/return (/{
:a
n
/^    <\/div>$/{
i\
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>\
        <DialogContent className="max-w-2xl">\
          <DialogHeader>\
            <DialogTitle>\
              {dialogType === "visualizar" ? "Detalhes do Bordero" : "Solicitar Correção"}\
            </DialogTitle>\
            <DialogDescription>\
              {dialogType === "visualizar"\
                ? `Informações detalhadas do bordero ${selectedBordero?.bordero_cod}`\
                : `Informe o motivo da correção para o bordero ${selectedBordero?.bordero_cod}.`\
              }\
            </DialogDescription>\
          </DialogHeader>\
\
          {dialogType === "visualizar" ? (\
            <div className="space-y-4">\
              <div className="grid grid-cols-2 gap-4">\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Código</label>\
                  <p className="text-sm">{selectedBordero?.bordero_cod}</p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Status</label>\
                  <p className="text-sm">{renderStatusBadge(selectedBordero?.status || "")}</p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Empresa</label>\
                  <p className="text-sm">{selectedBordero?.nome_empresa}</p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Valor</label>\
                  <p className="text-sm">\
                    {selectedBordero?.valor && new Intl.NumberFormat("pt-BR", {\
                      style: "currency",\
                      currency: "BRL"\
                    }).format(selectedBordero.valor)}\
                  </p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Data</label>\
                  <p className="text-sm">\
                    {selectedBordero?.data && new Date(selectedBordero.data).toLocaleDateString("pt-BR")}\
                  </p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Secretaria</label>\
                  <p className="text-sm">{selectedBordero?.secretaria?.nome}</p>\
                </div>\
                <div>\
                  <label className="text-sm font-medium text-gray-500">Tipo</label>\
                  <p className="text-sm">{selectedBordero?.tipo?.nome}</p>\
                </div>\
              </div>\
            </div>\
          ) : (\
            <Textarea\
              placeholder="Descreva o motivo da correção..."\
              value={correcaoMotivo}\
              onChange={(e) => setCorrecaoMotivo(e.target.value)}\
              className="min-h-[100px]"\
            />\
          )}\
\
          <DialogFooter>\
            <Button variant="outline" onClick={() => setDialogOpen(false)}>\
              {dialogType === "visualizar" ? "Fechar" : "Cancelar"}\
            </Button>\
            {dialogType === "correcao" && (\
              <Button onClick={handleSolicitarCorrecao}>Enviar Solicitação</Button>\
            )}\
          </DialogFooter>\
        </DialogContent>\
      </Dialog>
}
ba
}' components/dashboard/bordero-table-with-pagination.tsx

# 5. Reiniciar o servidor
echo "Reiniciando o servidor..."
npm run build
npm run start

echo "Correções aplicadas com sucesso!"
echo "O sistema agora deve funcionar corretamente sem erros de permissão, sem duplicação durante a importação, e com o botão de ações funcionando."
echo ""
echo "Resumo das alterações:"
echo "1. Criado um sistema de fila para atualização das materialized views"
echo "2. Removido o trigger que tentava atualizar as views diretamente"
echo "3. Criado um job agendado para processar a fila de atualização"
echo "4. Modificado o endpoint de importação para usar a fila"
echo "5. Cor