const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY

console.log('🔍 Testando autenticação...')
console.log('URL:', supabaseUrl)
console.log('Anon Key:', supabaseAnonKey ? 'Configurada' : 'Não configurada')

// Cliente com service role para administração
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Cliente normal para teste de login
const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  try {
    console.log('\n1. 📋 Listando usuários no Auth...')
    const { data: users, error: listError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (listError) {
      console.error('❌ Erro ao listar usuários:', listError.message)
      return
    }
    
    console.log('✅ Usuários encontrados:', users.users.length)
    users.users.forEach(user => {
      console.log(`   - ${user.email} (ID: ${user.id})`)
    })
    
    const adminUser = users.users.find(u => u.email === '<EMAIL>')
    if (!adminUser) {
      console.log('❌ Usuário <EMAIL> não encontrado no Auth')
      return
    }
    
    console.log('\n2. 🔑 Testando login com credenciais...')
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Admin@123'
    })
    
    if (loginError) {
      console.error('❌ Erro no login:', loginError.message)
      
      // Tentar resetar a senha
      console.log('\n3. 🔄 Tentando resetar senha...')
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        adminUser.id,
        { password: 'Admin@123' }
      )
      
      if (updateError) {
        console.error('❌ Erro ao resetar senha:', updateError.message)
      } else {
        console.log('✅ Senha resetada com sucesso')
        
        // Tentar login novamente
        console.log('\n4. 🔑 Testando login após reset...')
        const { data: newLoginData, error: newLoginError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'Admin@123'
        })
        
        if (newLoginError) {
          console.error('❌ Erro no login após reset:', newLoginError.message)
        } else {
          console.log('✅ Login bem-sucedido após reset!')
          console.log('   - User ID:', newLoginData.user.id)
          console.log('   - Email:', newLoginData.user.email)
        }
      }
    } else {
      console.log('✅ Login bem-sucedido!')
      console.log('   - User ID:', loginData.user.id)
      console.log('   - Email:', loginData.user.email)
    }
    
    console.log('\n5. 👤 Verificando dados na tabela usuarios...')
    const { data: userData, error: userError } = await supabaseAdmin
      .from('usuarios')
      .select('*, niveis_acesso(nome, permissoes)')
      .eq('email', '<EMAIL>')
      .single()
    
    if (userError) {
      console.error('❌ Erro ao buscar usuário na tabela:', userError.message)
    } else {
      console.log('✅ Usuário encontrado na tabela:')
      console.log('   - Nome:', userData.nome)
      console.log('   - Email:', userData.email)
      console.log('   - Nível:', userData.niveis_acesso?.nome)
    }
    
  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

testAuth()
