import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

// Método PATCH para marcar notificação como lida
export async function PATCH(request: Request, { params }: { params: Promise<{ id: string }> }) {
  return markNotificationAsRead(request, params)
}

// Método PUT para compatibilidade
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  return markNotificationAsRead(request, params)
}

async function markNotificationAsRead(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const supabase = createRouteHandlerClient({ cookies })

    // Obter o usuário atual
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    console.log(`Marcando notificação ${id} como lida para usuário ${session.user.id}`)

    const { data: notificacao, error } = await supabase
      .from("notificacoes")
      .update({ lida: true, updated_at: new Date().toISOString() })
      .eq("id", id)
      .eq("usuario_id", session.user.id) // Garantir que o usuário só pode marcar suas próprias notificações
      .select()
      .single()

    if (error) {
      console.error("Erro ao marcar notificação como lida:", error)
      return NextResponse.json({
        error: "Erro ao marcar notificação como lida",
        details: error.message
      }, { status: 500 })
    }

    if (!notificacao) {
      return NextResponse.json({ error: "Notificação não encontrada" }, { status: 404 })
    }

    return NextResponse.json(notificacao)
  } catch (error) {
    console.error("Erro ao marcar notificação como lida:", error)
    return NextResponse.json({ error: "Erro ao marcar notificação como lida" }, { status: 500 })
  }
}
