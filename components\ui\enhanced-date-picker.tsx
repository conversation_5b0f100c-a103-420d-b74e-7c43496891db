"use client"

import * as React from "react"
import { format, subDays } from "date-fns"
import { ptBR } from "date-fns/locale"
import { Calendar as CalendarIcon, X } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface EnhancedDatePickerProps {
  date?: Date
  onDateChange?: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function EnhancedDatePicker({
  date,
  onDateChange,
  placeholder = "Selecione uma data",
  className,
  disabled = false
}: EnhancedDatePickerProps) {
  const [open, setOpen] = React.useState(false)

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      // Garantir que a data está no timezone local
      const localDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
      onDateChange?.(localDate)
    } else {
      onDateChange?.(undefined)
    }
    setOpen(false)
  }

  const handleToday = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const today = new Date()
    const localToday = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    onDateChange?.(localToday)
    setOpen(false)
  }

  const handleYesterday = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const yesterday = subDays(new Date(), 1)
    const localYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
    onDateChange?.(localYesterday)
    setOpen(false)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onDateChange?.(undefined)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal relative",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "dd/MM/yyyy", { locale: ptBR }) : placeholder}
          {date && (
            <span
              className="ml-auto h-4 w-4 p-0 hover:bg-gray-100 rounded cursor-pointer flex items-center justify-center"
              onClick={handleClear}
            >
              <X className="h-3 w-3" />
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="flex flex-col">
          {/* Botões de atalho */}
          <div className="flex gap-2 p-3 border-b">
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="flex-1"
            >
              Hoje
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleYesterday}
              className="flex-1"
            >
              Ontem
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClear}
              className="flex-1"
            >
              Limpar
            </Button>
          </div>

          {/* Calendário */}
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleDateSelect}
            initialFocus
            locale={ptBR}
            className="rounded-md"
          />
        </div>
      </PopoverContent>
    </Popover>
  )
}
