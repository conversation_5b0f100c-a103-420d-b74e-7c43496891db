const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createBorderoLogsTable() {
  try {
    console.log('🔧 Criando tabela de logs de borderos...')

    // Verificar se a tabela já existe
    const { data: existingTable, error: checkError } = await supabase
      .from('bordero_logs')
      .select('*')
      .limit(1)

    if (!checkError) {
      console.log('✅ Tabela bordero_logs já existe')
      return
    }

    console.log('📝 Criando tabela bordero_logs...')

    // Criar a tabela usando SQL direto
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS bordero_logs (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        bordero_id UUID NOT NULL REFERENCES borderos(id) ON DELETE CASCADE,
        usuario_id UUID NOT NULL REFERENCES usuarios(id),
        acao VARCHAR(50) NOT NULL,
        detalhes TEXT,
        data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Criar índices para melhor performance
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_bordero_id ON bordero_logs(bordero_id);
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_usuario_id ON bordero_logs(usuario_id);
      CREATE INDEX IF NOT EXISTS idx_bordero_logs_data_hora ON bordero_logs(data_hora);

      -- Habilitar RLS (Row Level Security)
      ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;

      -- Criar política para permitir leitura para usuários autenticados
      CREATE POLICY IF NOT EXISTS "Usuários podem ver logs de borderos" ON bordero_logs
        FOR SELECT USING (auth.role() = 'authenticated');

      -- Criar política para permitir inserção para usuários autenticados
      CREATE POLICY IF NOT EXISTS "Usuários podem criar logs de borderos" ON bordero_logs
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    `

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL })

    if (createError) {
      console.error('❌ Erro ao criar tabela:', createError.message)
      
      // Tentar método alternativo
      console.log('🔄 Tentando método alternativo...')
      
      // Inserir um registro de teste para forçar a criação da tabela
      const { error: insertError } = await supabase
        .from('bordero_logs')
        .insert({
          bordero_id: '00000000-0000-0000-0000-000000000000',
          usuario_id: '00000000-0000-0000-0000-000000000000',
          acao: 'teste',
          detalhes: 'Teste de criação da tabela',
          data_hora: new Date().toISOString()
        })

      if (insertError && !insertError.message.includes('violates foreign key constraint')) {
        console.error('❌ Erro ao criar tabela (método alternativo):', insertError.message)
        console.log('\n⚠️ A tabela bordero_logs precisa ser criada manualmente no Supabase.')
        console.log('\n📝 Execute o seguinte SQL no Supabase:')
        console.log(createTableSQL)
        return
      }

      // Deletar o registro de teste se foi inserido
      await supabase
        .from('bordero_logs')
        .delete()
        .eq('acao', 'teste')
    }

    console.log('✅ Tabela bordero_logs criada com sucesso!')

    // Testar inserção de um log de exemplo
    console.log('\n🧪 Testando inserção de log...')
    
    // Buscar um bordero existente
    const { data: borderos } = await supabase
      .from('borderos')
      .select('id')
      .limit(1)

    // Buscar um usuário existente
    const { data: usuarios } = await supabase
      .from('usuarios')
      .select('id')
      .limit(1)

    if (borderos && borderos.length > 0 && usuarios && usuarios.length > 0) {
      const { data: logTeste, error: logError } = await supabase
        .from('bordero_logs')
        .insert({
          bordero_id: borderos[0].id,
          usuario_id: usuarios[0].id,
          acao: 'criar',
          detalhes: 'Log de teste - criação da tabela',
          data_hora: new Date().toISOString()
        })
        .select()
        .single()

      if (logError) {
        console.error('❌ Erro ao inserir log de teste:', logError.message)
      } else {
        console.log('✅ Log de teste inserido:', logTeste.id)
        
        // Deletar o log de teste
        await supabase
          .from('bordero_logs')
          .delete()
          .eq('id', logTeste.id)
        
        console.log('🧹 Log de teste removido')
      }
    } else {
      console.log('⚠️ Não foi possível testar - sem borderos ou usuários')
    }

    console.log('\n🎉 Configuração da tabela bordero_logs concluída!')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

createBorderoLogsTable()
