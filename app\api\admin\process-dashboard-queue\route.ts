import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function POST(request: Request) {
  try {
    console.log('🔄 [API] Iniciando processamento da fila de atualização do dashboard');
    
    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Verificar se o usuário é administrador
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar dados do usuário para verificar permissões
    const { data: usuario, error: userError } = await supabase
      .from("usuarios")
      .select("role")
      .eq("id", session.user.id)
      .single()

    if (userError || !usuario || usuario.role !== 'admin') {
      return NextResponse.json({ error: "Permissão negada. Apenas administradores podem processar a fila." }, { status: 403 })
    }

    // Processar a fila de atualização
    const { data: result, error: processError } = await supabase
      .rpc('process_dashboard_refresh_queue')

    if (processError) {
      console.error('❌ Erro ao processar fila de atualização:', processError);
      return NextResponse.json({ 
        error: "Erro ao processar fila de atualização", 
        details: processError.message 
      }, { status: 500 })
    }

    console.log(`✅ Fila de atualização processada com sucesso. Resultado:`, result);
    
    return NextResponse.json({ 
      success: true, 
      message: `Fila de atualização processada com sucesso`,
      processed_count: result
    })
  } catch (error: any) {
    console.error('💥 Erro crítico na API de processamento da fila:', error);
    return NextResponse.json({
      error: 'Erro interno do servidor',
      details: error && error.message ? error.message : String(error)
    }, { status: 500 });
  }
}