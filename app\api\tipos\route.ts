import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: tipos, error } = await supabase
      .from("tipos")
      .select("*")
      .order("nome", { ascending: true })

    if (error) {
      console.error("Erro ao buscar tipos:", error)
      return NextResponse.json({ error: "Erro ao buscar tipos" }, { status: 500 })
    }

    return NextResponse.json(tipos)
  } catch (error) {
    console.error("Erro ao buscar tipos:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { nome, descricao } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome do tipo é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe um tipo com este nome
    const { data: existingTipo, error: checkError } = await supabase
      .from("tipos")
      .select("id")
      .eq("nome", nome.trim())
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar tipo:", checkError)
      return NextResponse.json({ error: "Erro ao verificar tipo" }, { status: 500 })
    }

    if (existingTipo) {
      return NextResponse.json({ error: "Já existe um tipo com este nome" }, { status: 400 })
    }

    const { data: tipo, error } = await supabase
      .from("tipos")
      .insert({
        nome: nome.trim(),
        descricao: descricao?.trim() || "",
        ativo: true
      })
      .select()
      .single()

    if (error) {
      console.error("Erro ao criar tipo:", error)
      return NextResponse.json({ error: "Erro ao criar tipo" }, { status: 500 })
    }

    return NextResponse.json(tipo)
  } catch (error) {
    console.error("Erro ao criar tipo:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
