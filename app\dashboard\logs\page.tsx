"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { DateRangePicker } from "@/components/dashboard/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Loader2, Search, RefreshCw, ChevronLeft, ChevronRight, Eye } from "lucide-react"
import { useSupabase } from "@/lib/supabase-provider"
import { toast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { registrarLogCliente } from "@/lib/log-service"
import type { Log<PERSON>cao, LogEntidade } from "@/types/logs"

interface LogAtividade {
  id: number
  usuarios?: {
    id: number
    nome: string
    email: string
  } | null
  acao: string
  entidade: string
  entidade_id: string | null
  detalhes: any
  ip: string | null
  user_agent: string | null
  created_at: string
}

export default function LogsPage() {
  const [logs, setLogs] = useState<LogAtividade[]>([])
  const [totalLogs, setTotalLogs] = useState(0)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [acaoFilter, setAcaoFilter] = useState<string>("all")
  const [entidadeFilter, setEntidadeFilter] = useState<string>("all")
  const [usuarioFilter, setUsuarioFilter] = useState<string>("all")
  const [usuarios, setUsuarios] = useState<{ id: number; nome: string }[]>([])
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(20)
  const [selectedLog, setSelectedLog] = useState<LogAtividade | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)

  const { supabase } = useSupabase()

  // Carregar usuários para o filtro
  useEffect(() => {
    const fetchUsuarios = async () => {
      try {
        const { data, error } = await supabase.from("usuarios").select("id, nome").order("nome")

        if (error) throw error
        setUsuarios(data || [])
      } catch (error) {
        console.error("Erro ao carregar usuários:", error)
      }
    }

    if (supabase) {
      fetchUsuarios()
    }
  }, [supabase])

  // Carregar logs
  useEffect(() => {
    fetchLogs()
  }, [page, limit])

  const fetchLogs = async () => {
    try {
      setLoading(true)

      // Construir URL com parâmetros
      let url = `/api/logs?limit=${limit}&offset=${(page - 1) * limit}`

      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`
      }

      if (dateRange.from && dateRange.to) {
        const fromDate = new Date(dateRange.from)
        fromDate.setHours(0, 0, 0, 0)

        const toDate = new Date(dateRange.to)
        toDate.setHours(23, 59, 59, 999)

        url += `&dataInicio=${fromDate.toISOString()}&dataFim=${toDate.toISOString()}`
      }

      if (acaoFilter !== "all") {
        url += `&acao=${encodeURIComponent(acaoFilter)}`
      }

      if (entidadeFilter !== "all") {
        url += `&entidade=${encodeURIComponent(entidadeFilter)}`
      }

      if (usuarioFilter !== "all") {
        url += `&usuario=${encodeURIComponent(usuarioFilter)}`
      }

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`Erro ao buscar logs: ${response.status}`)
      }

      const data = await response.json()
      setLogs(data.logs || [])
      setTotalLogs(data.count || 0)

      // Se há uma mensagem, mostrar como toast informativo
      if (data.message) {
        toast({
          title: "Informação",
          description: data.message,
          variant: "default",
        })
      }

      // Registrar visualização de logs
      await registrarLogCliente("visualizar" as LogAcao, "sistema" as LogEntidade, undefined, {
        acao: "visualizar_logs",
        filtros: { searchTerm, dateRange, acaoFilter, entidadeFilter, usuarioFilter },
      })
    } catch (error) {
      console.error("Erro ao buscar logs:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os logs de atividade.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setPage(1) // Resetar para a primeira página
    fetchLogs()
  }

  const handleClearFilters = () => {
    setSearchTerm("")
    setDateRange({ from: undefined, to: undefined })
    setAcaoFilter("all")
    setEntidadeFilter("all")
    setUsuarioFilter("all")
    setPage(1)
  }

  const handleViewDetails = (log: LogAtividade) => {
    setSelectedLog(log)
    setIsDetailsOpen(true)
  }

  // Formatar data para exibição
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
  }

  // Traduzir ação para exibição
  const traduzirAcao = (acao: string) => {
    const traducoes: Record<string, string> = {
      criar: "Criação",
      atualizar: "Atualização",
      excluir: "Exclusão",
      visualizar: "Visualização",
      login: "Login",
      logout: "Logout",
      alterar_status: "Alteração de Status",
      exportar: "Exportação",
      importar: "Importação",
      enviar_email: "Envio de Email",
      gerar_relatorio: "Geração de Relatório",
    }

    return traducoes[acao] || acao
  }

  // Traduzir entidade para exibição
  const traduzirEntidade = (entidade: string) => {
    const traducoes: Record<string, string> = {
      bordero: "Bordero",
      secretaria: "Secretaria",
      tipo: "Tipo de Bordero",
      usuario: "Usuário",
      direcionamento: "Direcionamento",
      nivel_acesso: "Nível de Acesso",
      notificacao: "Notificação",
      sistema: "Sistema",
    }

    return traducoes[entidade] || entidade
  }

  // Renderizar badge para ação
  const renderAcaoBadge = (acao: string) => {
    const cores: Record<string, { bg: string; text: string }> = {
      criar: { bg: "bg-green-50", text: "text-green-600" },
      atualizar: { bg: "bg-blue-50", text: "text-blue-600" },
      excluir: { bg: "bg-red-50", text: "text-red-600" },
      visualizar: { bg: "bg-gray-50", text: "text-gray-600" },
      login: { bg: "bg-purple-50", text: "text-purple-600" },
      logout: { bg: "bg-orange-50", text: "text-orange-600" },
      alterar_status: { bg: "bg-yellow-50", text: "text-yellow-600" },
      exportar: { bg: "bg-indigo-50", text: "text-indigo-600" },
      importar: { bg: "bg-pink-50", text: "text-pink-600" },
      enviar_email: { bg: "bg-cyan-50", text: "text-cyan-600" },
      gerar_relatorio: { bg: "bg-emerald-50", text: "text-emerald-600" },
    }

    const cor = cores[acao] || { bg: "bg-gray-50", text: "text-gray-600" }

    return (
      <Badge variant="outline" className={`${cor.bg} ${cor.text} border-transparent`}>
        {traduzirAcao(acao)}
      </Badge>
    )
  }

  // Calcular total de páginas
  const totalPages = Math.ceil(totalLogs / limit)

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Logs de Atividades</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={fetchLogs} disabled={loading} className="gap-1">
            {loading ? <Loader2 className="h-3.5 w-3.5 animate-spin" /> : <RefreshCw className="h-3.5 w-3.5" />}
            <span className="hidden sm:inline">Atualizar</span>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Busca</label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar em logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Período</label>
              <DateRangePicker date={dateRange} onDateChange={setDateRange} />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Ação</label>
              <Select value={acaoFilter} onValueChange={setAcaoFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas as ações" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as ações</SelectItem>
                  <SelectItem value="criar">Criação</SelectItem>
                  <SelectItem value="atualizar">Atualização</SelectItem>
                  <SelectItem value="excluir">Exclusão</SelectItem>
                  <SelectItem value="visualizar">Visualização</SelectItem>
                  <SelectItem value="login">Login</SelectItem>
                  <SelectItem value="logout">Logout</SelectItem>
                  <SelectItem value="alterar_status">Alteração de Status</SelectItem>
                  <SelectItem value="exportar">Exportação</SelectItem>
                  <SelectItem value="importar">Importação</SelectItem>
                  <SelectItem value="enviar_email">Envio de Email</SelectItem>
                  <SelectItem value="gerar_relatorio">Geração de Relatório</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Entidade</label>
              <Select value={entidadeFilter} onValueChange={setEntidadeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todas as entidades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as entidades</SelectItem>
                  <SelectItem value="bordero">Bordero</SelectItem>
                  <SelectItem value="secretaria">Secretaria</SelectItem>
                  <SelectItem value="tipo">Tipo de Bordero</SelectItem>
                  <SelectItem value="usuario">Usuário</SelectItem>
                  <SelectItem value="direcionamento">Direcionamento</SelectItem>
                  <SelectItem value="nivel_acesso">Nível de Acesso</SelectItem>
                  <SelectItem value="notificacao">Notificação</SelectItem>
                  <SelectItem value="sistema">Sistema</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Usuário</label>
              <Select value={usuarioFilter} onValueChange={setUsuarioFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os usuários" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os usuários</SelectItem>
                  {usuarios.map((usuario) => (
                    <SelectItem key={usuario.id} value={usuario.id.toString()}>
                      {usuario.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end space-x-2">
              <Button onClick={handleSearch} disabled={loading} className="gap-2">
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                Aplicar Filtros
              </Button>
              <Button variant="outline" onClick={handleClearFilters} disabled={loading}>
                Limpar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Histórico de Atividades</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex h-[300px] items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <>
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Usuário</TableHead>
                      <TableHead>Ação</TableHead>
                      <TableHead>Entidade</TableHead>
                      <TableHead>ID</TableHead>
                      <TableHead>IP</TableHead>
                      <TableHead className="text-right">Detalhes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {logs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          Nenhum log encontrado
                        </TableCell>
                      </TableRow>
                    ) : (
                      logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="whitespace-nowrap">{formatDate(log.created_at)}</TableCell>
                          <TableCell>{log.usuarios?.nome || "Usuário desconhecido"}</TableCell>
                          <TableCell>{renderAcaoBadge(log.acao)}</TableCell>
                          <TableCell className="capitalize">{traduzirEntidade(log.entidade)}</TableCell>
                          <TableCell>{log.entidade_id || "-"}</TableCell>
                          <TableCell>{log.ip || "-"}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleViewDetails(log)} className="gap-1">
                              <Eye className="h-3.5 w-3.5" />
                              <span className="hidden sm:inline">Detalhes</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Paginação */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Mostrando {(page - 1) * limit + 1} a {Math.min(page * limit, totalLogs)} de {totalLogs} registros
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      disabled={page === 1 || loading}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-sm">
                      Página {page} de {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                      disabled={page === totalPages || loading}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Modal de detalhes */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes do Log</DialogTitle>
            <DialogDescription>Informações detalhadas sobre a atividade registrada</DialogDescription>
          </DialogHeader>

          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">ID do Log</h4>
                  <p>{selectedLog.id}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Data/Hora</h4>
                  <p>{formatDate(selectedLog.created_at)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Usuário</h4>
                  <p>{selectedLog.usuarios?.nome || "Usuário desconhecido"}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Email</h4>
                  <p>{selectedLog.usuarios?.email || "-"}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Ação</h4>
                  <div>{renderAcaoBadge(selectedLog.acao)}</div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">Entidade</h4>
                  <p>{traduzirEntidade(selectedLog.entidade)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">ID da Entidade</h4>
                  <p>{selectedLog.entidade_id || "-"}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground">IP</h4>
                  <p>{selectedLog.ip || "-"}</p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground">User Agent</h4>
                <p className="text-sm break-words">{selectedLog.user_agent || "-"}</p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground">Detalhes</h4>
                <pre className="mt-2 rounded-md bg-muted p-4 overflow-auto text-sm">
                  {JSON.stringify(selectedLog.detalhes, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
