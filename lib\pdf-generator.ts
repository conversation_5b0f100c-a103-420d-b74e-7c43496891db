import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import { formatCurrency } from './currency-utils'

interface BorderoData {
  id: string
  bordero_cod: string
  nome_empresa: string
  valor: number
  status: string
  data: string
  secretaria: { nome: string }
  tipo: { nome: string }
  direcionamento?: { nome: string }
}

interface ReportData {
  borderos: BorderoData[]
  filtros: {
    dataInicio?: string
    dataFim?: string
    secretaria?: string
    tipo?: string
    status?: string
  }
  usuario: {
    nome: string
    email: string
  }
}

export class PDFGenerator {
  private doc: jsPDF
  private pageHeight: number
  private pageWidth: number
  private margin: number
  private currentY: number

  constructor() {
    // Configurar explicitamente para formato retrato A4
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })
    this.pageHeight = this.doc.internal.pageSize.getHeight()
    this.pageWidth = this.doc.internal.pageSize.getWidth()
    this.margin = 15
    this.currentY = this.margin

    // Configurar fonte padrão
    this.doc.setFont('helvetica')
  }

  generateReport(data: ReportData): void {
    this.addHeader(data.usuario)
    this.addFilters(data.filtros)
    this.addSummary(data.borderos)
    this.addTable(data.borderos)
    this.addFooter()
  }

  private addHeader(usuario: { nome: string; email: string }): void {
    // Fundo do cabeçalho institucional
    this.doc.setFillColor(54, 86, 229) // #3656e5 - cor institucional
    this.doc.rect(0, 0, this.pageWidth, 65, 'F')

    // Logo/Brasão estilizado
    this.doc.setFillColor(255, 255, 255)
    this.doc.rect(this.margin, this.margin, 50, 30, 'F')

    this.doc.setTextColor(54, 86, 229)
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('PREFEITURA', this.margin + 3, this.margin + 10)
    this.doc.setFontSize(8)
    this.doc.text('MUNICIPAL', this.margin + 3, this.margin + 17)
    this.doc.text('CRM BORDERÔ', this.margin + 3, this.margin + 24)

    // Título principal
    this.doc.setTextColor(255, 255, 255)
    this.doc.setFontSize(22)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('RELATÓRIO DE BORDERÔS', this.margin + 60, this.margin + 18)

    // Informações do relatório
    this.doc.setFontSize(9)
    this.doc.setFont('helvetica', 'normal')
    const agora = new Date()
    this.doc.text(`Gerado por: ${usuario.nome}`, this.margin + 60, this.margin + 30)
    this.doc.text(`Data/Hora: ${agora.toLocaleDateString('pt-BR')} às ${agora.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`, this.margin + 60, this.margin + 38)

    // Data destacada no canto direito
    this.doc.setFontSize(11)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(agora.toLocaleDateString('pt-BR'), this.pageWidth - this.margin - 30, this.margin + 18)

    // Linha separadora
    this.doc.setDrawColor(255, 255, 255)
    this.doc.setLineWidth(1)
    this.doc.line(this.margin, 65, this.pageWidth - this.margin, 65)

    this.currentY = 75
  }



  private addFilters(filtros: ReportData['filtros']): void {
    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(0, 0, 0)
    this.doc.text('Filtros Aplicados:', this.margin, this.currentY)
    this.currentY += 10

    this.doc.setFontSize(10)
    this.doc.setFont('helvetica', 'normal')

    if (filtros.dataInicio || filtros.dataFim) {
      const periodo = `${filtros.dataInicio || 'Início'} até ${filtros.dataFim || 'Fim'}`
      this.doc.text(`• Período: ${periodo}`, this.margin + 5, this.currentY)
      this.currentY += 6
    }

    if (filtros.secretaria) {
      this.doc.text(`• Secretaria: ${filtros.secretaria}`, this.margin + 5, this.currentY)
      this.currentY += 6
    }

    if (filtros.tipo) {
      this.doc.text(`• Tipo: ${filtros.tipo}`, this.margin + 5, this.currentY)
      this.currentY += 6
    }

    if (filtros.status) {
      this.doc.text(`• Status: ${filtros.status}`, this.margin + 5, this.currentY)
      this.currentY += 6
    }

    this.currentY += 10
  }

  private addSummary(borderos: BorderoData[]): void {
    const total = borderos.length
    const valorTotal = borderos.reduce((sum, b) => sum + b.valor, 0)

    // Resumo por status
    const statusCount = borderos.reduce((acc, b) => {
      acc[b.status] = (acc[b.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Resumo por secretaria
    const secretariaCount = borderos.reduce((acc, b) => {
      acc[b.secretaria.nome] = (acc[b.secretaria.nome] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    this.doc.setFontSize(12)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Resumo Geral:', this.margin, this.currentY)
    this.currentY += 10

    this.doc.setFontSize(10)
    this.doc.setFont('helvetica', 'normal')
    this.doc.text(`Total de Borderôs: ${total}`, this.margin + 5, this.currentY)
    this.currentY += 6
    this.doc.text(`Valor Total: ${formatCurrency(valorTotal)}`, this.margin + 5, this.currentY)
    this.currentY += 15

    // Resumo por Status
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Por Status:', this.margin, this.currentY)
    this.currentY += 8

    this.doc.setFont('helvetica', 'normal')
    Object.entries(statusCount).forEach(([status, count]) => {
      this.doc.text(`• ${status}: ${count}`, this.margin + 5, this.currentY)
      this.currentY += 6
    })

    this.currentY += 10

    // Resumo por Secretaria
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Por Secretaria:', this.margin, this.currentY)
    this.currentY += 8

    this.doc.setFont('helvetica', 'normal')
    Object.entries(secretariaCount).forEach(([secretaria, count]) => {
      this.doc.text(`• ${secretaria}: ${count}`, this.margin + 5, this.currentY)
      this.currentY += 6
    })

    this.currentY += 15
  }

  private addTable(borderos: BorderoData[]): void {
    const tableData = borderos.map(bordero => [
      bordero.bordero_cod,
      bordero.nome_empresa.substring(0, 35) + (bordero.nome_empresa.length > 35 ? '...' : ''),
      formatCurrency(bordero.valor),
      this.getStatusDisplay(bordero.status),
      bordero.secretaria.nome.substring(0, 20) + (bordero.secretaria.nome.length > 20 ? '...' : ''),
      bordero.tipo?.nome || 'N/A',
      new Date(bordero.data).toLocaleDateString('pt-BR')
    ])

    autoTable(this.doc, {
      startY: this.currentY,
      head: [['Código', 'Empresa', 'Valor (R$)', 'Status', 'Secretaria', 'Tipo', 'Data']],
      body: tableData,
      theme: 'grid',
      styles: {
        fontSize: 9,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.5,
        textColor: [30, 41, 59] // slate-800
      },
      headStyles: {
        fillColor: [54, 86, 229], // #3656e5 - cor institucional
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 10,
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252] // slate-50
      },
      columnStyles: {
        0: { cellWidth: 20, halign: 'center' }, // Código
        1: { cellWidth: 55 }, // Empresa (maior)
        2: { cellWidth: 25, halign: 'right' }, // Valor
        3: { cellWidth: 18, halign: 'center' }, // Status
        4: { cellWidth: 30 }, // Secretaria
        5: { cellWidth: 20 }, // Tipo
        6: { cellWidth: 18, halign: 'center' } // Data
      },
      margin: { left: this.margin, right: this.margin },
      didDrawPage: () => {
        this.addPageNumber()
      }
    })
  }

  private getStatusDisplay(status: string): string {
    const statusMap: Record<string, string> = {
      'novo': 'Novo',
      'analise': 'Análise',
      'assinado': 'Assinado',
      'pago': 'Pago',
      'corrigir': 'Corrigir',
      'cancelado': 'Cancelado'
    }
    return statusMap[status] || status
  }

  private addFooter(): void {
    const pageCount = this.doc.getNumberOfPages()

    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i)

      // Fundo do rodapé
      this.doc.setFillColor(248, 250, 252) // slate-50
      this.doc.rect(0, this.pageHeight - 25, this.pageWidth, 25, 'F')

      // Linha separadora superior
      this.doc.setDrawColor(54, 86, 229)
      this.doc.setLineWidth(0.5)
      this.doc.line(this.margin, this.pageHeight - 25, this.pageWidth - this.margin, this.pageHeight - 25)

      // Texto institucional à esquerda
      this.doc.setFontSize(8)
      this.doc.setFont('helvetica', 'normal')
      this.doc.setTextColor(71, 85, 105) // slate-600
      this.doc.text('Gerado via CRM de Borderôs - Prefeitura Municipal', this.margin, this.pageHeight - 15)

      // Página no centro
      this.doc.setFont('helvetica', 'bold')
      this.doc.text(`Página ${i} de ${pageCount}`, this.pageWidth / 2 - 15, this.pageHeight - 15)

      // Data/hora à direita
      this.doc.setFont('helvetica', 'normal')
      const agora = new Date()
      this.doc.text(`${agora.toLocaleDateString('pt-BR')} - ${agora.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`, this.pageWidth - this.margin - 45, this.pageHeight - 15)

      // Linha de identificação
      this.doc.setFontSize(7)
      this.doc.setTextColor(100, 116, 139) // slate-500
      this.doc.text('Documento gerado automaticamente pelo sistema', this.pageWidth / 2 - 35, this.pageHeight - 8)
    }
  }

  private addPageNumber(): void {
    const pageNumber = this.doc.getCurrentPageInfo().pageNumber
    const totalPages = this.doc.getNumberOfPages()

    this.doc.setFontSize(8)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(128, 128, 128)
    this.doc.text(
      `Página ${pageNumber} de ${totalPages}`,
      this.pageWidth - this.margin - 20,
      this.pageHeight - 10
    )
  }

  save(filename: string = 'relatorio-borderos.pdf'): void {
    this.doc.save(filename)
  }

  getBlob(): Blob {
    return this.doc.output('blob')
  }

  getDataUri(): string {
    return this.doc.output('datauristring')
  }
}
