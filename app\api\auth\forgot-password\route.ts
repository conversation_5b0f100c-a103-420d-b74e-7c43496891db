import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { sendEmail, generateVerificationCode, getPasswordResetEmailTemplate } from "@/lib/email"

export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({ error: "Email é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se o usuário existe
    const { data: usuario, error: userError } = await supabase
      .from("usuarios")
      .select("id, nome, email")
      .eq("email", email)
      .single()

    if (userError || !usuario) {
      // Por segurança, não revelar se o email existe ou não
      return NextResponse.json({ 
        message: "Se o email existir em nosso sistema, você receberá um código de verificação." 
      })
    }

    // Gerar código de verificação
    const code = generateVerificationCode()
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000) // 15 minutos

    // Salvar código na tabela de verificação
    const { error: insertError } = await supabase
      .from("codigo_verificacao")
      .insert({
        email: usuario.email,
        codigo: code,
        tipo: "reset_password",
        expira_em: expiresAt.toISOString(),
        usado: false
      })

    if (insertError) {
      console.error("Erro ao salvar código de verificação:", insertError)
      return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
    }

    // Enviar email com o código
    const emailResult = await sendEmail({
      to: usuario.email,
      subject: "Redefinir Senha - CRM Bordero",
      html: getPasswordResetEmailTemplate(code, usuario.nome)
    })

    if (!emailResult.success) {
      console.error("Erro ao enviar email:", emailResult.error)
      // Mesmo se o email falhar, não revelar isso ao usuário por segurança
    }

    return NextResponse.json({ 
      message: "Se o email existir em nosso sistema, você receberá um código de verificação." 
    })

  } catch (error) {
    console.error("Erro ao processar solicitação de redefinição:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
