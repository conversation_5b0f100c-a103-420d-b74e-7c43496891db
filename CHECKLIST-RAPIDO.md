# ⚡ Checklist Rápido - Deploy CRM Borderos

## 🚀 COMANDOS ESSENCIAIS (Copy & Paste)

### 1. Preparar VPS
```bash
# Conectar SSH
ssh root@SEU_IP_VPS

# Atualizar sistema
apt update && apt upgrade -y

# Instalar Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Instalar ferramentas
npm install -g pm2
apt install nginx git certbot python3-certbot-nginx ufw -y
```

### 2. Clonar e Configurar Projeto
```bash
# Clonar projeto
cd /var/www
git clone SEU_REPOSITORIO bordero
cd bordero

# Instalar dependências
npm install

# Criar .env.local (edite com suas credenciais)
nano .env.local
```

### 3. Configurar .env.local
```env
NEXT_PUBLIC_SUPABASE_URL=https://SEU_PROJETO.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=SUA_CHAVE_ANON
SUPABASE_SERVICE_ROLE_KEY=SUA_CHAVE_SERVICE_ROLE
NEXT_PUBLIC_PUSHER_KEY=SUA_PUSHER_KEY
PUSHER_SECRET=SEU_PUSHER_SECRET
NEXT_PUBLIC_PUSHER_CLUSTER=us2
NEXTAUTH_URL=https://bordero.claudiomt.com.br
NEXT_PUBLIC_APP_URL=https://bordero.claudiomt.com.br
NEXTAUTH_SECRET=$(openssl rand -base64 32)
```

### 4. Build e PM2
```bash
# Build da aplicação
npm run build

# Criar configuração PM2
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'bordero-crm',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/bordero',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: { NODE_ENV: 'production', PORT: 3000 }
  }]
}
EOF

# Iniciar com PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 5. Configurar Nginx
```bash
# Criar configuração do site
cat > /etc/nginx/sites-available/bordero.claudiomt.com.br << 'EOF'
server {
    listen 80;
    server_name bordero.claudiomt.com.br www.bordero.claudiomt.com.br;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Ativar site
ln -s /etc/nginx/sites-available/bordero.claudiomt.com.br /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

### 6. SSL e Firewall
```bash
# Configurar SSL
certbot --nginx -d bordero.claudiomt.com.br -d www.bordero.claudiomt.com.br

# Configurar firewall
ufw allow ssh
ufw allow 'Nginx Full'
ufw enable
```

---

## 📋 CHECKLIST DE VERIFICAÇÃO

### ✅ Pré-Deploy
- [ ] **VPS criada na Hostinger**
- [ ] **Domínio bordero.claudiomt.com.br configurado**
- [ ] **Projeto Supabase criado**
- [ ] **App Pusher criado**
- [ ] **Credenciais anotadas**

### ✅ Durante Deploy
- [ ] **SSH conectado**
- [ ] **Node.js 18+ instalado** (`node --version`)
- [ ] **PM2 instalado** (`pm2 --version`)
- [ ] **Nginx instalado** (`nginx -v`)
- [ ] **Projeto clonado** (`ls /var/www/bordero`)
- [ ] **Dependências instaladas** (`npm list`)
- [ ] **.env.local configurado** (`cat .env.local`)
- [ ] **Build concluído** (`ls .next`)
- [ ] **PM2 rodando** (`pm2 status`)
- [ ] **Nginx configurado** (`nginx -t`)
- [ ] **SSL configurado** (`certbot certificates`)

### ✅ Pós-Deploy
- [ ] **Site carrega**: https://bordero.claudiomt.com.br
- [ ] **Redirecionamento HTTP→HTTPS funciona**
- [ ] **Login funciona**
- [ ] **Dashboard carrega**
- [ ] **Notificações funcionam**
- [ ] **Sem erros no console**

---

## 🔧 COMANDOS DE MANUTENÇÃO

### Verificar Status
```bash
# Status geral
pm2 status && systemctl status nginx

# Logs em tempo real
pm2 logs bordero-crm --lines 20

# Verificar recursos
free -h && df -h
```

### Atualizar Código
```bash
cd /var/www/bordero
git pull origin main
npm install
npm run build
pm2 restart bordero-crm
```

### Reiniciar Serviços
```bash
# Reiniciar aplicação
pm2 restart bordero-crm

# Reiniciar Nginx
systemctl restart nginx

# Reiniciar tudo
pm2 restart all && systemctl restart nginx
```

---

## 🚨 RESOLUÇÃO RÁPIDA DE PROBLEMAS

### Site não carrega
```bash
# 1. Verificar se aplicação está rodando
pm2 status

# 2. Se não estiver, iniciar
pm2 start ecosystem.config.js

# 3. Verificar Nginx
systemctl status nginx

# 4. Se necessário, reiniciar
systemctl restart nginx
```

### Erro 502 Bad Gateway
```bash
# 1. Verificar logs
pm2 logs bordero-crm --lines 10

# 2. Reiniciar aplicação
pm2 restart bordero-crm

# 3. Verificar porta 3000
netstat -tlnp | grep :3000
```

### SSL expirado
```bash
# Renovar certificado
certbot renew
systemctl reload nginx
```

### Falta de espaço
```bash
# Verificar espaço
df -h

# Limpar logs
pm2 flush
journalctl --vacuum-time=7d

# Limpar cache
npm cache clean --force
```

---

## 📞 SUPORTE RÁPIDO

### Logs Importantes
```bash
# Logs da aplicação
pm2 logs bordero-crm

# Logs do Nginx
tail -f /var/log/nginx/error.log

# Logs do sistema
journalctl -u nginx -f
```

### Informações do Sistema
```bash
# Versões
node --version
npm --version
pm2 --version
nginx -v

# Recursos
free -h
df -h
uptime
```

### Testar Conectividade
```bash
# Testar aplicação local
curl -I http://localhost:3000

# Testar domínio
curl -I https://bordero.claudiomt.com.br

# Testar Supabase
curl -I https://SEU_PROJETO.supabase.co
```

---

## 🎯 CONFIGURAÇÃO DNS (Hostinger)

### Registros Necessários
```
Tipo: A
Nome: @
Valor: IP_DA_VPS
TTL: 3600

Tipo: A
Nome: www
Valor: IP_DA_VPS
TTL: 3600
```

---

## 📱 CONTATOS DE EMERGÊNCIA

- **Hostinger**: Chat 24/7 no painel
- **Supabase**: Discord - https://discord.supabase.com
- **Pusher**: <EMAIL>

---

## 🎉 SUCESSO!

Se todos os itens do checklist estão ✅, seu sistema está funcionando em:

**🌐 https://bordero.claudiomt.com.br**

### Credenciais de Acesso
- **URL**: https://bordero.claudiomt.com.br
- **Admin**: <EMAIL>
- **Primeiro acesso**: Criar conta via interface

**Sistema CRM de Borderos deployado com sucesso! 🚀**
