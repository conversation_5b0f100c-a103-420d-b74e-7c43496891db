import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET() {
  try {
    // Verificar autenticação
    const cookieStore = await cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Usuário não autenticado" }, { status: 401 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar secretarias
    const { data: secretarias, error: secretariasError } = await supabase
      .from("secretarias")
      .select("id, nome")
      .order("nome")

    if (secretariasError) {
      console.error("Erro ao buscar secretarias:", secretariasError)
      return NextResponse.json({ error: "Erro ao buscar secretarias" }, { status: 500 })
    }

    // Buscar tipos
    const { data: tipos, error: tiposError } = await supabase
      .from("tipos")
      .select("id, nome")
      .order("nome")

    if (tiposError) {
      console.error("Erro ao buscar tipos:", tiposError)
      return NextResponse.json({ error: "Erro ao buscar tipos" }, { status: 500 })
    }

    // Buscar valor máximo para o slider
    const { data: maxValorData, error: maxValorError } = await supabase
      .from("borderos")
      .select("valor")
      .order("valor", { ascending: false })
      .limit(1)

    if (maxValorError) {
      console.error("Erro ao buscar valor máximo:", maxValorError)
      return NextResponse.json({ error: "Erro ao buscar valor máximo" }, { status: 500 })
    }

    const valorMaximo = maxValorData && maxValorData.length > 0 
      ? Math.ceil(maxValorData[0].valor / 100000) * 100000 
      : 5000000

    return NextResponse.json({
      secretarias: secretarias || [],
      tipos: tipos || [],
      valorMaximo
    })
  } catch (error) {
    console.error("Erro na API de dados básicos:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
