"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase-client"
import type { User } from "@supabase/supabase-js"

interface AuthState {
  user: User | null
  loading: boolean
  isAuthenticated: boolean
}

export function useAuth(redirectTo?: string) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    isAuthenticated: false
  })
  const router = useRouter()
  const supabase = getSupabaseClient()

  useEffect(() => {
    let mounted = true

    const checkAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (!mounted) return

        if (error) {
          console.error("Erro ao verificar autenticação:", error)
          setAuthState({
            user: null,
            loading: false,
            isAuthenticated: false
          })
          
          if (redirectTo) {
            router.push(redirectTo)
          }
          return
        }

        if (session?.user) {
          setAuthState({
            user: session.user,
            loading: false,
            isAuthenticated: true
          })
        } else {
          setAuthState({
            user: null,
            loading: false,
            isAuthenticated: false
          })
          
          if (redirectTo) {
            router.push(redirectTo)
          }
        }
      } catch (error) {
        console.error("Erro inesperado na verificação de auth:", error)
        if (!mounted) return
        
        setAuthState({
          user: null,
          loading: false,
          isAuthenticated: false
        })
        
        if (redirectTo) {
          router.push(redirectTo)
        }
      }
    }

    checkAuth()

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        console.log("Auth state changed:", event, session?.user?.id)
        
        if (session?.user) {
          setAuthState({
            user: session.user,
            loading: false,
            isAuthenticated: true
          })
        } else {
          setAuthState({
            user: null,
            loading: false,
            isAuthenticated: false
          })
          
          if (redirectTo && event === 'SIGNED_OUT') {
            router.push(redirectTo)
          }
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [router, redirectTo, supabase.auth])

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setAuthState({
        user: null,
        loading: false,
        isAuthenticated: false
      })
      
      if (redirectTo) {
        router.push(redirectTo)
      }
    } catch (error) {
      console.error("Erro ao fazer logout:", error)
    }
  }

  return {
    ...authState,
    signOut
  }
}

// Hook para páginas que requerem autenticação
export function useRequireAuth() {
  return useAuth("/")
}

// Hook para páginas que requerem que o usuário NÃO esteja autenticado (como login)
export function useRequireNoAuth() {
  const authState = useAuth("/dashboard")
  
  // Inverter a lógica para páginas de login
  return {
    ...authState,
    shouldRedirect: authState.isAuthenticated
  }
}
