import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: secretarias, error } = await supabase
      .from("secretarias")
      .select("*")
      .order("nome", { ascending: true })

    if (error) {
      console.error("Erro ao buscar secretarias:", error)
      return NextResponse.json({ error: "Erro ao buscar secretarias" }, { status: 500 })
    }

    return NextResponse.json(secretarias)
  } catch (error) {
    console.error("Erro ao buscar secretarias:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { nome, descricao } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome da secretaria é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Gerar slug a partir do nome
    const slug = nome
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Remove acentos
      .replace(/[^a-z0-9\s-]/g, "") // Remove caracteres especiais
      .replace(/\s+/g, "-") // Substitui espaços por hífens
      .replace(/-+/g, "-") // Remove hífens duplicados
      .trim()

    // Verificar se o slug já existe
    const { data: existingSecretaria, error: checkError } = await supabase
      .from("secretarias")
      .select("id")
      .eq("slug", slug)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar slug:", checkError)
      return NextResponse.json({ error: "Erro ao verificar slug" }, { status: 500 })
    }

    if (existingSecretaria) {
      return NextResponse.json({ error: "Já existe uma secretaria com este nome" }, { status: 400 })
    }

    const { data: secretaria, error } = await supabase
      .from("secretarias")
      .insert({
        nome: nome.trim(),
        slug,
        valores_total: 0
      })
      .select()
      .single()

    if (error) {
      console.error("Erro ao criar secretaria:", error)
      return NextResponse.json({ error: "Erro ao criar secretaria" }, { status: 500 })
    }

    return NextResponse.json(secretaria)
  } catch (error) {
    console.error("Erro ao criar secretaria:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "ID da secretaria é obrigatório" }, { status: 400 })
    }

    const body = await request.json()
    const { nome } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome da secretaria é obrigatório" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Gerar novo slug se o nome mudou
    const slug = nome
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim()

    // Verificar se o slug já existe (exceto para a própria secretaria)
    const { data: existingSecretaria, error: checkError } = await supabase
      .from("secretarias")
      .select("id")
      .eq("slug", slug)
      .neq("id", id)
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar slug:", checkError)
      return NextResponse.json({ error: "Erro ao verificar slug" }, { status: 500 })
    }

    if (existingSecretaria) {
      return NextResponse.json({ error: "Já existe uma secretaria com este nome" }, { status: 400 })
    }

    const { data: secretaria, error } = await supabase
      .from("secretarias")
      .update({
        nome: nome.trim(),
        slug,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .select()
      .single()

    if (error) {
      console.error("Erro ao atualizar secretaria:", error)
      return NextResponse.json({ error: "Erro ao atualizar secretaria" }, { status: 500 })
    }

    return NextResponse.json(secretaria)
  } catch (error) {
    console.error("Erro ao atualizar secretaria:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
