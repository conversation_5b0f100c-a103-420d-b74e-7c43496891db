import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import nodemailer from "nodemailer"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { email_destino } = body

    if (!email_destino) {
      return NextResponse.json({ error: "Email de destino é obrigatório" }, { status: 400 })
    }

    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar configurações SMTP
    const { data: config, error: configError } = await supabaseAdmin
      .from("smtp_config")
      .select("*")
      .limit(1)
      .single()

    if (configError || !config) {
      return NextResponse.json({ error: "Configurações SMTP não encontradas" }, { status: 404 })
    }

    // Buscar dados do usuário
    const { data: usuario } = await supabaseAdmin
      .from("usuarios")
      .select("nome")
      .eq("id", session.user.id)
      .single()

    // Configurar transporter com configurações otimizadas para Brevo
    const transporterConfig = {
      host: config.host,
      port: config.port,
      secure: config.port === 465, // true apenas para porta 465
      requireTLS: config.port !== 465, // true para portas diferentes de 465
      auth: {
        user: config.email_remetente,
        pass: config.senha,
      },
      debug: true, // Habilitar debug
      logger: true, // Habilitar logs
      connectionTimeout: 60000, // 60 segundos
      greetingTimeout: 30000, // 30 segundos
      socketTimeout: 60000, // 60 segundos
      // Configurações específicas para Brevo
      tls: {
        ciphers: 'SSLv3',
        rejectUnauthorized: false
      }
    }

    console.log("🔧 Configuração do transporter:", {
      host: transporterConfig.host,
      port: transporterConfig.port,
      secure: transporterConfig.secure,
      requireTLS: transporterConfig.requireTLS,
      user: transporterConfig.auth.user
    })

    const transporter = nodemailer.createTransporter(transporterConfig)

    // Verificar conexão
    await transporter.verify()

    console.log("🔄 Iniciando envio de email de teste...")
    console.log("📧 Configurações:", {
      host: config.host,
      port: config.port,
      secure: config.secure,
      from: config.email_remetente,
      to: email_destino
    })

    // Preparar dados do email
    const emailData = {
      from: `"CRM Bordero" <${config.email_remetente}>`,
      to: email_destino,
      replyTo: config.email_remetente,
      subject: "✅ Teste de Configuração SMTP - CRM Bordero",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">🎉 Teste de SMTP Realizado com Sucesso!</h2>

          <p>Olá!</p>

          <p>Este é um email de teste enviado pelo sistema <strong>CRM Bordero</strong> para verificar se as configurações SMTP estão funcionando corretamente.</p>

          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #374151;">📋 Detalhes do Teste:</h3>
            <ul style="color: #6b7280;">
              <li><strong>Data/Hora:</strong> ${new Date().toLocaleString('pt-BR')}</li>
              <li><strong>Usuário:</strong> ${usuario?.nome || 'Sistema'}</li>
              <li><strong>Servidor SMTP:</strong> ${config.host}:${config.port}</li>
              <li><strong>Segurança:</strong> ${config.secure ? 'SSL/TLS' : 'STARTTLS'}</li>
            </ul>
          </div>

          <p>Se você recebeu este email, significa que:</p>
          <ul>
            <li>✅ As configurações SMTP estão corretas</li>
            <li>✅ O servidor de email está respondendo</li>
            <li>✅ O sistema pode enviar emails automaticamente</li>
          </ul>

          <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
            Este é um email automático gerado pelo sistema CRM Bordero.
          </p>
        </div>
      `,
      // Configurações adicionais para melhor entrega
      headers: {
        'X-Mailer': 'CRM Bordero System',
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal'
      }
    }

    console.log("📧 Dados do email a ser enviado:", {
      from: emailData.from,
      to: emailData.to,
      subject: emailData.subject
    })

    // Enviar email de teste
    console.log("🚀 Iniciando envio do email...")
    const startTime = Date.now()

    const info = await transporter.sendMail(emailData)

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log("✅ Email enviado com sucesso!")
    console.log(`⏱️ Tempo de envio: ${duration}ms`)
    console.log("📨 Resposta completa do servidor:", {
      messageId: info.messageId,
      response: info.response,
      accepted: info.accepted,
      rejected: info.rejected,
      pending: info.pending,
      envelope: info.envelope,
      duration: `${duration}ms`
    })

    // Verificar se o email foi aceito
    if (info.rejected && info.rejected.length > 0) {
      console.warn("⚠️ Alguns emails foram rejeitados:", info.rejected)
    }

    if (info.accepted && info.accepted.length > 0) {
      console.log("✅ Emails aceitos pelo servidor:", info.accepted)
    }

    // Registrar no log do sistema
    try {
      await supabaseAdmin
        .from("sistema_logs")
        .insert({
          usuario_id: session.user.id,
          acao: "test",
          entidade: "smtp",
          detalhes: {
            email_destino,
            message_id: info.messageId,
            response: info.response,
            accepted: info.accepted,
            rejected: info.rejected,
            host: config.host,
            port: config.port,
            status: "success"
          }
        })
    } catch (logError) {
      console.error("Erro ao registrar log:", logError)
    }

    return NextResponse.json({
      success: true,
      message: "Email de teste enviado com sucesso!",
      debug: {
        messageId: info.messageId,
        response: info.response,
        accepted: info.accepted,
        rejected: info.rejected,
        pending: info.pending,
        envelope: info.envelope,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      },
      config: {
        host: config.host,
        port: config.port,
        secure: config.port === 465,
        from: config.email_remetente,
        to: email_destino
      },
      preview: nodemailer.getTestMessageUrl(info)
    })
  } catch (error: any) {
    console.error("Erro ao testar SMTP:", error)

    // Registrar erro no log
    try {
      const cookieStore = await cookies()
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        const supabaseAdmin = createClient(
          process.env.SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!,
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          }
        )

        await supabaseAdmin
          .from("sistema_logs")
          .insert({
            usuario_id: session.user.id,
            acao: "error",
            entidade: "smtp_test",
            detalhes: {
              error: error.message,
              code: error.code
            }
          })
      }
    } catch (logError) {
      console.error("Erro ao registrar log de erro:", logError)
    }

    return NextResponse.json({
      error: "Erro ao enviar email de teste",
      details: error.message
    }, { status: 500 })
  }
}
