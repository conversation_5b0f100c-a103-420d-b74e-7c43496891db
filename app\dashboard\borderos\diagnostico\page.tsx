"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  FileText, 
  Settings,
  RefreshCw,
  ArrowLeft
} from "lucide-react"
import Link from "next/link"

interface DiagnosticResult {
  name: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function DiagnosticoBorderosPage() {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([])
  const [loading, setLoading] = useState(false)
  const [lastCheck, setLastCheck] = useState<Date | null>(null)
  const { toast } = useToast()

  const runDiagnostics = async () => {
    setLoading(true)
    const results: DiagnosticResult[] = []

    try {
      // 1. Verificar estrutura da tabela borderos
      try {
        const response = await fetch('/api/borderos?limit=1')
        if (response.ok) {
          results.push({
            name: "Tabela Borderos",
            status: "success",
            message: "Tabela borderos acessível e funcionando"
          })
        } else {
          results.push({
            name: "Tabela Borderos",
            status: "error",
            message: "Erro ao acessar tabela borderos"
          })
        }
      } catch (error) {
        results.push({
          name: "Tabela Borderos",
          status: "error",
          message: "Falha na conexão com a tabela borderos"
        })
      }

      // 2. Verificar secretarias
      try {
        const response = await fetch('/api/secretarias')
        if (response.ok) {
          const data = await response.json()
          if (data.length > 0) {
            results.push({
              name: "Secretarias",
              status: "success",
              message: `${data.length} secretarias encontradas`,
              details: data.slice(0, 3)
            })
          } else {
            results.push({
              name: "Secretarias",
              status: "warning",
              message: "Nenhuma secretaria cadastrada"
            })
          }
        }
      } catch (error) {
        results.push({
          name: "Secretarias",
          status: "error",
          message: "Erro ao verificar secretarias"
        })
      }

      // 3. Verificar tipos
      try {
        const response = await fetch('/api/tipos')
        if (response.ok) {
          const data = await response.json()
          if (data.length > 0) {
            results.push({
              name: "Tipos de Bordero",
              status: "success",
              message: `${data.length} tipos encontrados`,
              details: data.slice(0, 3)
            })
          } else {
            results.push({
              name: "Tipos de Bordero",
              status: "warning",
              message: "Nenhum tipo de bordero cadastrado"
            })
          }
        }
      } catch (error) {
        results.push({
          name: "Tipos de Bordero",
          status: "error",
          message: "Erro ao verificar tipos"
        })
      }

      // 4. Verificar usuários
      try {
        const response = await fetch('/api/usuarios')
        if (response.ok) {
          const data = await response.json()
          if (data.usuarios && data.usuarios.length > 0) {
            results.push({
              name: "Usuários",
              status: "success",
              message: `${data.usuarios.length} usuários encontrados`
            })
          } else {
            results.push({
              name: "Usuários",
              status: "warning",
              message: "Poucos usuários cadastrados"
            })
          }
        }
      } catch (error) {
        results.push({
          name: "Usuários",
          status: "error",
          message: "Erro ao verificar usuários"
        })
      }

      // 5. Teste de importação simulada
      try {
        const testData = {
          borderos: [{
            bordero_cod: `TEST-${Date.now()}`,
            nome_empresa: "Empresa Teste",
            valor: 1000.00,
            data: new Date().toISOString(),
            status: "novo"
          }]
        }

        const response = await fetch('/api/borderos/import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testData)
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success > 0) {
            results.push({
              name: "Teste de Importação",
              status: "success",
              message: "Sistema de importação funcionando corretamente"
            })
            
            // Limpar o bordero de teste
            try {
              await fetch(`/api/borderos?search=TEST-${Date.now()}`, {
                method: 'DELETE'
              })
            } catch (cleanupError) {
              console.log("Erro ao limpar teste (não crítico):", cleanupError)
            }
          } else {
            results.push({
              name: "Teste de Importação",
              status: "warning",
              message: "Sistema de importação com problemas",
              details: result.details
            })
          }
        } else {
          results.push({
            name: "Teste de Importação",
            status: "error",
            message: "Falha no teste de importação"
          })
        }
      } catch (error) {
        results.push({
          name: "Teste de Importação",
          status: "error",
          message: "Erro no teste de importação"
        })
      }

      setDiagnostics(results)
      setLastCheck(new Date())

      const errors = results.filter(r => r.status === 'error').length
      const warnings = results.filter(r => r.status === 'warning').length

      if (errors === 0 && warnings === 0) {
        toast({
          title: "✅ Diagnóstico Completo",
          description: "Todos os sistemas estão funcionando corretamente"
        })
      } else if (errors > 0) {
        toast({
          title: "❌ Problemas Encontrados",
          description: `${errors} erros e ${warnings} avisos detectados`,
          variant: "destructive"
        })
      } else {
        toast({
          title: "⚠️ Avisos Encontrados",
          description: `${warnings} avisos detectados - sistema funcional`,
          variant: "default"
        })
      }

    } catch (error) {
      console.error("Erro no diagnóstico:", error)
      toast({
        title: "Erro no Diagnóstico",
        description: "Falha ao executar diagnóstico completo",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runDiagnostics()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">OK</Badge>
      case 'error':
        return <Badge variant="destructive">ERRO</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">AVISO</Badge>
      default:
        return <Badge variant="outline">DESCONHECIDO</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/borderos">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Diagnóstico de Importação</h1>
          <p className="text-muted-foreground">
            Verificação do sistema para identificar problemas de importação
          </p>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <Button onClick={runDiagnostics} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Executando...' : 'Executar Diagnóstico'}
        </Button>
        {lastCheck && (
          <p className="text-sm text-muted-foreground">
            Última verificação: {lastCheck.toLocaleString()}
          </p>
        )}
      </div>

      <Tabs defaultValue="results" className="w-full">
        <TabsList>
          <TabsTrigger value="results">Resultados</TabsTrigger>
          <TabsTrigger value="solutions">Soluções</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-4">
          {diagnostics.map((diagnostic, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(diagnostic.status)}
                    <CardTitle className="text-lg">{diagnostic.name}</CardTitle>
                  </div>
                  {getStatusBadge(diagnostic.status)}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  {diagnostic.message}
                </p>
                {diagnostic.details && (
                  <div className="mt-3 p-3 bg-muted rounded-md">
                    <pre className="text-xs overflow-auto">
                      {JSON.stringify(diagnostic.details, null, 2)}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {diagnostics.length === 0 && !loading && (
            <Card>
              <CardContent className="text-center py-8">
                <Database className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Clique em "Executar Diagnóstico" para verificar o sistema
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="solutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Soluções Comuns
              </CardTitle>
              <CardDescription>
                Passos para resolver problemas de importação
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  <strong>Problema:</strong> Tabela borderos não encontrada<br />
                  <strong>Solução:</strong> Execute o script SQL em /scripts/fix-borderos-import.sql no Supabase
                </AlertDescription>
              </Alert>

              <Alert>
                <Database className="h-4 w-4" />
                <AlertDescription>
                  <strong>Problema:</strong> Secretarias ou tipos não cadastrados<br />
                  <strong>Solução:</strong> Acesse Configurações → Secretarias/Tipos e cadastre pelo menos um item
                </AlertDescription>
              </Alert>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Problema:</strong> Erro de permissão (RLS)<br />
                  <strong>Solução:</strong> Execute EXECUTE_AGORA_NO_SUPABASE.sql para corrigir políticas
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
