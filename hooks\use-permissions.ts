"use client"

import { useSupabase } from "@/lib/supabase-provider"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export type Permission =
  | "dashboard"
  | "borderos"
  | "secretarias"
  | "direcionamentos"
  | "tipos"
  | "usuarios"
  | "relatorios"
  | "configuracoes"

export function usePermissions() {
  const { userPermissions, userDetails, loading } = useSupabase()

  const isAdmin = (): boolean => {
    return userDetails?.nivel_acesso === "Administrador"
  }

  const hasPermission = (permission: Permission): boolean => {
    if (loading) return false

    // Administrador tem acesso total
    if (isAdmin()) return true

    if (!userPermissions) return false
    return userPermissions[permission] || false
  }

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    // Administrador tem acesso total
    if (isAdmin()) return true

    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    // Administrador tem acesso total
    if (isAdmin()) return true

    return permissions.every(permission => hasPermission(permission))
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin,
    userPermissions,
    loading
  }
}

export function useRequirePermission(permission: Permission) {
  const { hasPermission, loading } = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !hasPermission(permission)) {
      router.push("/dashboard?error=access_denied")
    }
  }, [hasPermission, permission, loading, router])

  return { hasPermission: hasPermission(permission), loading }
}

export function useRequireAnyPermission(permissions: Permission[]) {
  const { hasAnyPermission, loading } = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !hasAnyPermission(permissions)) {
      router.push("/dashboard?error=access_denied")
    }
  }, [hasAnyPermission, permissions, loading, router])

  return { hasPermission: hasAnyPermission(permissions), loading }
}
