"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { Plus, Edit, Loader2, Trash2 } from "lucide-react"

interface Direcionamento {
  id: string
  nome: string
  valor_minimo: number
  valor_maximo: number
  created_at: string
  updated_at: string
}

interface Usuario {
  id: string
  nome: string
  email: string
}

export default function DirecionamentosPage() {
  const notify = useNotify()
  const [direcionamentos, setDirecionamentos] = useState<Direcionamento[]>([])
  const [usuarios, setUsuarios] = useState<Usuario[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingDirecionamento, setEditingDirecionamento] = useState<Direcionamento | null>(null)
  const [formData, setFormData] = useState({
    nome: "",
    valor_minimo: "",
    valor_maximo: "",
    usuarios: [] as string[]
  })

  useEffect(() => {
    fetchDirecionamentos()
    fetchUsuarios()
  }, [])

  const fetchDirecionamentos = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/direcionamentos")
      if (!response.ok) throw new Error("Erro ao buscar direcionamentos")

      const data = await response.json()
      setDirecionamentos(data)
    } catch (error) {
      console.error("Erro ao buscar direcionamentos:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar os direcionamentos.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchUsuarios = async () => {
    try {
      const response = await fetch("/api/usuarios")
      if (!response.ok) throw new Error("Erro ao buscar usuários")

      const data = await response.json()
      setUsuarios(data)
    } catch (error) {
      console.error("Erro ao buscar usuários:", error)
    }
  }

  const handleSalvar = async () => {
    if (!formData.nome.trim()) {
      toast({
        title: "Erro",
        description: "Nome do direcionamento é obrigatório.",
        variant: "destructive",
      })
      return
    }

    if (!formData.valor_minimo || !formData.valor_maximo) {
      toast({
        title: "Erro",
        description: "Valores mínimo e máximo são obrigatórios.",
        variant: "destructive",
      })
      return
    }

    const valorMin = parseFloat(formData.valor_minimo)
    const valorMax = parseFloat(formData.valor_maximo)

    if (valorMin >= valorMax) {
      toast({
        title: "Erro",
        description: "O valor máximo deve ser maior que o valor mínimo.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      const url = editingDirecionamento ? `/api/direcionamentos/${editingDirecionamento.id}` : "/api/direcionamentos"
      const method = editingDirecionamento ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          nome: formData.nome.trim(),
          valor_minimo: valorMin,
          valor_maximo: valorMax,
          usuarios: formData.usuarios
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Erro ao ${editingDirecionamento ? 'atualizar' : 'criar'} direcionamento`)
      }

      const direcionamento = await response.json()

      notify.success(
        editingDirecionamento ? "Direcionamento Atualizado" : "Direcionamento Cadastrado",
        `Direcionamento "${direcionamento.nome}" foi ${editingDirecionamento ? 'atualizado' : 'cadastrado'} com sucesso!`
      )

      setDialogOpen(false)
      setEditingDirecionamento(null)
      setFormData({ nome: "", valor_minimo: "", valor_maximo: "", usuarios: [] })
      fetchDirecionamentos()
    } catch (error: any) {
      console.error(`Erro ao ${editingDirecionamento ? 'atualizar' : 'criar'} direcionamento:`, error)
      toast({
        title: "Erro",
        description: error.message || `Não foi possível ${editingDirecionamento ? 'atualizar' : 'criar'} o direcionamento.`,
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditar = (direcionamento: Direcionamento) => {
    setEditingDirecionamento(direcionamento)

    // Extrair IDs dos usuários associados
    const usuariosAssociados = direcionamento.usuarios?.map(du => du.usuario.id) || []

    setFormData({
      nome: direcionamento.nome,
      valor_minimo: direcionamento.valor_minimo.toString(),
      valor_maximo: direcionamento.valor_maximo.toString(),
      usuarios: usuariosAssociados
    })
    setDialogOpen(true)
  }

  const handleExcluir = async (direcionamento: Direcionamento) => {
    try {
      const response = await fetch(`/api/direcionamentos/${direcionamento.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao excluir direcionamento")
      }

      notify.success(
        "Direcionamento Excluído",
        `Direcionamento "${direcionamento.nome}" foi excluído com sucesso!`
      )

      fetchDirecionamentos()
    } catch (error: any) {
      console.error("Erro ao excluir direcionamento:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir o direcionamento.",
        variant: "destructive",
      })
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const toggleUsuario = (usuarioId: string) => {
    setFormData(prev => ({
      ...prev,
      usuarios: prev.usuarios.includes(usuarioId)
        ? prev.usuarios.filter(id => id !== usuarioId)
        : [...prev.usuarios, usuarioId]
    }))
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Direcionamentos</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sky-600 hover:bg-sky-700">
              <Plus className="mr-2 h-4 w-4" />
              Novo Direcionamento
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingDirecionamento ? "Editar Direcionamento" : "Adicionar Novo Direcionamento"}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome do Direcionamento</Label>
                <Input
                  id="nome"
                  placeholder="Digite o nome do direcionamento"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="valorMinimo">Valor Mínimo</Label>
                  <Input
                    id="valorMinimo"
                    type="number"
                    placeholder="0,00"
                    step="0.01"
                    min="0"
                    value={formData.valor_minimo}
                    onChange={(e) => setFormData(prev => ({ ...prev, valor_minimo: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="valorMaximo">Valor Máximo</Label>
                  <Input
                    id="valorMaximo"
                    type="number"
                    placeholder="0,00"
                    step="0.01"
                    min="0"
                    value={formData.valor_maximo}
                    onChange={(e) => setFormData(prev => ({ ...prev, valor_maximo: e.target.value }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Usuários com Acesso</Label>
                <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                  {usuarios.length === 0 ? (
                    <p className="text-sm text-muted-foreground">Carregando usuários...</p>
                  ) : (
                    <div className="space-y-2">
                      {usuarios.map((usuario) => (
                        <div key={usuario.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`usuario-${usuario.id}`}
                            checked={formData.usuarios.includes(usuario.id)}
                            onChange={() => toggleUsuario(usuario.id)}
                            className="rounded border-gray-300"
                          />
                          <label
                            htmlFor={`usuario-${usuario.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {usuario.nome}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Selecione os usuários que terão acesso a este direcionamento
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setDialogOpen(false)
                  setEditingDirecionamento(null)
                  setFormData({ nome: "", valor_minimo: "", valor_maximo: "", usuarios: [] })
                }}
                disabled={saving}
              >
                Cancelar
              </Button>
              <Button
                className="bg-sky-600 hover:bg-sky-700"
                onClick={handleSalvar}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Direcionamentos Cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Valor Mínimo</TableHead>
                  <TableHead>Valor Máximo</TableHead>
                  <TableHead>Data de Criação</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {direcionamentos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      Nenhum direcionamento cadastrado.
                    </TableCell>
                  </TableRow>
                ) : (
                  direcionamentos.map((direcionamento) => (
                    <TableRow key={direcionamento.id}>
                      <TableCell className="font-medium">{direcionamento.nome}</TableCell>
                      <TableCell>{formatCurrency(direcionamento.valor_minimo)}</TableCell>
                      <TableCell>{formatCurrency(direcionamento.valor_maximo)}</TableCell>
                      <TableCell>{new Date(direcionamento.created_at).toLocaleDateString("pt-BR")}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditar(direcionamento)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir o direcionamento "{direcionamento.nome}"?
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleExcluir(direcionamento)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Excluir
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
