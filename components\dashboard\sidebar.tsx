"use client"


import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  FileText,
  Home,
  LogOut,
  Package,
  Settings,
  Users,
  Building2,
  FileBarChart2,
  FileStack,
  Shield,
  Trash2,
  Stethoscope,
  User,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { ThemeToggleSimple } from "@/components/ui/theme-toggle-simple"
import { useSupabase } from "@/lib/supabase-provider"
import { usePermissions as usePermissionsHook } from "@/hooks/use-permissions"

export function DashboardSidebar() {
  const pathname = usePathname()
  const { state, toggleSidebar } = useSidebar()
  const { userDetails, loading, signOut } = useSupabase()

  const handleLogout = async () => {
    try {
      console.log("Sidebar logout iniciado...")
      await signOut()
      // Forçar redirecionamento
      window.location.href = "/login"
    } catch (error) {
      console.error("Erro no logout do sidebar:", error)
      // Mesmo com erro, forçar redirecionamento
      window.location.href = "/login"
    }
  }

  // Usar o hook de permissões correto
  const { hasPermission, isAdmin } = usePermissionsHook()

  // Função para verificar se o usuário tem permissão para uma seção
  const canAccessSection = (permission: string) => {
    // Admin tem acesso total
    if (isAdmin()) return true

    // Verificar permissão específica
    return hasPermission(permission as any)
  }

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-border p-4">
        <div className="flex items-center gap-2">
          <FileStack className="h-6 w-6 text-sky-600" />
          <span
            className={`text-lg font-bold transition-opacity duration-200 ${state === "collapsed" ? "opacity-0" : "opacity-100"}`}
          >
            CRM de Bordero
          </span>
        </div>
        <div className="lg:hidden">
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent className="p-2">
        <SidebarMenu>
          {/* Dashboard - Todos podem acessar */}
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/dashboard"} tooltip="Dashboard">
              <Link href="/dashboard">
                <Home className="h-4 w-4" />
                <span>Dashboard</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* Borderos - Verificar permissão */}
          {canAccessSection('borderos') && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname.startsWith("/dashboard/borderos")} tooltip="Borderos">
                <Link href="/dashboard/borderos">
                  <FileText className="h-4 w-4" />
                  <span>Borderos</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Secretarias - Verificar permissão */}
          {canAccessSection('secretarias') && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname.startsWith("/dashboard/secretarias")} tooltip="Secretarias">
                <Link href="/dashboard/secretarias">
                  <Building2 className="h-4 w-4" />
                  <span>Secretarias</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Direcionamentos - Verificar permissão */}
          {canAccessSection('direcionamentos') && (
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                isActive={pathname.startsWith("/dashboard/direcionamentos")}
                tooltip="Direcionamentos"
              >
                <Link href="/dashboard/direcionamentos">
                  <Package className="h-4 w-4" />
                  <span>Direcionamentos</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Tipos de Bordero - Verificar permissão */}
          {canAccessSection('tipos') && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname.startsWith("/dashboard/tipos")} tooltip="Tipos de Bordero">
                <Link href="/dashboard/tipos">
                  <FileBarChart2 className="h-4 w-4" />
                  <span>Tipos de Bordero</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Usuários - Verificar permissão */}
          {canAccessSection('usuarios') && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname.startsWith("/dashboard/usuarios")} tooltip="Usuários">
                <Link href="/dashboard/usuarios">
                  <Users className="h-4 w-4" />
                  <span>Usuários</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Relatórios - Verificar permissão */}
          {canAccessSection('relatorios') && (
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname.startsWith("/dashboard/relatorios")} tooltip="Relatórios">
                <Link href="/dashboard/relatorios">
                  <BarChart3 className="h-4 w-4" />
                  <span>Relatórios</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Perfil - Todos os usuários */}
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname === "/dashboard/perfil"} tooltip="Perfil">
              <Link href="/dashboard/perfil">
                <User className="h-4 w-4" />
                <span>Perfil</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>

          {/* Configurações - Verificar permissão */}
          {canAccessSection('configuracoes') && (
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                isActive={pathname.startsWith("/dashboard/configuracoes")}
                tooltip="Configurações"
              >
                <Link href="/dashboard/configuracoes">
                  <Settings className="h-4 w-4" />
                  <span>Configurações</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}

          {/* Sistema - Apenas para administradores */}
          {isAdmin() && (
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                isActive={pathname.startsWith("/dashboard/sistema") && !pathname.includes("?tab=diagnostico")}
                tooltip="Sistema"
              >
                <Link href="/dashboard/sistema">
                  <Shield className="h-4 w-4" />
                  <span>Sistema</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-border p-4">
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={userDetails?.avatar_url || "/diverse-avatars.png"} alt="Avatar" />
            <AvatarFallback>{userDetails?.nome?.charAt(0) || "U"}</AvatarFallback>
          </Avatar>
          <div
            className={`flex flex-col transition-opacity duration-200 ${state === "collapsed" ? "opacity-0" : "opacity-100"}`}
          >
            <span className="text-sm font-medium">{userDetails?.nome || "Usuário"}</span>
            <span className="text-xs text-muted-foreground">{userDetails?.nivel_acesso || "Carregando..."}</span>
          </div>
          <ThemeToggleSimple />
          <Button variant="ghost" size="icon" className="ml-1" onClick={handleLogout}>
            <LogOut className="h-4 w-4" />
            <span className="sr-only">Sair</span>
          </Button>
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
