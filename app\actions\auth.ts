"use server"

import { createServerActionClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"

export async function login(formData: FormData) {
  const email = formData.get("email") as string
  const password = formData.get("password") as string

  if (!email || !password) {
    return { error: "Email e senha são obrigatórios" }
  }

  const supabase = createServerActionClient({ cookies })

  try {
    // Tentar fazer login
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error("Erro de login:", error.message)

      // Para desenvolvimento, podemos criar um bypass temporário
      if (process.env.NODE_ENV === "development") {
        console.log("Modo de desenvolvimento: criando sessão de bypass")

        // Criar uma sessão temporária para desenvolvimento
        const { data: sessionData, error: sessionError } = await supabase.auth.signInWithPassword({
          email: "<EMAIL>",
          password: "Admin@123",
        })

        if (sessionError) {
          return { error: "Falha na autenticação: " + sessionError.message }
        }

        redirect("/dashboard")
      }

      return { error: "Credenciais inválidas" }
    }

    redirect("/dashboard")
  } catch (err) {
    console.error("Erro inesperado durante login:", err)
    return { error: "Ocorreu um erro inesperado durante o login" }
  }
}
