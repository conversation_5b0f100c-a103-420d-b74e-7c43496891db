// Otimizações para consultas do banco de dados

export const DB_OPTIMIZATIONS = {
  // Consultas otimizadas para usuários
  USER_WITH_PERMISSIONS: `
    *,
    nivel_acesso:niveis_acesso(nome, permissoes)
  `,
  
  // Consultas otimizadas para borderos
  BORDERO_LIST: `
    id,
    bordero_cod,
    valor,
    data,
    status,
    nome_empresa,
    created_at,
    secretaria:secretarias(nome),
    tipo:tipos(nome),
    responsavel:usuarios(nome)
  `,
  
  BORDERO_DETAIL: `
    *,
    secretaria:secretarias(nome),
    tipo:tipos(nome),
    responsavel:usuarios(nome, email)
  `,
  
  // Consultas otimizadas para logs
  BORDERO_LOGS: `
    id,
    acao,
    detalhes,
    data_hora,
    usuario:usuarios(nome, email)
  `
}

// Cache de consultas frequentes
class QueryCache {
  private cache = new Map<string, { data: any, timestamp: number }>()
  private readonly TTL = 2 * 60 * 1000 // 2 minutos

  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

export const queryCache = new QueryCache()

// Função para criar chave de cache
export function createCacheKey(table: string, filters: Record<string, any>): string {
  const filterStr = Object.entries(filters)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}:${value}`)
    .join('|')
  
  return `${table}:${filterStr}`
}

// Wrapper para consultas com cache
export async function cachedQuery<T>(
  cacheKey: string,
  queryFn: () => Promise<T>
): Promise<T> {
  // Verificar cache primeiro
  const cached = queryCache.get(cacheKey)
  if (cached) {
    console.log(`📦 Cache hit: ${cacheKey}`)
    return cached
  }

  // Executar consulta
  console.log(`🔍 Cache miss: ${cacheKey}`)
  const result = await queryFn()
  
  // Armazenar no cache
  queryCache.set(cacheKey, result)
  
  return result
}

// Função para invalidar cache relacionado
export function invalidateCache(pattern: string): void {
  const keysToDelete: string[] = []
  
  for (const [key] of queryCache['cache']) {
    if (key.includes(pattern)) {
      keysToDelete.push(key)
    }
  }
  
  keysToDelete.forEach(key => {
    queryCache['cache'].delete(key)
  })
  
  console.log(`🗑️ Invalidated ${keysToDelete.length} cache entries for pattern: ${pattern}`)
}

// Configurações de performance
export const PERFORMANCE_CONFIG = {
  // Timeouts para diferentes tipos de operação
  TIMEOUTS: {
    AUTH: 5000,        // 5 segundos para autenticação
    PERMISSIONS: 3000, // 3 segundos para permissões
    DATA_FETCH: 8000,  // 8 segundos para busca de dados
    MUTATION: 10000    // 10 segundos para operações de escrita
  },
  
  // Limites de paginação
  PAGINATION: {
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100
  },
  
  // Cache TTL por tipo de dados
  CACHE_TTL: {
    USER_DATA: 5 * 60 * 1000,      // 5 minutos
    PERMISSIONS: 3 * 60 * 1000,    // 3 minutos
    BORDEROS: 1 * 60 * 1000,       // 1 minuto
    STATIC_DATA: 10 * 60 * 1000    // 10 minutos (secretarias, tipos, etc)
  }
}

// Função para monitorar performance de consultas
export function withPerformanceMonitoring<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  
  return fn().finally(() => {
    const duration = performance.now() - start
    
    if (duration > 2000) {
      console.warn(`🐌 Operação lenta: ${operation} - ${duration.toFixed(2)}ms`)
    } else {
      console.log(`⚡ ${operation}: ${duration.toFixed(2)}ms`)
    }
  })
}
