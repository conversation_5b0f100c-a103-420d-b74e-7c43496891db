const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createLogTable() {
  try {
    console.log('🔧 Tentando criar tabela log_atividades...')

    // Tentar inserir um registro para forçar a criação da tabela
    const { error } = await supabase
      .from('log_atividades')
      .insert({
        usuario_id: null,
        acao: 'teste',
        entidade: 'sistema',
        entidade_id: null,
        detalhes: { teste: true },
        ip: '127.0.0.1',
        user_agent: 'Script'
      })

    if (error) {
      console.log('❌ Erro esperado (tabela não existe):', error.message)
      console.log('\n📝 A tabela log_atividades precisa ser criada manualmente no Supabase.')
      console.log('\n🔗 Acesse o Supabase SQL Editor e execute o seguinte SQL:')
      console.log('\n' + '='.repeat(60))
      console.log(`
-- Criar tabela de logs de atividade
CREATE TABLE IF NOT EXISTS log_atividades (
    id SERIAL PRIMARY KEY,
    usuario_id UUID REFERENCES usuarios(id) ON DELETE CASCADE,
    acao VARCHAR(255) NOT NULL,
    entidade VARCHAR(255) NOT NULL,
    entidade_id VARCHAR(255),
    detalhes JSONB,
    ip VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_log_atividades_usuario_id ON log_atividades(usuario_id);
CREATE INDEX IF NOT EXISTS idx_log_atividades_entidade ON log_atividades(entidade);
CREATE INDEX IF NOT EXISTS idx_log_atividades_created_at ON log_atividades(created_at);

-- Habilitar RLS (Row Level Security)
ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;

-- Criar política para permitir leitura para usuários autenticados
CREATE POLICY IF NOT EXISTS "Usuários podem ver logs de atividades" ON log_atividades
  FOR SELECT USING (auth.role() = 'authenticated');

-- Criar política para permitir inserção para usuários autenticados
CREATE POLICY IF NOT EXISTS "Usuários podem criar logs de atividades" ON log_atividades
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');
      `)
      console.log('='.repeat(60))
    } else {
      console.log('✅ Tabela já existe!')
      
      // Deletar o registro de teste
      await supabase
        .from('log_atividades')
        .delete()
        .eq('acao', 'teste')
    }

  } catch (error) {
    console.error('❌ Erro:', error.message)
  }
}

createLogTable()
  .then(() => {
    console.log('\n🎉 Verificação concluída!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
