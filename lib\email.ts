import nodemailer from "nodemailer"

interface EmailOptions {
  to: string
  subject: string
  html: string
}

export async function sendEmail({ to, subject, html }: EmailOptions) {
  try {
    // Verificar se as variáveis de ambiente estão configuradas
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.warn("SMTP não configurado. Email não será enviado.")
      return { success: false, error: "SMTP não configurado" }
    }

    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT) || 587,
      secure: false, // true para 465, false para outras portas
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    })

    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || `"CRM Bordero" <${process.env.SMTP_USER}>`,
      to,
      subject,
      html,
    })

    console.log("Email enviado:", info.messageId)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error("Erro ao enviar email:", error)
    return { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" }
  }
}

export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export function getPasswordResetEmailTemplate(code: string, userName: string): string {
  return `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Redefinir Senha - CRM Bordero</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #0ea5e9;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #f8f9fa;
          padding: 30px;
          border-radius: 0 0 8px 8px;
        }
        .code-box {
          background-color: #e3f2fd;
          border: 2px solid #0ea5e9;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          margin: 20px 0;
        }
        .code {
          font-size: 32px;
          font-weight: bold;
          color: #0ea5e9;
          letter-spacing: 4px;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          color: #666;
          font-size: 14px;
        }
        .warning {
          background-color: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 4px;
          padding: 15px;
          margin: 20px 0;
          color: #856404;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🔐 Redefinir Senha</h1>
        <p>CRM de Bordero</p>
      </div>
      
      <div class="content">
        <h2>Olá, ${userName}!</h2>
        
        <p>Você solicitou a redefinição da sua senha no CRM de Bordero.</p>
        
        <p>Use o código abaixo para redefinir sua senha:</p>
        
        <div class="code-box">
          <div class="code">${code}</div>
          <p><strong>Código de Verificação</strong></p>
        </div>
        
        <div class="warning">
          <strong>⚠️ Importante:</strong>
          <ul>
            <li>Este código é válido por apenas <strong>15 minutos</strong></li>
            <li>Use apenas se você solicitou esta redefinição</li>
            <li>Não compartilhe este código com ninguém</li>
          </ul>
        </div>
        
        <p>Se você não solicitou esta redefinição, ignore este email. Sua senha permanecerá inalterada.</p>
        
        <p>Para sua segurança, este código expirará automaticamente em 15 minutos.</p>
      </div>
      
      <div class="footer">
        <p>Este é um email automático. Não responda a esta mensagem.</p>
        <p><strong>CRM de Bordero</strong> - Sistema de Gestão</p>
      </div>
    </body>
    </html>
  `
}

export function getWelcomeEmailTemplate(userName: string, tempPassword: string): string {
  return `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Bem-vindo ao CRM Bordero</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #0ea5e9;
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 8px 8px 0 0;
        }
        .content {
          background-color: #f8f9fa;
          padding: 30px;
          border-radius: 0 0 8px 8px;
        }
        .credentials {
          background-color: #e3f2fd;
          border: 2px solid #0ea5e9;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          color: #666;
          font-size: 14px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🎉 Bem-vindo!</h1>
        <p>CRM de Bordero</p>
      </div>
      
      <div class="content">
        <h2>Olá, ${userName}!</h2>
        
        <p>Sua conta foi criada com sucesso no CRM de Bordero!</p>
        
        <div class="credentials">
          <h3>Suas credenciais de acesso:</h3>
          <p><strong>Senha temporária:</strong> ${tempPassword}</p>
          <p><em>Por favor, altere sua senha no primeiro acesso.</em></p>
        </div>
        
        <p>Acesse o sistema e comece a gerenciar seus borderos de forma eficiente.</p>
      </div>
      
      <div class="footer">
        <p>Este é um email automático. Não responda a esta mensagem.</p>
        <p><strong>CRM de Bordero</strong> - Sistema de Gestão</p>
      </div>
    </body>
    </html>
  `
}
