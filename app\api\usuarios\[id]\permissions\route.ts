import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    // Verificar se o usuário está tentando acessar suas próprias permissões
    // ou se é um admin (que pode ver permissões de qualquer usuário)
    const userId = params.id
    const isOwnProfile = session.user.id === userId
    
    // Se não for o próprio perfil, verificar se é admin
    if (!isOwnProfile) {
      const supabase = createClient(
        process.env.SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      )
      
      const { data: requestingUser, error: userError } = await supabase
        .from("usuarios")
        .select("nivel_acesso")
        .eq("id", session.user.id)
        .single()
      
      if (userError || !requestingUser || requestingUser.nivel_acesso !== 'admin') {
        return NextResponse.json({ error: "Não autorizado a ver permissões de outros usuários" }, { status: 403 })
      }
    }

    // Buscar nível de acesso do usuário solicitado
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se existe cache na tabela user_permissions_cache
    const { data: cachedPermissions, error: cacheError } = await supabase
      .from("user_permissions_cache")
      .select("permissions, last_updated")
      .eq("user_id", userId)
      .single()

    // Se tiver cache válido (menos de 10 minutos), usar
    if (!cacheError && cachedPermissions) {
      const cacheAge = Date.now() - new Date(cachedPermissions.last_updated).getTime()
      const cacheValid = cacheAge < 10 * 60 * 1000 // 10 minutos
      
      if (cacheValid) {
        console.log(`Usando cache de permissões para usuário ${userId} (${Math.round(cacheAge / 1000)}s)`);
        return NextResponse.json({
          ...cachedPermissions.permissions,
          fromCache: true,
          cacheAge: Math.round(cacheAge / 1000)
        })
      }
    }

    // Se não tiver cache válido, buscar do banco
    console.log(`Buscando permissões do banco para usuário ${userId}`);
    
    let usuario;
    
    try {
      const { data: usuarioData, error: userError } = await supabase
        .from("usuarios")
        .select(`
          id,
          nome,
          email,
          nivel_acesso,
          direcionamentos:direcionamento_usuarios(
            direcionamento:direcionamentos(*)
          )
        `)
        .eq("id", userId)
        .single();

      if (userError) {
        console.error("Erro ao buscar usuário:", userError);
        
        // Se o usuário não for encontrado, retornar um nível de acesso padrão
        // em vez de falhar completamente
        if (userError.code === 'PGRST116') {
          console.log(`Usuário ${userId} não encontrado, retornando nível padrão`);
          return NextResponse.json({
            id: userId,
            nome: "Usuário",
            email: "",
            nivelAcesso: "visualizador", // Nível padrão
            direcionamentos: []
          });
        }
        
        return NextResponse.json({ error: "Erro ao buscar usuário" }, { status: 500 });
      }

      if (!usuarioData) {
        console.log(`Usuário ${userId} não encontrado, retornando nível padrão`);
        return NextResponse.json({
          id: userId,
          nome: "Usuário",
          email: "",
          nivelAcesso: "visualizador", // Nível padrão
          direcionamentos: []
        });
      }
      
      // Guardar os dados do usuário para uso posterior
      usuario = usuarioData;
      
    } catch (error) {
      console.error("Erro ao buscar usuário:", error);
      
      // Retornar um nível de acesso padrão em vez de falhar
      return NextResponse.json({
        id: userId,
        nome: "Usuário",
        email: "",
        nivelAcesso: "visualizador", // Nível padrão
        direcionamentos: []
      });
    }

    // Preparar resposta
    const response = {
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      nivelAcesso: usuario.nivel_acesso,
      direcionamentos: usuario.direcionamentos?.map((d: any) => d.direcionamento) || []
    }

    // Atualizar cache
    try {
      // Deletar cache existente
      await supabase
        .from("user_permissions_cache")
        .delete()
        .eq("user_id", userId)

      // Inserir novo cache
      await supabase
        .from("user_permissions_cache")
        .insert({
          user_id: userId,
          permissions: response,
          last_updated: new Date().toISOString()
        })
    } catch (cacheError) {
      console.error("Erro ao atualizar cache de permissões:", cacheError)
      // Não falhar a requisição por erro de cache
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Erro ao buscar permissões:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}