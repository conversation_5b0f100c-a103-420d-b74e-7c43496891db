// Interfaces para tipagem
interface LogBorderoParams {
  borderoId: string | number
  usuarioId: string
  acao: 'criar' | 'editar' | 'excluir' | 'status' | 'comentario' | 'devolucao'
  detalhes: any
  ip?: string
  userAgent?: string
}

interface LogAtividadeParams {
  usuarioId: string
  acao: string
  entidade: string
  entidadeId: string
  detalhes: any
  ip?: string
  userAgent?: string
}

// Classe para uso apenas no servidor
export class BorderoLogger {
  private supabase: any

  constructor(supabaseInstance: any) {
    this.supabase = supabaseInstance
  }

  /**
   * Registra uma ação específica do bordero
   */
  async logBorderoAction({
    borderoId,
    usuarioId,
    acao,
    detalhes,
    ip,
    userAgent
  }: LogBorderoParams) {
    try {
      const { error } = await this.supabase
        .from("bordero_logs")
        .insert({
          bordero_id: typeof borderoId === 'string' ? parseInt(borderoId) : borderoId,
          usuario_id: usuarioId,
          acao,
          detalhes: typeof detalhes === 'object' ? detalhes : { message: detalhes },
          data_hora: new Date().toISOString()
        })

      if (error) {
        console.error("Erro ao registrar log do bordero:", error)
        throw error
      }

      console.log(`✅ Log do bordero registrado: ${acao} - Bordero ${borderoId}`)
    } catch (error) {
      console.error("Falha ao registrar log do bordero:", error)
      // Não propagar o erro para não quebrar a operação principal
    }
  }

  /**
   * Registra uma atividade geral do sistema
   */
  async logActivity({
    usuarioId,
    acao,
    entidade,
    entidadeId,
    detalhes,
    ip,
    userAgent
  }: LogAtividadeParams) {
    try {
      const { error } = await this.supabase
        .from("log_atividades")
        .insert({
          usuario_id: usuarioId,
          acao,
          entidade,
          entidade_id: entidadeId,
          detalhes: typeof detalhes === 'object' ? detalhes : { message: detalhes },
          ip,
          user_agent: userAgent,
          created_at: new Date().toISOString()
        })

      if (error) {
        console.error("Erro ao registrar atividade:", error)
        throw error
      }

      console.log(`✅ Atividade registrada: ${acao} - ${entidade} ${entidadeId}`)
    } catch (error) {
      console.error("Falha ao registrar atividade:", error)
      // Não propagar o erro para não quebrar a operação principal
    }
  }

  /**
   * Registra tanto no log específico quanto no log geral
   */
  async logBorderoComplete({
    borderoId,
    usuarioId,
    acao,
    detalhes,
    ip,
    userAgent
  }: LogBorderoParams) {
    // Registrar no log específico do bordero
    await this.logBorderoAction({
      borderoId,
      usuarioId,
      acao,
      detalhes,
      ip,
      userAgent
    })

    // Registrar no log geral de atividades
    await this.logActivity({
      usuarioId,
      acao,
      entidade: "bordero",
      entidadeId: borderoId.toString(),
      detalhes,
      ip,
      userAgent
    })
  }

  /**
   * Cria notificação para usuários específicos
   */
  async createNotification({
    titulo,
    mensagem,
    usuarioIds,
    borderoId
  }: {
    titulo: string
    mensagem: string
    usuarioIds: string[]
    borderoId?: string | number
  }) {
    try {
      const notificacoes = usuarioIds.map(usuarioId => ({
        titulo,
        mensagem,
        usuario_id: usuarioId,
        lida: false,
        created_at: new Date().toISOString()
      }))

      const { error } = await this.supabase
        .from("notificacoes")
        .insert(notificacoes)

      if (error) {
        console.error("Erro ao criar notificações:", error)
        throw error
      }

      console.log(`✅ ${notificacoes.length} notificações criadas`)
    } catch (error) {
      console.error("Falha ao criar notificações:", error)
    }
  }

  /**
   * Busca usuários responsáveis por um bordero para notificação
   */
  async getBorderoStakeholders(borderoId: string | number): Promise<string[]> {
    try {
      const { data: bordero, error } = await this.supabase
        .from("borderos")
        .select(`
          responsavel_id,
          secretaria:secretarias(
            usuarios:secretaria_usuarios(
              usuario:usuarios(id)
            )
          ),
          devolucoes:bordero_devolucoes(
            usuario:usuarios(id)
          )
        `)
        .eq("id", borderoId)
        .single()

      if (error || !bordero) {
        console.error("Erro ao buscar stakeholders:", error)
        return []
      }

      const stakeholders = new Set<string>()

      // Adicionar responsável
      if (bordero.responsavel_id) {
        stakeholders.add(bordero.responsavel_id)
      }

      // Adicionar usuários da secretaria
      if (bordero.secretaria?.usuarios) {
        bordero.secretaria.usuarios.forEach((su: any) => {
          if (su.usuario?.id) {
            stakeholders.add(su.usuario.id)
          }
        })
      }

      // Adicionar usuários de devolução
      if (bordero.devolucoes) {
        bordero.devolucoes.forEach((dev: any) => {
          if (dev.usuario?.id) {
            stakeholders.add(dev.usuario.id)
          }
        })
      }

      return Array.from(stakeholders)
    } catch (error) {
      console.error("Erro ao buscar stakeholders:", error)
      return []
    }
  }
}

// Funções para criar instância do logger (apenas no servidor)
export function createBorderoLogger(supabaseInstance: any) {
  return new BorderoLogger(supabaseInstance)
}

// Funções de conveniência para uso no servidor
export const logBorderoAction = (supabaseInstance: any, params: LogBorderoParams) => {
  const logger = new BorderoLogger(supabaseInstance)
  return logger.logBorderoAction(params)
}

export const logBorderoComplete = (supabaseInstance: any, params: LogBorderoParams) => {
  const logger = new BorderoLogger(supabaseInstance)
  return logger.logBorderoComplete(params)
}

export const createBorderoNotification = (supabaseInstance: any, params: {
  titulo: string
  mensagem: string
  usuarioIds: string[]
  borderoId?: string | number
}) => {
  const logger = new BorderoLogger(supabaseInstance)
  return logger.createNotification(params)
}

// Função para formatação de moeda
function formatarMoeda(valor: number | string): string {
  const numericValue = typeof valor === 'string' ? parseFloat(valor) : valor

  if (isNaN(numericValue)) {
    return 'R$ 0,00'
  }

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numericValue)
}

// Funções de formatação para a timeline
export function formatarDetalhesLog(tipo: string, detalhes: any): string {
  switch (tipo) {
    case 'criar':
      return `Bordero ${detalhes.bordero_cod} criado${detalhes.valor ? ` no valor de ${formatarMoeda(detalhes.valor)}` : ''}${detalhes.empresa ? ` para ${detalhes.empresa}` : ''}`

    case 'editar':
      if (detalhes.campos_modificados && detalhes.campos_modificados.length > 0) {
        const campos = detalhes.campos_modificados.map((campo: string) => {
          const valorAntigo = detalhes.valores_antigos?.[campo]
          const valorNovo = detalhes.valores_novos?.[campo]

          if (valorAntigo && valorNovo) {
            // Formatação especial para campo valor
            if (campo === 'valor') {
              const valorAntigoFormatado = formatarMoeda(valorAntigo)
              const valorNovoFormatado = formatarMoeda(valorNovo)
              return `${formatarNomeCampo(campo)} alterado de ${valorAntigoFormatado} para ${valorNovoFormatado}`
            }
            return `${formatarNomeCampo(campo)} alterado de "${valorAntigo}" para "${valorNovo}"`
          }
          return `${formatarNomeCampo(campo)} alterado`
        })

        if (campos.length === 1) {
          return campos[0]
        }
        return `Campos alterados: ${campos.join(', ')}`
      }
      return 'Bordero editado'

    case 'status':
      if (detalhes.status_anterior && detalhes.status_novo) {
        return `Status alterado de "${formatarStatus(detalhes.status_anterior)}" para "${formatarStatus(detalhes.status_novo)}"`
      }
      return 'Status alterado'

    case 'excluir':
      return `Bordero ${detalhes.bordero_cod} excluído${detalhes.motivo_exclusao ? ` - Motivo: ${detalhes.motivo_exclusao}` : ''}`

    default:
      return 'Ação realizada'
  }
}

function formatarNomeCampo(campo: string): string {
  const nomes: Record<string, string> = {
    'bordero_cod': 'Código',
    'valor': 'Valor',
    'data': 'Data',
    'nome_empresa': 'Empresa',
    'secretaria_id': 'Secretaria',
    'tipo_id': 'Tipo',
    'observacao': 'Observação',
    'responsavel_id': 'Responsável',
    'status': 'Status'
  }
  return nomes[campo] || campo
}

function formatarStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'novo': 'Novo',
    'analise': 'Em Análise',
    'assinado': 'Assinado',
    'pago': 'Pago',
    'corrigir': 'Corrigir',
    'cancelado': 'Cancelado',
    'excluido': 'Excluído',
    'arquivado': 'Arquivado'
  }
  return statusMap[status] || status
}



export function getIconeAcao(acao: string) {
  switch (acao) {
    case 'criar':
      return { icon: 'Plus', color: 'text-green-600', bg: 'bg-green-100 border-green-200' }
    case 'editar':
      return { icon: 'Edit', color: 'text-blue-600', bg: 'bg-blue-100 border-blue-200' }
    case 'status':
      return { icon: 'Repeat', color: 'text-purple-600', bg: 'bg-purple-100 border-purple-200' }
    case 'excluir':
      return { icon: 'Trash2', color: 'text-red-600', bg: 'bg-red-100 border-red-200' }
    default:
      return { icon: 'FileText', color: 'text-gray-600', bg: 'bg-gray-100 border-gray-200' }
  }
}
