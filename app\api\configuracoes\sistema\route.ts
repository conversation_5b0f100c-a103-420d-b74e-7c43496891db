import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: config, error } = await supabase
      .from("configuracoes_sistema")
      .select("*")
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error("Erro ao buscar configurações:", error)

      // Se a tabela não existir, retornar valores padrão
      if (error.message?.includes('does not exist')) {
        console.log("Tabela configuracoes_sistema não existe, retornando valores padrão")
        return NextResponse.json({
          nome: "CRM de Bordero",
          modoEscuro: false,
          notificacoesEmail: true,
        })
      }

      return NextResponse.json({ error: "Erro ao buscar configurações" }, { status: 500 })
    }

    // Se não existir configuração, retornar valores padrão
    if (!config) {
      return NextResponse.json({
        nome: "CRM de Bordero",
        modoEscuro: false,
        notificacoesEmail: true,
      })
    }

    // Mapear os campos do banco para o formato esperado pelo frontend
    return NextResponse.json({
      id: config.id,
      nome: config.nome,
      modoEscuro: config.modo_escuro,
      notificacoesEmail: config.notificacoes_email,
      logoUrl: config.logo_url,
      created_at: config.created_at,
      updated_at: config.updated_at
    })
  } catch (error) {
    console.error("Erro ao buscar configurações:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { nome, modoEscuro, notificacoesEmail } = body

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe configuração
    const { data: existingConfig, error: checkError } = await supabase
      .from("configuracoes_sistema")
      .select("id")
      .single()

    // Se a tabela não existir, retornar erro informativo
    if (checkError && checkError.message?.includes('does not exist')) {
      console.error("Erro ao criar configurações: tabela não existe")
      return NextResponse.json({
        error: "Tabela configuracoes_sistema não existe. Execute o setup primeiro.",
        needsSetup: true
      }, { status: 400 })
    }

    if (existingConfig) {
      // Atualizar configuração existente
      const { data, error } = await supabase
        .from("configuracoes_sistema")
        .update({
          nome,
          modo_escuro: modoEscuro,
          notificacoes_email: notificacoesEmail,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingConfig.id)
        .select()
        .single()

      if (error) {
        console.error("Erro ao atualizar configurações:", error)
        return NextResponse.json({ error: "Erro ao atualizar configurações" }, { status: 500 })
      }

      // Mapear os campos do banco para o formato esperado pelo frontend
      return NextResponse.json({
        id: data.id,
        nome: data.nome,
        modoEscuro: data.modo_escuro,
        notificacoesEmail: data.notificacoes_email,
        logoUrl: data.logo_url,
        created_at: data.created_at,
        updated_at: data.updated_at
      })
    } else {
      // Criar nova configuração
      const { data, error } = await supabase
        .from("configuracoes_sistema")
        .insert({
          nome,
          modo_escuro: modoEscuro,
          notificacoes_email: notificacoesEmail,
        })
        .select()
        .single()

      if (error) {
        console.error("Erro ao criar configurações:", error)
        return NextResponse.json({ error: "Erro ao criar configurações" }, { status: 500 })
      }

      // Mapear os campos do banco para o formato esperado pelo frontend
      return NextResponse.json({
        id: data.id,
        nome: data.nome,
        modoEscuro: data.modo_escuro,
        notificacoesEmail: data.notificacoes_email,
        logoUrl: data.logo_url,
        created_at: data.created_at,
        updated_at: data.updated_at
      })
    }
  } catch (error) {
    console.error("Erro ao salvar configurações:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
