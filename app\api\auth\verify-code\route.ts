import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST(request: Request) {
  try {
    const { email, code } = await request.json()

    if (!email || !code) {
      return NextResponse.json({ error: "Email e código são obrigatórios" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar código de verificação
    const { data: verificacao, error: verifyError } = await supabase
      .from("codigo_verificacao")
      .select("*")
      .eq("email", email)
      .eq("codigo", code)
      .eq("tipo", "reset_password")
      .eq("usado", false)
      .single()

    if (verifyError || !verificacao) {
      return NextResponse.json({ error: "Código inválido ou expirado" }, { status: 400 })
    }

    // Verificar se o código não expirou
    const now = new Date()
    const expiresAt = new Date(verificacao.expira_em)

    if (now > expiresAt) {
      return NextResponse.json({ error: "Código expirado" }, { status: 400 })
    }

    // Marcar código como usado
    const { error: updateError } = await supabase
      .from("codigo_verificacao")
      .update({ usado: true })
      .eq("id", verificacao.id)

    if (updateError) {
      console.error("Erro ao marcar código como usado:", updateError)
    }

    // Gerar token temporário para redefinição de senha
    const resetToken = crypto.randomUUID()
    const tokenExpiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutos

    // Salvar token de redefinição
    const { error: tokenError } = await supabase
      .from("reset_tokens")
      .insert({
        email: email,
        token: resetToken,
        expira_em: tokenExpiresAt.toISOString(),
        usado: false
      })

    if (tokenError) {
      console.error("Erro ao salvar token de redefinição:", tokenError)
      return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
    }

    return NextResponse.json({ 
      message: "Código verificado com sucesso",
      resetToken: resetToken
    })

  } catch (error) {
    console.error("Erro ao verificar código:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
