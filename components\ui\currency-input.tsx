"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { applyCurrencyMask, removeCurrencyMask } from "@/lib/currency-utils"

interface CurrencyInputProps extends Omit<React.ComponentProps<"input">, "onChange" | "value"> {
  value?: number | string
  onChange?: (value: number) => void
  onValueChange?: (value: number) => void
  placeholder?: string
}

const CurrencyInput = React.forwardRef<HTMLInputElement, CurrencyInputProps>(
  ({ className, value, onChange, onValueChange, placeholder = "R$ 0,00", ...props }, ref) => {
    const [displayValue, setDisplayValue] = React.useState("")

    // Sincronizar valor inicial
    React.useEffect(() => {
      if (value !== undefined) {
        const numericValue = typeof value === 'string' ? parseFloat(value) : value
        if (!isNaN(numericValue) && numericValue > 0) {
          setDisplayValue(applyCurrencyMask((numericValue * 100).toString()))
        } else {
          setDisplayValue("")
        }
      }
    }, [value])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      
      // Aplicar máscara
      const maskedValue = applyCurrencyMask(inputValue)
      setDisplayValue(maskedValue)
      
      // Extrair valor numérico
      const numericValue = removeCurrencyMask(maskedValue)
      
      // Chamar callbacks
      if (onChange) {
        onChange(numericValue)
      }
      if (onValueChange) {
        onValueChange(numericValue)
      }
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Permitir apenas números, backspace, delete, tab, escape, enter
      const allowedKeys = [
        'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
        'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'
      ]
      
      if (allowedKeys.includes(e.key)) {
        return
      }
      
      // Permitir números
      if (e.key >= '0' && e.key <= '9') {
        return
      }
      
      // Bloquear outras teclas
      e.preventDefault()
    }

    return (
      <input
        type="text"
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        {...props}
      />
    )
  }
)

CurrencyInput.displayName = "CurrencyInput"

export { CurrencyInput }
