import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const startTime = Date.now()

    // Teste 1: Verificar conexão básica
    const { data: connectionTest, error: connectionError } = await supabase
      .from("usuarios")
      .select("count")
      .limit(1)

    const connectionTime = Date.now() - startTime

    if (connectionError) {
      return NextResponse.json({
        status: 'erro',
        message: 'Falha na conexão com Supabase',
        details: {
          error: connectionError.message,
          code: connectionError.code,
          hint: connectionError.hint
        },
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    // Teste 2: Listar todas as tabelas
    const { data: tabelas, error: tabelasError } = await supabase
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_schema", "public")
      .order("table_name")

    // Teste 3: Verificar tabelas principais do sistema
    const tabelasPrincipais = [
      'usuarios', 'borderos', 'secretarias', 'tipos', 'direcionamentos',
      'niveis_acesso', 'notificacoes', 'log_atividades', 'bordero_logs',
      'bordero_comentarios', 'configuracoes_sistema'
    ]

    const verificacaoTabelas = []
    
    for (const tabela of tabelasPrincipais) {
      try {
        const { data, error } = await supabase
          .from(tabela)
          .select("*", { count: "exact", head: true })

        verificacaoTabelas.push({
          tabela,
          existe: !error,
          registros: error ? 0 : (data?.length || 0),
          erro: error?.message || null
        })
      } catch (err: any) {
        verificacaoTabelas.push({
          tabela,
          existe: false,
          registros: 0,
          erro: err.message
        })
      }
    }

    // Teste 4: Verificar logs recentes
    const { data: logsRecentes, error: logsError } = await supabase
      .from("log_atividades")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(5)

    // Teste 5: Verificar configurações
    const { data: configuracoes, error: configError } = await supabase
      .from("configuracoes_sistema")
      .select("*")
      .limit(1)

    // Teste 6: Verificar RLS (Row Level Security)
    const { data: rlsInfo, error: rlsError } = await supabase
      .rpc('check_rls_status')
      .single()

    return NextResponse.json({
      status: 'ok',
      message: 'Conexão com Supabase funcionando',
      details: {
        conexao: {
          tempo_resposta_ms: connectionTime,
          url: process.env.SUPABASE_URL?.substring(0, 30) + "...",
          timestamp: new Date().toISOString()
        },
        tabelas: {
          total: tabelas?.length || 0,
          lista: tabelas?.map(t => t.table_name) || [],
          erro: tabelasError?.message || null
        },
        verificacao_sistema: {
          tabelas_principais: verificacaoTabelas,
          total_ok: verificacaoTabelas.filter(t => t.existe).length,
          total_erro: verificacaoTabelas.filter(t => !t.existe).length
        },
        logs_recentes: {
          quantidade: logsRecentes?.length || 0,
          ultimo_log: logsRecentes?.[0] || null,
          erro: logsError?.message || null
        },
        configuracoes: {
          existe: !configError,
          dados: configuracoes?.[0] || null,
          erro: configError?.message || null
        },
        rls: {
          ativo: !rlsError,
          detalhes: rlsInfo || null,
          erro: rlsError?.message || null
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    console.error("Erro na verificação de conexão:", error)
    
    return NextResponse.json({
      status: 'erro_critico',
      message: 'Erro crítico na verificação',
      details: {
        error: error.message,
        stack: error.stack?.substring(0, 500),
        env_vars: {
          supabase_url_exists: !!process.env.SUPABASE_URL,
          service_role_exists: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
          anon_key_exists: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        }
      },
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
