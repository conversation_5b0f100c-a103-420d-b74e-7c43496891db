import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: direcionamentos, error } = await supabase
      .from("direcionamentos")
      .select(`
        *,
        usuarios:direcionamento_usuarios(
          usuario:usuarios(*)
        )
      `)
      .order("nome")

    if (error) {
      console.error("Erro ao buscar direcionamentos:", error)
      return NextResponse.json({ error: "Erro ao buscar direcionamentos" }, { status: 500 })
    }

    return NextResponse.json(direcionamentos)
  } catch (error) {
    console.error("Erro ao buscar direcionamentos:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { nome, valor_minimo, valor_maximo, usuarios } = body

    if (!nome || !nome.trim()) {
      return NextResponse.json({ error: "Nome do direcionamento é obrigatório" }, { status: 400 })
    }

    if (valor_minimo === undefined || valor_maximo === undefined) {
      return NextResponse.json({ error: "Valores mínimo e máximo são obrigatórios" }, { status: 400 })
    }

    if (valor_minimo >= valor_maximo) {
      return NextResponse.json({ error: "O valor máximo deve ser maior que o valor mínimo" }, { status: 400 })
    }

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verificar se já existe um direcionamento com este nome
    const { data: existingDirecionamento, error: checkError } = await supabase
      .from("direcionamentos")
      .select("id")
      .eq("nome", nome.trim())
      .maybeSingle()

    if (checkError) {
      console.error("Erro ao verificar direcionamento:", checkError)
      return NextResponse.json({ error: "Erro ao verificar direcionamento" }, { status: 500 })
    }

    if (existingDirecionamento) {
      return NextResponse.json({ error: "Já existe um direcionamento com este nome" }, { status: 400 })
    }

    // Verificar se há sobreposição de valores com outros direcionamentos
    const { data: overlappingDirecionamentos, error: overlapError } = await supabase
      .from("direcionamentos")
      .select("id, nome, valor_minimo, valor_maximo")
      .or(`and(valor_minimo.lte.${valor_maximo},valor_maximo.gte.${valor_minimo})`)

    if (overlapError) {
      console.error("Erro ao verificar sobreposição:", overlapError)
      return NextResponse.json({ error: "Erro ao verificar sobreposição de valores" }, { status: 500 })
    }

    if (overlappingDirecionamentos && overlappingDirecionamentos.length > 0) {
      return NextResponse.json({ 
        error: `Há sobreposição de valores com o direcionamento "${overlappingDirecionamentos[0].nome}"` 
      }, { status: 400 })
    }

    const { data: direcionamento, error } = await supabase
      .from("direcionamentos")
      .insert({
        nome: nome.trim(),
        valor_minimo,
        valor_maximo
      })
      .select()
      .single()

    if (error) {
      console.error("Erro ao criar direcionamento:", error)
      return NextResponse.json({ error: "Erro ao criar direcionamento" }, { status: 500 })
    }

    // Se há usuários selecionados, criar as associações
    if (usuarios && usuarios.length > 0) {
      const associacoes = usuarios.map((usuarioId: string) => ({
        direcionamento_id: direcionamento.id,
        usuario_id: usuarioId
      }))

      const { error: associacaoError } = await supabase
        .from("direcionamento_usuarios")
        .insert(associacoes)

      if (associacaoError) {
        console.error("Erro ao criar associações:", associacaoError)
        // Não falhar a criação do direcionamento por causa das associações
      }
    }

    return NextResponse.json(direcionamento)
  } catch (error) {
    console.error("Erro ao criar direcionamento:", error)
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 })
  }
}
