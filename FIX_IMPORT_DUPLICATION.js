// Correções para o problema de duplicação durante a importação de borderos

// 1. Modificar a função executeImport para lidar melhor com erros e evitar duplicações
// Localizar a função executeImport no arquivo app/dashboard/borderos/importar/page.tsx
// e substituir pelo código abaixo:

const executeImport = async () => {
  // Resetar estados antes de iniciar a importação
  setImporting(true);
  setPaused(false);
  setCancelled(false);
  setProgressCount(0);
  setSuccessCount(0);
  setErrorCount(0);
  setLogs([]);
  setImportResults(null);
  setImportProgress(0);
  setStep('import');
  
  // Filtrar apenas borderos válidos e não duplicados
  const validRows = mappedData.filter(row => row.status === 'valid' && !row.duplicate);
  totalValidRowsRef.current = validRows.length;
  
  if (validRows.length === 0) {
    toast({
      title: "Nenhum bordero válido",
      description: "Não há borderos válidos para importar.",
      variant: "destructive"
    });
    setImporting(false);
    setStep('validation');
    return;
  }
  
  // Inicializar progresso
  setProgressCount(0);
  setImportProgress(0);
  setLogs([{status:'success', message:`Iniciando importação de ${validRows.length} borderos...`}]);
  
  // Criar um novo AbortController para esta importação
  if (abortControllerRef.current) {
    try {
      abortControllerRef.current.abort();
    } catch (e) {
      console.error("Erro ao abortar controlador anterior:", e);
    }
  }
  
  // Processar em lotes para melhor feedback visual
  const BATCH_SIZE = 5; // Processar em lotes de 5 para atualizar a UI mais frequentemente
  let hasErrors = false;
  
  try {
    for (let i = 0; i < validRows.length; i++) {
      if (cancelledRef.current) {
        setLogs(logs => [{status:'error', message:'Importação cancelada pelo usuário.'}, ...logs]);
        break;
      }
      while (pausedRef.current) await new Promise(res => setTimeout(res, 300));
      
      const row = validRows[i];
      currentRecordRef.current = { bordero_cod: row.mapped.bordero_cod, nome_empresa: row.mapped.nome_empresa };
      
      // Atualizar progresso com animação suave
      const progressPercentage = Math.round(((i+1) / validRows.length) * 100);
      setProgressCount(i+1);
      setImportProgress(prev => {
        // Incrementar gradualmente para dar sensação de progresso contínuo
        const increment = (progressPercentage - prev) / 2;
        return prev + increment;
      });
      
      // A cada BATCH_SIZE registros ou no último registro, atualizar o progresso exato
      if (i % BATCH_SIZE === 0 || i === validRows.length - 1) {
        setTimeout(() => setImportProgress(progressPercentage), 100);
      }
      
      abortControllerRef.current = new AbortController();
      try {
        console.log(`[IMPORT] Enviando registro ${i+1}/${validRows.length}:`, row.mapped);
        const response = await fetch('/api/borderos/import', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ borderos: [row.mapped] }),
          signal: abortControllerRef.current.signal
        });
        
        let result = null;
        try {
          result = await response.json();
        } catch (jsonErr) {
          console.error('[IMPORT] Erro ao parsear JSON da resposta:', jsonErr);
          setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (Resposta não é JSON)`}, ...logs.slice(0,49)]);
          setErrorCount(c => c+1);
          hasErrors = true;
          continue;
        }
        
        if (response.ok && result.success > 0) {
          setSuccessCount(c => c+1);
          setLogs(logs => [{status:'success', message:`Importado: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa}`}, ...logs.slice(0,49)]);
          console.log(`[IMPORT] Sucesso:`, result);
        } else {
          let errorMsg = '';
          if (typeof result?.error === 'string') errorMsg = result.error;
          else if (Array.isArray(result?.details) && result.details.length > 0 && result.details[0]?.error) errorMsg = result.details[0].error;
          else if (result?.details) errorMsg = JSON.stringify(result.details);
          else errorMsg = JSON.stringify(result);
          
          setErrorCount(c => c+1);
          setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${errorMsg})`}, ...logs.slice(0,49)]);
          console.error(`[IMPORT] Falha:`, errorMsg, result);
          hasErrors = true;
          
          // Se o erro for de permissão da materialized view, mostrar mensagem específica
          if (errorMsg.includes("must be owner of materialized view")) {
            toast({
              title: "Erro de Permissão",
              description: "Erro de permissão ao atualizar estatísticas. Contate o administrador do sistema.",
              variant: "destructive"
            });
          }
        }
      } catch (err) {
        if (err.name === 'AbortError') {
          setLogs(logs => [{status:'error', message:'Importação cancelada pelo usuário (AbortController).'}, ...logs]);
          console.warn('[IMPORT] Importação abortada pelo usuário.');
          break;
        }
        
        setErrorCount(c => c+1);
        setLogs(logs => [{status:'error', message:`Falha: ${row.mapped.bordero_cod} - ${row.mapped.nome_empresa} (${err.message || err})`}, ...logs.slice(0,49)]);
        console.error(`[IMPORT] Erro inesperado:`, err);
        hasErrors = true;
      }
    }
  } catch (error) {
    console.error("[IMPORT] Erro crítico durante a importação:", error);
    toast({
      title: "Erro na Importação",
      description: "Ocorreu um erro durante a importação. Verifique os logs para mais detalhes.",
      variant: "destructive"
    });
    hasErrors = true;
  } finally {
    setImporting(false);
    setImportResults({ 
      success: successCount, 
      errors: errorCount,
      hasErrors: hasErrors
    });
    
    // Se não houve nenhum sucesso e houve erros, voltar para a tela de validação
    if (successCount === 0 && hasErrors) {
      toast({
        title: "Importação Falhou",
        description: "A importação falhou completamente. Verifique os erros e tente novamente.",
        variant: "destructive"
      });
      
      // Aguardar um momento antes de voltar para a tela de validação
      setTimeout(() => {
        setStep('validation');
      }, 3000);
    }
  }
};

// 2. Modificar o componente de exibição dos resultados para mostrar mensagem adequada
// Localizar a seção que renderiza os resultados da importação (por volta da linha 1559)
// e substituir pelo código abaixo:

{importResults && (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card className="p-4">
        <div className="flex items-center gap-2">
          <CheckCircle className="w-4 h-4 text-green-600" />
          <span className="text-sm font-medium">Importados</span>
        </div>
        <div className="text-2xl font-bold text-green-600">
          {typeof importResults.success === 'number' ? importResults.success : 0}
        </div>
      </Card>
      <Card className="p-4">
        <div className="flex items-center gap-2">
          <XCircle className="w-4 h-4 text-red-600" />
          <span className="text-sm font-medium">Falhas</span>
        </div>
        <div className="text-2xl font-bold text-red-600">
          {Array.isArray(importResults.errors) ? importResults.errors.length : (typeof importResults.errors === 'number' ? importResults.errors : 0)}
        </div>
      </Card>
    </div>

    {importResults.errors && importResults.errors.length > 0 && (
      <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
        <CardHeader>
          <CardTitle className="text-red-800 dark:text-red-200 flex items-center gap-2">
            <XCircle className="w-5 h-5" />
            Detalhes das Falhas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {importResults.errors.map((error, index) => (
            <div key={index} className="text-sm text-red-700 dark:text-red-300">
              {error.message || "Erro desconhecido durante a importação."}
            </div>
          ))}
        </CardContent>
      </Card>
    )}

    <div className="flex gap-4">
      <Button onClick={() => router.push('/dashboard/borderos')}>
        Ver Borderos
      </Button>
      <Button variant="outline" onClick={handleResetImport}>
        Nova Importação
      </Button>
      {importResults.hasErrors && (
        <Button variant="outline" onClick={() => setStep('validation')}>
          Voltar para Validação
        </Button>
      )}
    </div>
  </div>
)}