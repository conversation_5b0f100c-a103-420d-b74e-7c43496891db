"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { toast } from "@/components/ui/use-toast"
import { useNotify } from "@/components/providers/notification-provider"
import { Plus, Edit, Loader2, Trash2 } from "lucide-react"

interface Secretaria {
  id: string
  nome: string
  slug: string
  valores_total: number
  created_at: string
  updated_at: string
}

interface Bordero {
  id: string
  valor: number
  secretaria_id: string
}

export default function SecretariasPage() {
  const notify = useNotify()
  const [secretarias, setSecretarias] = useState<Secretaria[]>([])
  const [valoresCalculados, setValoresCalculados] = useState<Record<string, number>>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingSecretaria, setEditingSecretaria] = useState<Secretaria | null>(null)
  const [formData, setFormData] = useState({
    nome: "",
    descricao: ""
  })

  useEffect(() => {
    fetchSecretarias()
    calcularValoresSecretarias()
  }, [])

  const calcularValoresSecretarias = async () => {
    try {
      console.log("Iniciando cálculo de valores das secretarias");
      
      // Buscar todos os borderos - usando endpoint de relatórios que sabemos que funciona
      const response = await fetch("/api/relatorios")
      if (!response.ok) {
        console.error("Erro ao buscar dados de relatórios:", response.statusText);
        return;
      }

      const data = await response.json();
      const borderos = Array.isArray(data.borderos) ? data.borderos : [];
      
      console.log("Borderos carregados para cálculo:", borderos.length);

      // Calcular valores por secretaria
      const valores: Record<string, number> = {}
      borderos.forEach((bordero: any) => {
        if (bordero.secretaria?.id) {
          valores[bordero.secretaria.id] = (valores[bordero.secretaria.id] || 0) + (bordero.valor || 0)
        }
      })

      console.log("Valores calculados por secretaria:", valores);
      setValoresCalculados(valores)
    } catch (error) {
      console.error("Erro ao calcular valores das secretarias:", error)
    }
  }

  const fetchSecretarias = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/secretarias")
      if (!response.ok) throw new Error("Erro ao buscar secretarias")

      const data = await response.json()
      setSecretarias(data)
    } catch (error) {
      console.error("Erro ao buscar secretarias:", error)
      toast({
        title: "Erro",
        description: "Não foi possível carregar as secretarias.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSalvar = async () => {
    if (!formData.nome.trim()) {
      toast({
        title: "Erro",
        description: "Nome da secretaria é obrigatório.",
        variant: "destructive",
      })
      return
    }

    setSaving(true)
    try {
      const url = editingSecretaria ? `/api/secretarias/${editingSecretaria.id}` : "/api/secretarias"
      const method = editingSecretaria ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Erro ao ${editingSecretaria ? 'atualizar' : 'criar'} secretaria`)
      }

      const secretaria = await response.json()

      // Usar o novo sistema de notificações
      notify.success(
        editingSecretaria ? "Secretaria Atualizada" : "Secretaria Cadastrada",
        `Secretaria "${secretaria.nome}" foi ${editingSecretaria ? 'atualizada' : 'cadastrada'} com sucesso!`
      )

      setDialogOpen(false)
      setEditingSecretaria(null)
      setFormData({ nome: "", descricao: "" })
      fetchSecretarias() // Recarregar a lista
      calcularValoresSecretarias() // Recalcular valores
    } catch (error: any) {
      console.error(`Erro ao ${editingSecretaria ? 'atualizar' : 'criar'} secretaria:`, error)
      toast({
        title: "Erro",
        description: error.message || `Não foi possível ${editingSecretaria ? 'atualizar' : 'criar'} a secretaria.`,
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditar = (secretaria: Secretaria) => {
    setEditingSecretaria(secretaria)
    setFormData({
      nome: secretaria.nome,
      descricao: ""
    })
    setDialogOpen(true)
  }

  const handleExcluir = async (secretaria: Secretaria) => {
    try {
      const response = await fetch(`/api/secretarias/${secretaria.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erro ao excluir secretaria")
      }

      notify.success(
        "Secretaria Excluída",
        `Secretaria "${secretaria.nome}" foi excluída com sucesso!`
      )

      fetchSecretarias() // Recarregar a lista
      calcularValoresSecretarias() // Recalcular valores
    } catch (error: any) {
      console.error("Erro ao excluir secretaria:", error)
      toast({
        title: "Erro",
        description: error.message || "Não foi possível excluir a secretaria.",
        variant: "destructive",
      })
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Secretarias</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-sky-600 hover:bg-sky-700">
              <Plus className="mr-2 h-4 w-4" />
              Nova Secretaria
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingSecretaria ? "Editar Secretaria" : "Adicionar Nova Secretaria"}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome da Secretaria</Label>
                <Input
                  id="nome"
                  placeholder="Digite o nome da secretaria"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="descricao">Descrição (opcional)</Label>
                <Textarea
                  id="descricao"
                  placeholder="Digite uma descrição para a secretaria"
                  value={formData.descricao}
                  onChange={(e) => setFormData(prev => ({ ...prev, descricao: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setDialogOpen(false)
                  setEditingSecretaria(null)
                  setFormData({ nome: "", descricao: "" })
                }}
                disabled={saving}
              >
                Cancelar
              </Button>
              <Button
                className="bg-sky-600 hover:bg-sky-700"
                onClick={handleSalvar}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  "Salvar"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Secretarias Cadastradas</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Valores Totais</TableHead>
                  <TableHead>Data de Criação</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {secretarias.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      Nenhuma secretaria cadastrada.
                    </TableCell>
                  </TableRow>
                ) : (
                  secretarias.map((secretaria) => (
                    <TableRow key={secretaria.id}>
                      <TableCell className="font-medium">{secretaria.nome}</TableCell>
                      <TableCell>{secretaria.slug}</TableCell>
                      <TableCell>{formatCurrency(valoresCalculados[secretaria.id] || 0)}</TableCell>
                      <TableCell>{new Date(secretaria.created_at).toLocaleDateString("pt-BR")}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditar(secretaria)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Excluir
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja excluir a secretaria "{secretaria.nome}"?
                                  Esta ação não pode ser desfeita.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleExcluir(secretaria)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Excluir
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
