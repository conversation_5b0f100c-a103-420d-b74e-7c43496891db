"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/hooks/use-toast'
import { PDFGenerator } from '@/lib/pdf-generator'
import { Download, FileText, Loader2 } from 'lucide-react'

interface Secretaria {
  id: string
  nome: string
}

interface Tipo {
  id: string
  nome: string
}

interface PDFReportGeneratorProps {
  secretarias: Secretaria[]
  tipos: Tipo[]
}

export function PDFReportGenerator({ secretarias, tipos }: PDFReportGeneratorProps) {
  const [loading, setLoading] = useState(false)
  const [filtros, setFiltros] = useState({
    dataInicio: '',
    dataFim: '',
    secretariaId: '',
    tipoId: '',
    status: ''
  })

  const handleFiltroChange = (campo: string, valor: string) => {
    setFiltros(prev => ({
      ...prev,
      [campo]: valor === 'all' ? '' : valor
    }))
  }

  const generatePDF = async () => {
    setLoading(true)
    try {
      // Buscar dados dos borderôs com filtros
      const params = new URLSearchParams()
      
      if (filtros.dataInicio) params.append('dataInicio', filtros.dataInicio)
      if (filtros.dataFim) params.append('dataFim', filtros.dataFim)
      if (filtros.secretariaId) params.append('secretariaId', filtros.secretariaId)
      if (filtros.tipoId) params.append('tipoId', filtros.tipoId)
      if (filtros.status) params.append('status', filtros.status)

      const response = await fetch(`/api/borderos?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error('Erro ao buscar dados dos borderôs')
      }

      const borderos = await response.json()

      // Buscar dados do usuário
      const userResponse = await fetch('/api/auth/user')
      const userData = userResponse.ok ? await userResponse.json() : { nome: 'Usuário', email: '<EMAIL>' }

      // Preparar dados para o PDF
      const reportData = {
        borderos,
        filtros: {
          dataInicio: filtros.dataInicio,
          dataFim: filtros.dataFim,
          secretaria: filtros.secretariaId ? secretarias.find(s => s.id === filtros.secretariaId)?.nome : undefined,
          tipo: filtros.tipoId ? tipos.find(t => t.id === filtros.tipoId)?.nome : undefined,
          status: filtros.status
        },
        usuario: userData
      }

      // Gerar PDF
      const pdfGenerator = new PDFGenerator()
      pdfGenerator.generateReport(reportData)
      
      // Salvar PDF
      const filename = `relatorio-borderos-${new Date().toISOString().split('T')[0]}.pdf`
      pdfGenerator.save(filename)

      toast({
        title: "PDF Gerado",
        description: `Relatório salvo como ${filename}`,
      })

    } catch (error) {
      console.error('Erro ao gerar PDF:', error)
      toast({
        title: "Erro",
        description: "Não foi possível gerar o relatório PDF",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Gerar Relatório PDF
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="dataInicio">Data Início</Label>
            <Input
              id="dataInicio"
              type="date"
              value={filtros.dataInicio}
              onChange={(e) => handleFiltroChange('dataInicio', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dataFim">Data Fim</Label>
            <Input
              id="dataFim"
              type="date"
              value={filtros.dataFim}
              onChange={(e) => handleFiltroChange('dataFim', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="secretaria">Secretaria</Label>
            <Select value={filtros.secretariaId || 'all'} onValueChange={(value) => handleFiltroChange('secretariaId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Todas as secretarias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as secretarias</SelectItem>
                {secretarias.map((secretaria) => (
                  <SelectItem key={secretaria.id} value={secretaria.id}>
                    {secretaria.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tipo">Tipo</Label>
            <Select value={filtros.tipoId || 'all'} onValueChange={(value) => handleFiltroChange('tipoId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Todos os tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                {tipos.map((tipo) => (
                  <SelectItem key={tipo.id} value={tipo.id}>
                    {tipo.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={filtros.status || 'all'} onValueChange={(value) => handleFiltroChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Todos os status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os status</SelectItem>
                <SelectItem value="novo">Novo</SelectItem>
                <SelectItem value="analise">Em Análise</SelectItem>
                <SelectItem value="assinado">Assinado</SelectItem>
                <SelectItem value="pago">Pago</SelectItem>
                <SelectItem value="corrigir">Corrigir</SelectItem>
                <SelectItem value="cancelado">Cancelado</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end">
          <Button 
            onClick={generatePDF} 
            disabled={loading}
            className="bg-sky-600 hover:bg-sky-700"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando PDF...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Gerar Relatório PDF
              </>
            )}
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <p>• O relatório incluirá cabeçalho com logo e informações do sistema</p>
          <p>• Resumo geral com totais e estatísticas</p>
          <p>• Tabela detalhada de todos os borderôs</p>
          <p>• Rodapé com numeração de páginas</p>
        </div>
      </CardContent>
    </Card>
  )
}
