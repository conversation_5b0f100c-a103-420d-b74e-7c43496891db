/**
 * Inicialização de serviços do servidor
 * 
 * Este arquivo é carregado automaticamente quando o servidor é iniciado
 * e inicializa serviços como o job de atualização do dashboard.
 */

import { startDashboardRefreshJob } from "@/lib/dashboard-refresh-job";

// Verificar se estamos em ambiente de produção
const isProduction = process.env.NODE_ENV === 'production';

// Iniciar o job de atualização do dashboard apenas em produção
if (isProduction) {
  console.log('🚀 Iniciando serviços do servidor em ambiente de produção');
  startDashboardRefreshJob();
} else {
  console.log('⚠️ Ambiente de desenvolvimento detectado. Jobs agendados não serão iniciados automaticamente.');
  console.log('   Para testar, chame manualmente startDashboardRefreshJob() ou use o endpoint /api/admin/process-dashboard-queue');
}

// Exportar uma função vazia para evitar erros de importação
export function initServer() {
  return {
    status: 'initialized'
  };
}

// Inicializar o servidor
initServer();