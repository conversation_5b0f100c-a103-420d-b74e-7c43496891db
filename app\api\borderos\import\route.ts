import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { logBorderoComplete } from "@/lib/bordero-logger"

export async function POST(request: Request) {
  try {
    console.log('🔔 [API] Início do handler POST /api/borderos/import');
    // Verificar autenticação
    const cookieStore = cookies()
    const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore })
    const { data: { session }, error: authError } = await supabaseAuth.auth.getSession()

    if (authError || !session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 })
    }

    const body = await request.json()
    const { borderos } = body

    if (!borderos || !Array.isArray(borderos) || borderos.length === 0) {
      return NextResponse.json({ error: "Lista de borderos é obrigatória" }, { status: 400 })
    }

    console.log('📦 Payload recebido (primeiras 3 linhas):', Array.isArray(borderos) ? borderos.slice(0, 3) : borderos);
    console.log('Total de registros recebidos:', Array.isArray(borderos) ? borderos.length : 0);

    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Buscar dados do usuário para logs
    const { data: usuario } = await supabase
      .from("usuarios")
      .select("nome")
      .eq("id", session.user.id)
      .single()

    const results = {
      success: 0,
      errors: [] as any[]
    }

    // Processar cada bordero
    for (let i = 0; i < borderos.length; i++) {
      const borderoData = borderos[i]

      try {
        console.log(`📋 Processando bordero ${i + 1}/${borderos.length}:`, borderoData.bordero_cod)

        // Validar dados obrigatórios
        if (!borderoData.bordero_cod || !borderoData.nome_empresa || !borderoData.valor) {
          const error = "Campos obrigatórios ausentes (bordero_cod, nome_empresa, valor)"
          console.error(`❌ Erro no bordero ${borderoData.bordero_cod}:`, error)
          results.errors.push({
            bordero: borderoData,
            error
          })
          continue
        }

        // Verificar se o código já existe
        const { data: existingBordero, error: checkError } = await supabase
          .from("borderos")
          .select("id")
          .eq("bordero_cod", borderoData.bordero_cod)
          .maybeSingle()

        if (checkError) {
          console.error(`❌ Erro ao verificar bordero existente ${borderoData.bordero_cod}:`, checkError)
          results.errors.push({
            bordero: borderoData,
            error: `Erro ao verificar código: ${checkError.message}`
          })
          continue
        }

        if (existingBordero) {
          const error = `Código ${borderoData.bordero_cod} já existe`
          console.error(`❌ ${error}`)
          results.errors.push({
            bordero: borderoData,
            error
          })
          continue
        }

        // Processar data com validação robusta
        let processedDate: string
        try {
          if (borderoData.data) {
            const dateValue = borderoData.data
            let date: Date

            // Se for string no formato YYYY-MM-DD
            if (typeof dateValue === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
              date = new Date(dateValue + 'T00:00:00.000Z')
            }
            // Se for número (data serial do Excel)
            else if (typeof dateValue === 'number' || (!isNaN(Number(dateValue)) && Number(dateValue) > 25000)) {
              const excelEpoch = new Date(1900, 0, 1)
              const daysOffset = Number(dateValue) - 2
              date = new Date(excelEpoch.getTime() + daysOffset * 24 * 60 * 60 * 1000)
            }
            // Outros formatos
            else {
              date = new Date(dateValue)
            }

            // Validar se a data é válida e razoável
            if (isNaN(date.getTime()) || date.getFullYear() < 1900 || date.getFullYear() > 2100) {
              throw new Error(`Data inválida: ${dateValue}`)
            }

            processedDate = date.toISOString()
          } else {
            processedDate = new Date().toISOString()
          }
        } catch (dateError: any) {
          console.error(`❌ Erro ao processar data do bordero ${borderoData.bordero_cod}:`, dateError)
          results.errors.push({
            bordero: borderoData,
            error: `Erro na data: ${dateError.message}`
          })
          continue
        }

        // Preparar dados para inserção com validação de tipos
        const insertData = {
          bordero_cod: String(borderoData.bordero_cod).trim(),
          nome_empresa: String(borderoData.nome_empresa).trim(),
          valor: Number.parseFloat(String(borderoData.valor)),
          data: processedDate,
          status: 'novo', // Garantir que todos os borderos importados sejam "novo"
          secretaria_id: borderoData.secretaria_id ? String(borderoData.secretaria_id) : null,
          tipo_id: borderoData.tipo_id ? String(borderoData.tipo_id) : null,
          observacao: borderoData.observacao ? String(borderoData.observacao).trim() : null,
          responsavel_id: session.user.id
        }

        // Validar valor numérico
        if (isNaN(insertData.valor) || insertData.valor <= 0) {
          const error = `Valor inválido: ${borderoData.valor}`
          console.error(`❌ ${error}`)
          results.errors.push({
            bordero: borderoData,
            error
          })
          continue
        }

        // Validar secretaria_id (obrigatório)
        if (!insertData.secretaria_id) {
          results.errors.push({
            bordero: borderoData,
            error: `Secretaria obrigatória não informada ou não encontrada.`
          });
          continue;
        }
        const { data: secretariaExists, error: secretariaError } = await supabase
          .from("secretarias")
          .select("id")
          .eq("id", insertData.secretaria_id)
          .maybeSingle();
        if (secretariaError || !secretariaExists) {
          results.errors.push({
            bordero: borderoData,
            error: `Secretaria inválida ou não encontrada (ID: ${insertData.secretaria_id})`
          });
          continue;
        }
        // Validar tipo_id (se não encontrado, usar 'Sem Tipo')
        if (!insertData.tipo_id) {
          // Buscar tipo 'Sem Tipo'
          let tipoIdSemTipo = null;
          const { data: tipoSemTipo } = await supabase
            .from("tipos")
            .select("id")
            .eq("nome", "Sem Tipo")
            .maybeSingle();
          if (tipoSemTipo && tipoSemTipo.id) {
            tipoIdSemTipo = tipoSemTipo.id;
          } else {
            // Criar tipo 'Sem Tipo'
            const { data: novoTipo, error: tipoCreateError } = await supabase
              .from("tipos")
              .insert({ nome: "Sem Tipo", descricao: "Tipo padrão para importação" })
              .select()
              .single();
            if (novoTipo && novoTipo.id) {
              tipoIdSemTipo = novoTipo.id;
            }
          }
          insertData.tipo_id = tipoIdSemTipo;
        } else {
          // Validar tipo_id existente
          const { data: tipoExists, error: tipoError } = await supabase
            .from("tipos")
            .select("id")
            .eq("id", insertData.tipo_id)
            .maybeSingle();
          if (tipoError || !tipoExists) {
            // Buscar ou criar 'Sem Tipo'
            let tipoIdSemTipo = null;
            const { data: tipoSemTipo } = await supabase
              .from("tipos")
              .select("id")
              .eq("nome", "Sem Tipo")
              .maybeSingle();
            if (tipoSemTipo && tipoSemTipo.id) {
              tipoIdSemTipo = tipoSemTipo.id;
            } else {
              const { data: novoTipo, error: tipoCreateError } = await supabase
                .from("tipos")
                .insert({ nome: "Sem Tipo", descricao: "Tipo padrão para importação" })
                .select()
                .single();
              if (novoTipo && novoTipo.id) {
                tipoIdSemTipo = novoTipo.id;
              }
            }
            insertData.tipo_id = tipoIdSemTipo;
          }
        }
        // Logar valores recebidos
        console.log(`Recebido secretaria_id:`, insertData.secretaria_id, 'tipo_id:', insertData.tipo_id);

        console.log('📝 Dados para inserção:', insertData);

        // Forçar status "novo" para todos os borderos importados
        insertData.status = 'novo';
        
        // Validar status do bordero (apenas para log, já que estamos forçando "novo")
        if (borderoData.status && borderoData.status !== 'novo') {
          console.log(`⚠️ Status original ignorado para bordero ${borderoData.bordero_cod}: ${borderoData.status}. Usando 'novo'.`)
        }

        // Logar valores antes da inserção
        console.log(`📝 Tentando inserir bordero:`, {
          codigo: insertData.bordero_cod,
          empresa: insertData.nome_empresa,
          valor: insertData.valor,
          data: insertData.data,
          status: insertData.status,
          secretaria_id: insertData.secretaria_id,
          tipo_id: insertData.tipo_id,
          responsavel_id: insertData.responsavel_id
        })

        // Inserir bordero
        const { data: novoBordero, error: insertError } = await supabase
          .from("borderos")
          .insert(insertData)
          .select()
          .single()

        if (insertError) {
          console.error('❌ Erro ao inserir bordero:', insertError, insertData);
          console.error('Detalhes do erro:', {
            code: insertError.code,
            message: insertError.message,
            details: insertError.details,
            hint: insertError.hint
          })
          results.errors.push({
            bordero: borderoData,
            error: `Erro de inserção: ${insertError.message} (${insertError.code})`
          })
          continue
        }

        console.log(`✅ Bordero ${borderoData.bordero_cod} inserido com sucesso:`, novoBordero)

        // Registrar log de criação (não crítico)
        try {
          await logBorderoComplete(supabase, {
            borderoId: novoBordero.id,
            usuarioId: session.user.id,
            acao: "criar",
            detalhes: {
              tipo: 'criacao',
              bordero_cod: borderoData.bordero_cod,
              nome_empresa: borderoData.nome_empresa,
              valor: insertData.valor,
              criado_em: new Date().toISOString(),
              criado_por: usuario?.nome || 'Usuário desconhecido',
              origem: 'importacao_excel'
            },
            ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
            userAgent: request.headers.get("user-agent") || "unknown"
          })
        } catch (logError) {
          console.error("⚠️ Erro ao registrar log (não crítico):", logError)
        }

        results.success++
      } catch (error: any) {
        console.error(`❌ Erro inesperado ao processar bordero ${borderoData.bordero_cod}:`, error)
        results.errors.push({
          bordero: borderoData,
          error: error.message || "Erro interno inesperado"
        })
      }
    }

    console.log(`📊 Importação finalizada: ${results.success} sucessos, ${results.errors.length} erros`)

    // Registrar log da importação
    try {
      await supabase
        .from("log_atividades")
        .insert({
          usuario_id: session.user.id,
          acao: "importacao_excel",
          entidade: "bordero",
          detalhes: {
            total_processados: borderos.length,
            sucessos: results.success,
            erros: results.errors.length,
            arquivo_origem: "excel_import",
            timestamp: new Date().toISOString()
          },
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
          user_agent: request.headers.get("user-agent") || "unknown"
        })
    } catch (logError) {
      console.error("⚠️ Erro ao registrar log de importação (não crítico):", logError)
    }

    // Retornar resultados detalhados
    const response = {
      success: results.success,
      errors: results.errors.length,
      total: borderos.length,
      details: results.errors.slice(0, 10), // Primeiros 10 erros para debug
      message: `Importação concluída: ${results.success} borderos importados, ${results.errors.length} erros`
    }

    console.log(`📋 Resposta da importação:`, response)
    return NextResponse.json(response)
  } catch (error: any) {
    // Logar o erro completo (stack trace) no terminal
    console.error('💥 Erro crítico na API de importação:', error && error.stack ? error.stack : error);
    // Retornar sempre JSON, nunca HTML
    return NextResponse.json({
      error: 'Erro interno do servidor durante a importação. Consulte os logs do backend para detalhes.',
      details: error && error.message ? error.message : String(error),
      stack: error && error.stack ? error.stack : undefined
    }, { status: 500 });
  }
}
