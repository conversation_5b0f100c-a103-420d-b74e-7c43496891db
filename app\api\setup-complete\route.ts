import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log("Executando setup completo do sistema...")

    const results = []

    // 1. Testar se configuracoes_sistema existe
    try {
      const { data: configTest, error: configError } = await supabase
        .from('configuracoes_sistema')
        .select('*')
        .limit(1)

      if (configError && configError.code === '42P01') {
        // Tabela não existe, tentar criar via inserção
        const { error: insertError } = await supabase
          .from('configuracoes_sistema')
          .insert({
            nome: 'CRM de Bordero',
            modo_escuro: false,
            notificacoes_email: true
          })

        if (insertError) {
          results.push({
            step: 'configuracoes_sistema',
            success: false,
            message: 'Tabela não existe e precisa ser criada manualmente',
            error: insertError.message
          })
        } else {
          results.push({
            step: 'configuracoes_sistema',
            success: true,
            message: 'Tabela criada e configurada com sucesso'
          })
        }
      } else if (configError) {
        results.push({
          step: 'configuracoes_sistema',
          success: false,
          message: 'Erro ao verificar tabela',
          error: configError.message
        })
      } else {
        results.push({
          step: 'configuracoes_sistema',
          success: true,
          message: `Tabela existe com ${configTest?.length || 0} registros`
        })
      }
    } catch (error: any) {
      results.push({
        step: 'configuracoes_sistema',
        success: false,
        message: 'Erro inesperado',
        error: error.message
      })
    }

    // 2. Verificar niveis_acesso
    try {
      const { data: niveisData, error: niveisError } = await supabase
        .from('niveis_acesso')
        .select('nome, permissoes')

      if (niveisError) {
        results.push({
          step: 'niveis_acesso',
          success: false,
          message: 'Erro ao verificar níveis de acesso',
          error: niveisError.message
        })
      } else {
        const niveisComPermissoes = niveisData.filter(n => n.permissoes && Object.keys(n.permissoes).length > 0)
        results.push({
          step: 'niveis_acesso',
          success: true,
          message: `${niveisData.length} níveis encontrados, ${niveisComPermissoes.length} com permissões configuradas`,
          data: niveisData
        })
      }
    } catch (error: any) {
      results.push({
        step: 'niveis_acesso',
        success: false,
        message: 'Erro inesperado',
        error: error.message
      })
    }

    // 3. Verificar tabelas de log
    try {
      const { data: logData, error: logError } = await supabase
        .from('log_atividades')
        .select('id')
        .limit(1)

      if (logError && logError.code === '42P01') {
        results.push({
          step: 'log_atividades',
          success: false,
          message: 'Tabela log_atividades não existe'
        })
      } else if (logError) {
        results.push({
          step: 'log_atividades',
          success: false,
          message: 'Erro ao verificar log_atividades',
          error: logError.message
        })
      } else {
        results.push({
          step: 'log_atividades',
          success: true,
          message: 'Tabela log_atividades existe'
        })
      }
    } catch (error: any) {
      results.push({
        step: 'log_atividades',
        success: false,
        message: 'Erro inesperado',
        error: error.message
      })
    }

    // 4. Testar APIs
    try {
      // Testar API de configurações
      const configResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/configuracoes/sistema`)
      
      if (configResponse.ok) {
        const configData = await configResponse.json()
        results.push({
          step: 'api_configuracoes',
          success: true,
          message: 'API de configurações funcionando',
          data: configData
        })
      } else {
        results.push({
          step: 'api_configuracoes',
          success: false,
          message: 'API de configurações com erro',
          error: `Status: ${configResponse.status}`
        })
      }
    } catch (error: any) {
      results.push({
        step: 'api_configuracoes',
        success: false,
        message: 'Erro ao testar API',
        error: error.message
      })
    }

    const allSuccess = results.every(r => r.success)
    const sqlNeeded = !allSuccess

    return NextResponse.json({
      success: allSuccess,
      message: allSuccess ? 'Sistema configurado com sucesso!' : 'Algumas configurações precisam ser feitas manualmente',
      results,
      sqlNeeded,
      sql: sqlNeeded ? `
-- Execute este SQL no Supabase SQL Editor para completar a configuração:

-- 1. Criar tabela configuracoes_sistema
CREATE TABLE IF NOT EXISTS configuracoes_sistema (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
  modo_escuro BOOLEAN DEFAULT false,
  notificacoes_email BOOLEAN DEFAULT true,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema FOR ALL USING (true);

INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
SELECT 'CRM de Bordero', false, true
WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);

-- 2. Atualizar niveis_acesso com permissões
ALTER TABLE niveis_acesso ADD COLUMN IF NOT EXISTS permissoes JSONB DEFAULT '{}';

UPDATE niveis_acesso 
SET permissoes = CASE 
  WHEN nome = 'Administrador' THEN '{"dashboard": true, "borderos": true, "secretarias": true, "direcionamentos": true, "tipos": true, "usuarios": true, "relatorios": true, "configuracoes": true}'::jsonb
  WHEN nome = 'Usuário' THEN '{"dashboard": true, "borderos": true, "secretarias": false, "direcionamentos": false, "tipos": false, "usuarios": false, "relatorios": false, "configuracoes": false}'::jsonb
  ELSE '{"dashboard": true, "borderos": false, "secretarias": false, "direcionamentos": false, "tipos": false, "usuarios": false, "relatorios": false, "configuracoes": false}'::jsonb
END
WHERE permissoes IS NULL OR permissoes = '{}';
      ` : null
    })

  } catch (error) {
    console.error("Erro no setup completo:", error)
    return NextResponse.json({
      success: false,
      message: "Erro interno do servidor",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
