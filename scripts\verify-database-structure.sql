-- Verificar estrutura da tabela bordero_logs
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'bordero_logs' 
ORDER BY ordinal_position;

-- Verificar se a tabela existe
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'bordero_logs'
);

-- Verificar estrutura da tabela log_atividades
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'log_atividades' 
ORDER BY ordinal_position;

-- Verificar alguns logs recentes para validar estrutura
SELECT 
    id,
    bordero_id,
    usuario_id,
    acao,
    detalhes,
    data_hora,
    created_at
FROM bordero_logs 
ORDER BY created_at DESC 
LIMIT 5;

-- Verificar logs de atividades recentes
SELECT 
    id,
    usuario_id,
    acao,
    entidade,
    entidade_id,
    detalhes,
    ip,
    user_agent,
    created_at
FROM log_atividades 
WHERE entidade = 'bordero'
ORDER BY created_at DESC 
LIMIT 5;
