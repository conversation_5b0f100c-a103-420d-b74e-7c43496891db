"use client"

import { useState, use<PERSON><PERSON><PERSON>, useMemo } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/hooks/use-toast"
import { ArrowLeft, Upload, FileSpreadsheet, CheckCircle, AlertTriangle, XCircle, Download, Settings, Eye } from "lucide-react"
import { useDropzone } from "react-dropzone"
import * as XLSX from "xlsx"
import Link from "next/link"

// Função para normalizar texto removendo acentos, espaços extras e caracteres especiais
function normalizeText(text: string | number | boolean | null | undefined): string {
  if (!text && text !== 0) return ''; // Retorna string vazia para valores falsy, exceto 0
  
  // Converte para string e remove caracteres de controle e espaços extras
  let normalized = String(text)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove caracteres de controle
    .replace(/\s+/g, ' ') // Substitui múltiplos espaços por um único
    .trim();
    
  // Se após a limpeza a string estiver vazia, retorna vazio
  if (!normalized) return '';
  
  // Aplica transformações de normalização
  return normalized
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais, mantendo espaços
    .replace(/\s+/g, '') // Remove todos os espaços
    .replace(/^(?:sec(?:retaria)?|secre?t?)(?:de|do|da|das|dos)?/i, '') // Remove prefixos comuns
    .replace(/[^a-z0-9]/g, ''); // Remove qualquer caractere que não seja letra ou número
}

interface ExcelRow {
  [key: string]: any
}

interface MappedRow {
  original: ExcelRow
  mapped: {
    bordero_cod?: string
    nome_empresa?: string
    valor?: number
    tipo_id?: number
    secretaria_id?: number
    direcionamento_id?: number
    status?: string
    data?: string
  }
  status: 'valid' | 'warning' | 'error'
  errors: string[]
}

interface ColumnMapping {
  [excelColumn: string]: string
}

export default function ImportarBorderosPage() {
  const router = useRouter()
  const [step, setStep] = useState<'upload' | 'mapping' | 'validation' | 'import'>('upload')
  const [excelData, setExcelData] = useState<ExcelRow[]>([])
  const [excelColumns, setExcelColumns] = useState<string[]>([])
  const [columnMapping, setColumnMapping] = useState<ColumnMapping>({})
  const [mappedData, setMappedData] = useState<MappedRow[]>([])
  const [secretarias, setSecretarias] = useState<any[]>([])
  const [tipos, setTipos] = useState<any[]>([])
  const [direcionamentos, setDirecionamentos] = useState<any[]>([])
  const [importing, setImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importResults, setImportResults] = useState<any>(null)

  // Novas opções de configuração
  const [hasHeader, setHasHeader] = useState(true)
  const [skipLines, setSkipLines] = useState(0)
  const [previewData, setPreviewData] = useState<ExcelRow[]>([])
  const [inconsistentData, setInconsistentData] = useState<{
    secretarias: string[]
    direcionamentos: string[]
    tipos: string[]
  }>({ secretarias: [], direcionamentos: [], tipos: [] })
  const [validationFilter, setValidationFilter] = useState<'all' | 'valid' | 'warning' | 'error'>('all')

  // Funções auxiliares para valores dos Selects
  const getSelectValue = useCallback((fieldName: string) => {
    const found = Object.entries(columnMapping).find(([_, value]) => value === fieldName)
    const result = found?.[0] || 'unmapped'
    return result && result.trim() !== '' ? result : 'unmapped'
  }, [columnMapping])

  // Configuração do dropzone com processamento avançado
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        // Ler dados brutos primeiro
        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

        if (rawData.length === 0) {
          toast({
            title: "Erro",
            description: "Planilha vazia ou inválida.",
            variant: "destructive"
          })
          return
        }

        console.log('📊 Dados brutos da planilha:', rawData.slice(0, 3))

        // Aplicar configurações de skip lines
        let processedData = rawData.slice(skipLines)
        console.log(`⏭️ Pulando ${skipLines} linhas. Dados após pular:`, processedData.slice(0, 3))

        let headers: string[]
        let rows: any[]

        if (hasHeader && processedData.length > 0) {
          headers = processedData[0] as string[]
          rows = processedData.slice(1)
          console.log('📋 Cabeçalhos detectados:', headers)
        } else {
          // Gerar cabeçalhos baseados no padrão comum das planilhas
          const maxCols = Math.max(...processedData.map((row: any) => row.length))
          headers = []
          for (let i = 0; i < maxCols; i++) {
            switch (i) {
              case 0: headers.push('Bordero'); break;
              case 1: headers.push('Valor'); break;
              case 2: headers.push('Data'); break;
              case 3: headers.push('Empresa'); break;
              case 4: headers.push('Secretaria'); break;
              case 5: headers.push('Devolução'); break;
              case 6: headers.push('OBS'); break;
              default: headers.push(`Coluna ${i + 1}`); break;
            }
          }
          rows = processedData
          console.log('🔤 Cabeçalhos gerados automaticamente:', headers)
        }

        // Converter para objetos, filtrando linhas vazias
        const excelRows = rows
          .map((row: any) => {
            const obj: ExcelRow = {}
            headers.forEach((header, index) => {
              obj[header] = row[index] || ''
            })
            return obj
          })
          .filter(row => {
            // Filtrar linhas que têm pelo menos um campo não vazio
            return Object.values(row).some(val =>
              val !== undefined && val !== null && val !== '' && String(val).trim() !== ''
            )
          })

        console.log(`✅ Processadas ${excelRows.length} linhas válidas de ${rows.length} totais`)

        setExcelColumns(headers)
        setExcelData(excelRows)

        // Mapeamento automático inteligente baseado nos nomes das colunas
        const autoMapping: Record<string, string> = {}
        headers.forEach(header => {
          const headerLower = header.toLowerCase().trim()

          // Mapeamento para código do bordero
          if (headerLower.includes('bordero') || headerLower.includes('codigo') || headerLower === 'bordero') {
            autoMapping[header] = 'bordero_cod'
          }
          // Mapeamento para valor
          else if (headerLower.includes('valor') || headerLower === 'valor') {
            autoMapping[header] = 'valor'
          }
          // Mapeamento para data
          else if (headerLower.includes('data') || headerLower === 'data') {
            autoMapping[header] = 'data'
          }
          // Mapeamento para empresa
          else if (headerLower.includes('empresa') || headerLower.includes('nome') || headerLower === 'empresa') {
            autoMapping[header] = 'nome_empresa'
          }
          // Mapeamento para secretaria
          else if (headerLower.includes('secretaria') || headerLower === 'secretaria') {
            autoMapping[header] = 'secretaria_id'
          }
          // Mapeamento para observação
          else if (headerLower.includes('obs') || headerLower.includes('observ') || headerLower.includes('devolucao')) {
            autoMapping[header] = 'observacao'
          }
        })

        setColumnMapping(autoMapping)

        // Gerar preview das primeiras 5 linhas brutas (antes do processamento)
        setPreviewData(rawData.slice(0, 5))

        // Só avançar para mapping se tiver dados processados
        if (excelRows.length > 0) {
          setStep('mapping')
        }

        toast({
          title: "✅ Planilha Processada",
          description: `${excelRows.length} linhas válidas encontradas. Configure o mapeamento.`
        })
      } catch (error) {
        console.error('❌ Erro ao processar planilha:', error)
        toast({
          title: "Erro",
          description: "Erro ao processar a planilha. Verifique o formato do arquivo.",
          variant: "destructive"
        })
      }
    }
    reader.readAsArrayBuffer(file)
  }, [hasHeader, skipLines])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1
  })

  // Carregar dados de referência
  const loadReferenceData = async () => {
    try {
      console.log('Carregando dados de referência...')
      const [secretariasRes, tiposRes, direcionamentosRes] = await Promise.all([
        fetch('/api/secretarias'),
        fetch('/api/tipos'),
        fetch('/api/direcionamentos')
      ])

      if (secretariasRes.ok && tiposRes.ok && direcionamentosRes.ok) {
        const secretariasData = await secretariasRes.json()
        const tiposData = await tiposRes.json()
        const direcionamentosData = await direcionamentosRes.json()
        
        console.log('Dados carregados:')
        console.log('- Secretarias:', secretariasData.map((s: any) => s.nome))
        console.log('- Tipos:', tiposData.map((t: any) => t.nome))
        console.log('- Direcionamentos:', direcionamentosData.map((d: any) => d.nome))
        
        setSecretarias(secretariasData)
        setTipos(tiposData)
        setDirecionamentos(direcionamentosData)
      }
    } catch (error) {
      console.error('Erro ao carregar dados de referência:', error)
    }
  }

  // Validar dados mapeados
  const validateMappedData = () => {
    const inconsistent = {
      secretarias: new Set<string>(),
      direcionamentos: new Set<string>(),
      tipos: new Set<string>()
    }

    const validated: MappedRow[] = excelData.map(row => {
      const mapped: any = {}
      const errors: string[] = []
      let status: 'valid' | 'warning' | 'error' = 'valid'

      // Mapear colunas
      Object.entries(columnMapping).forEach(([excelCol, systemCol]) => {
        if (systemCol && row[excelCol] !== undefined) {
          let value = String(row[excelCol]).trim()

          // Conversões específicas
          if (systemCol === 'valor' && value) {
            // Limpar formatação brasileira: 39664,55 ou 39.664,55
            let cleanValue = value.toString()
              .replace(/[^\d.,]/g, '') // Remove tudo exceto dígitos, pontos e vírgulas
              .replace(/\./g, '') // Remove pontos (separadores de milhares)
              .replace(',', '.') // Substitui vírgula por ponto (decimal)

            const numValue = parseFloat(cleanValue)
            if (isNaN(numValue) || numValue <= 0) {
              errors.push(`Valor inválido: ${value}`)
              status = 'error'
            } else {
              mapped[systemCol] = numValue
            }
          } else if (systemCol === 'data' && value) {
            try {
              let date: Date | null = null

              // Se for um número (data serial do Excel)
              if (typeof value === 'number' || (!isNaN(Number(value)) && Number(value) > 25000)) {
                // Converter data serial do Excel para JavaScript
                // Excel conta dias desde 1900-01-01, mas tem um bug de ano bissexto
                const excelEpoch = new Date(1900, 0, 1)
                const daysOffset = Number(value) - 2 // Ajuste para o bug do Excel
                date = new Date(excelEpoch.getTime() + daysOffset * 24 * 60 * 60 * 1000)
              }
              // Tentar formato brasileiro DD/MM/YYYY
              else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(String(value))) {
                const [dia, mes, ano] = String(value).split('/')
                date = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia))
              }
              // Tentar formato ISO YYYY-MM-DD
              else if (/^\d{4}-\d{2}-\d{2}$/.test(String(value))) {
                date = new Date(String(value))
              }
              // Tentar outros formatos
              else {
                date = new Date(String(value))
              }

              // Validar se a data é razoável (entre 1900 e 2100)
              if (!date || isNaN(date.getTime()) || date.getFullYear() < 1900 || date.getFullYear() > 2100) {
                errors.push(`Data inválida: ${value}`)
                status = 'error'
              } else {
                mapped[systemCol] = date.toISOString().split('T')[0]
              }
            } catch {
              errors.push(`Data inválida: ${value}`)
              status = 'error'
            }
          } else if (systemCol === 'secretaria_id' && value) {
            console.log('--- VALIDAÇÃO DE SECRETARIA ---');
            console.log('Valor original da planilha:', value);
            
            // Normaliza o valor da planilha
            const normalizedValue = normalizeText(value);
            console.log('Valor normalizado:', normalizedValue);
            
            // Log das secretarias disponíveis para comparação
            console.log('Secretarias disponíveis para comparação:');
            secretarias.forEach(s => {
              const norm = normalizeText(s.nome);
              console.log(`- "${s.nome}" (normalizado: "${norm}")`);
            });
            
            // Tenta encontrar uma correspondência exata ou parcial
            const secretaria = secretarias.find(s => {
              if (!s || !s.nome) return false;
              
              const normalizedSecretaria = normalizeText(s.nome);
              
              // Se um for substring do outro (em qualquer ordem)
              if (normalizedSecretaria.includes(normalizedValue) || 
                  normalizedValue.includes(normalizedSecretaria)) {
                return true;
              }
              
              // Se alguma palavra em comum for significativa (>3 letras)
              const palavrasValor = normalizedValue.split(/\s+/).filter(w => w.length > 3);
              const palavrasSecretaria = normalizedSecretaria.split(/\s+/).filter(w => w.length > 3);
              
              const temPalavraComum = palavrasValor.some(pv => 
                palavrasSecretaria.some(ps => pv === ps)
              );
              
              console.log(`Comparando "${value}" (${normalizedValue}) com "${s.nome}" (${normalizedSecretaria}):`, {
                valorIncluiSecretaria: normalizedValue.includes(normalizedSecretaria),
                secretariaIncluiValor: normalizedSecretaria.includes(normalizedValue),
                palavrasComuns: temPalavraComum
              });
              
              return temPalavraComum;
            });
            
            if (secretaria) {
              console.log(`✅ Encontrada correspondência: ${secretaria.nome} (ID: ${secretaria.id})`);
              mapped[systemCol] = secretaria.id;
            } else {
              console.log('❌ Nenhuma correspondência direta encontrada. Tentando similaridade...');
              
              // Tenta encontrar por correspondência parcial com limite de similaridade
              const similarSecretaria = secretarias.reduce<{s: any, score: number} | null>((best, s) => {
                const normalizedSecretaria = normalizeText(s.nome);
                const similarity = calculateSimilarity(normalizedValue, normalizedSecretaria);
                console.log(`- Similaridade com "${s.nome}": ${(similarity * 100).toFixed(1)}%`);
                
                if (similarity > 0.5 && (!best || similarity > best.score)) {
                  return { s, score: similarity };
                }
                return best;
              }, null);
              
              if (similarSecretaria && similarSecretaria.score > 0.5) {
                console.log(`✅ Melhor correspondência por similaridade: ${similarSecretaria.s.nome} (${(similarSecretaria.score * 100).toFixed(1)}% de similaridade)`);
                mapped[systemCol] = similarSecretaria.s.id;
              } else {
                console.log('❌ Nenhuma correspondência por similaridade encontrada');
                console.log('Valor que causou o erro:', value);
                console.log('Valor normalizado:', normalizedValue);
                inconsistent.secretarias.add(value);
                errors.push(`Secretaria não encontrada: ${value}`);
                status = status === 'valid' ? 'warning' : status;
              }
            }
          } else if (systemCol === 'tipo_id' && value) {
            const normalizedValue = normalizeText(value);
            const tipo = tipos.find(t => {
              const normalizedTipo = normalizeText(t.nome);
              return normalizedTipo.includes(normalizedValue) || 
                     normalizedValue.includes(normalizedTipo) ||
                     normalizedValue.split(' ').some(word => 
                       word.length > 3 && normalizedTipo.includes(word)
                     );
            });
            
            if (tipo) {
              mapped[systemCol] = tipo.id;
            } else {
              inconsistent.tipos.add(value);
              errors.push(`Tipo não encontrado: ${value}`);
              status = status === 'valid' ? 'warning' : status;
            }
          } else if (systemCol === 'direcionamento_id' && value) {
            const normalizedValue = normalizeText(value);
            const direcionamento = direcionamentos.find(d => {
              const normalizedDir = normalizeText(d.nome);
              return normalizedDir.includes(normalizedValue) || 
                     normalizedValue.includes(normalizedDir) ||
                     normalizedValue.split(' ').some(word => 
                       word.length > 3 && normalizedDir.includes(word)
                     );
            });
            
            if (direcionamento) {
              mapped[systemCol] = direcionamento.id;
            } else {
              inconsistent.direcionamentos.add(value);
              errors.push(`Direcionamento não encontrado: ${value}`);
              status = status === 'valid' ? 'warning' : status;
            }
          } else if (systemCol === 'observacao' && value) {
            // Campo de observação - aceitar qualquer texto
            mapped[systemCol] = value
          } else {
            mapped[systemCol] = value
          }
        }
      })

      // Validações obrigatórias
      if (!mapped.bordero_cod) {
        errors.push('Código do bordero é obrigatório')
        status = 'error'
      }
      if (!mapped.nome_empresa) {
        errors.push('Nome da empresa é obrigatório')
        status = 'error'
      }
      if (!mapped.valor || mapped.valor <= 0) {
        errors.push('Valor deve ser maior que zero')
        status = 'error'
      }

      return {
        original: row,
        mapped,
        status,
        errors
      }
    })

    // Atualizar dados inconsistentes
    setInconsistentData({
      secretarias: Array.from(inconsistent.secretarias),
      direcionamentos: Array.from(inconsistent.direcionamentos),
      tipos: Array.from(inconsistent.tipos)
    })

    setMappedData(validated)
    setStep('validation')
  }

  // Função para calcular similaridade entre strings (0 a 1)
  function calculateSimilarity(str1: string, str2: string): number {
    // Se alguma string estiver vazia, retorna 0
    if (str1.length === 0 || str2.length === 0) return 0;
    
    // Se forem iguais, retorna 1
    if (str1 === str2) return 1;
    
    // Se uma string estiver contida na outra, retorna a razão entre os tamanhos
    if (str1.includes(str2)) return str2.length / str1.length;
    if (str2.includes(str1)) return str1.length / str2.length;
    
    // Calcula a distância de Levenshtein
    const track = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    );
    
    for (let i = 0; i <= str1.length; i += 1) track[0][i] = i;
    for (let j = 0; j <= str2.length; j += 1) track[j][0] = j;
    
    for (let j = 1; j <= str2.length; j += 1) {
      for (let i = 1; i <= str1.length; i += 1) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        track[j][i] = Math.min(
          track[j][i - 1] + 1, // deleção
          track[j - 1][i] + 1, // inserção
          track[j - 1][i - 1] + indicator // substituição
        );
      }
    }
    
    const distance = track[str2.length][str1.length];
    return 1 - distance / Math.max(str1.length, str2.length);
  }

  // Executar importação
  const executeImport = async () => {
    const validRows = mappedData.filter(row => row.status === 'valid')

    if (validRows.length === 0) {
      toast({
        title: "Erro",
        description: "Nenhum registro válido para importar.",
        variant: "destructive"
      })
      return
    }

    setImporting(true)
    setImportProgress(0)
    setStep('import')

    try {
      const response = await fetch('/api/borderos/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          borderos: validRows.map(row => row.mapped)
        })
      })

      if (response.ok) {
        const results = await response.json()
        setImportResults(results)
        setImportProgress(100)

        toast({
          title: "Importação concluída",
          description: `${results.success} borderos importados com sucesso.`
        })
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Erro na importação')
      }
    } catch (error: any) {
      console.error('Erro na importação:', error)
      toast({
        title: "Erro na importação",
        description: error.message,
        variant: "destructive"
      })
    } finally {
      setImporting(false)
    }
  }

  // Renderizar status
  const renderStatus = (status: string) => {
    switch (status) {
      case 'valid':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"><CheckCircle className="w-3 h-3 mr-1" />Pronto</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"><AlertTriangle className="w-3 h-3 mr-1" />Atenção</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"><XCircle className="w-3 h-3 mr-1" />Erro</Badge>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/borderos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Importar Borderos</h1>
            <p className="text-muted-foreground">
              Importe borderos em lote através de planilhas Excel
            </p>
          </div>
        </div>
        <Link href="/dashboard/borderos/diagnostico">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Diagnóstico
          </Button>
        </Link>
      </div>

      {/* Indicador de progresso */}
      <div className="flex items-center gap-4">
        {['upload', 'mapping', 'validation', 'import'].map((s, index) => (
          <div key={s} className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === s ? 'bg-blue-600 text-white' :
              ['upload', 'mapping', 'validation', 'import'].indexOf(step) > index ? 'bg-green-600 text-white' :
              'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
            }`}>
              {index + 1}
            </div>
            <span className="text-sm font-medium capitalize">{s === 'upload' ? 'Upload' : s === 'mapping' ? 'Mapeamento' : s === 'validation' ? 'Validação' : 'Importação'}</span>
            {index < 3 && <div className="w-8 h-px bg-gray-300 dark:bg-gray-600" />}
          </div>
        ))}
      </div>

      {/* Conteúdo baseado no step */}
      {step === 'upload' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Upload da Planilha
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              <input {...getInputProps()} />
              <FileSpreadsheet className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              {isDragActive ? (
                <p className="text-lg">Solte o arquivo aqui...</p>
              ) : (
                <div>
                  <p className="text-lg mb-2">Arraste e solte uma planilha Excel aqui</p>
                  <p className="text-sm text-muted-foreground mb-4">ou clique para selecionar</p>
                  <Button variant="outline">Selecionar Arquivo</Button>
                </div>
              )}
            </div>
            {/* Configurações Avançadas de Processamento */}
            <div className="mt-6 bg-slate-50 dark:bg-slate-900 p-4 rounded-lg">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                ⚙️ Configurações de Processamento
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasHeader"
                      checked={hasHeader}
                      onCheckedChange={(checked) => setHasHeader(checked as boolean)}
                    />
                    <Label htmlFor="hasHeader" className="text-sm font-medium">
                      ✅ A primeira linha contém cabeçalhos
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground ml-6">
                    Marque se a primeira linha da planilha contém os nomes das colunas
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="skipLines" className="text-sm font-medium">
                      ⏭️ Pular linhas no início
                    </Label>
                    <Input
                      id="skipLines"
                      type="number"
                      min="0"
                      max="10"
                      value={skipLines}
                      onChange={(e) => setSkipLines(parseInt(e.target.value) || 0)}
                      className="w-24"
                      placeholder="0"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Útil para pular linhas de título, informações extras ou espaços em branco
                  </p>
                </div>
              </div>

              {/* Dicas de uso */}
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/30 rounded border border-blue-200 dark:border-blue-800">
                <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">💡 Dicas de Uso:</h5>
                <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Configure as opções antes de fazer o upload para melhor processamento</li>
                  <li>• Use "Pular linhas" se sua planilha tem títulos ou informações extras no topo</li>
                  <li>• O preview mostrará exatamente como os dados serão interpretados</li>
                </ul>
              </div>
            </div>

            {/* Preview dos Dados Brutos (se arquivo foi carregado) */}
            {previewData.length > 0 && (
              <div className="mt-6 bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  👁️ Preview dos Dados Brutos (5 primeiras linhas)
                </h4>
                <div className="overflow-x-auto">
                  <Table>
                    <TableBody>
                      {previewData.map((row: any, index) => (
                        <TableRow
                          key={index}
                          className={
                            index === 0 && hasHeader
                              ? 'font-medium bg-blue-100 dark:bg-blue-900 border-2 border-blue-300'
                              : index < skipLines
                                ? 'opacity-50 bg-gray-100 dark:bg-gray-800 line-through'
                                : ''
                          }
                        >
                          <TableCell className="w-12 text-xs font-mono text-gray-500">
                            {index + 1}
                          </TableCell>
                          {Array.isArray(row) ? row.map((cell, cellIndex) => (
                            <TableCell key={cellIndex} className="text-xs max-w-[120px] truncate">
                              {String(cell || '').substring(0, 30)}
                              {String(cell || '').length > 30 ? '...' : ''}
                            </TableCell>
                          )) : Object.values(row).map((cell, cellIndex) => (
                            <TableCell key={cellIndex} className="text-xs max-w-[120px] truncate">
                              {String(cell || '').substring(0, 30)}
                              {String(cell || '').length > 30 ? '...' : ''}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Legenda do Preview */}
                <div className="mt-3 text-xs text-muted-foreground space-y-1">
                  <p className="flex items-center gap-2">
                    <span className="w-4 h-4 bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 rounded"></span>
                    {hasHeader ? '🟦 Linha azul = cabeçalho detectado' : '⚪ Sem cabeçalho - colunas serão numeradas automaticamente'}
                  </p>
                  {skipLines > 0 && (
                    <p className="flex items-center gap-2">
                      <span className="w-4 h-4 bg-gray-100 dark:bg-gray-800 rounded opacity-50"></span>
                      🚫 Linhas riscadas = serão puladas ({skipLines} linha{skipLines > 1 ? 's' : ''})
                    </p>
                  )}
                  <p>📊 Total de linhas válidas que serão processadas: <strong>{excelData.length}</strong></p>
                </div>

                <div className="mt-4 flex gap-3">
                  <Button onClick={() => setStep('mapping')} className="bg-blue-600 hover:bg-blue-700">
                    ➡️ Continuar para Mapeamento
                  </Button>
                  <Button variant="outline" onClick={() => {
                    setExcelData([])
                    setPreviewData([])
                    setExcelColumns([])
                  }}>
                    🔄 Carregar Outro Arquivo
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-4 text-sm text-muted-foreground">
              <p>• Formatos aceitos: .xlsx, .xls</p>
              <p>• Colunas recomendadas: Código, Empresa, Valor, Tipo, Secretaria, Status, Data</p>
              <p>• Configure as opções acima antes de fazer o upload</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Mapeamento de Colunas */}
      {step === 'mapping' && excelColumns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              🔗 Mapeamento de Colunas
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Mapeie as colunas da planilha para os campos do sistema. Campos obrigatórios: Código, Empresa e Valor.
            </p>
            {Object.keys(columnMapping).length > 0 && (
              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg">
                <p className="text-sm text-green-800 dark:text-green-200">
                  🤖 <strong>Mapeamento automático aplicado!</strong> Verifique se os campos estão corretos e ajuste se necessário.
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Indicadores de campos obrigatórios */}
            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-blue-800 dark:text-blue-200">📋 Campos Obrigatórios</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Código do Bordero
                </Badge>
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Nome da Empresa
                </Badge>
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                  🔴 Valor
                </Badge>
              </div>
              <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">
                Estes campos são obrigatórios para a importação. Certifique-se de mapeá-los corretamente.
              </p>
            </div>

            {/* Mapeamento dos campos do sistema */}
            <div className="space-y-4">
              {/* Código do Bordero */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">🔢 Código do Bordero *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('bordero_cod')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        // Remove mapeamento anterior
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'bordero_cod') delete newMapping[key]
                        })
                        // Adiciona novo mapeamento
                        if (column !== 'unmapped') newMapping[column] = 'bordero_cod'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`bordero-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o código do bordero
                </div>
              </div>

              {/* Valor */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">💰 Valor *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('valor')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'valor') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'valor'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`valor-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o valor monetário
                </div>
              </div>

              {/* Nome da Empresa */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg">
                <div>
                  <Label className="font-medium text-red-600">🏢 Nome da Empresa *</Label>
                  <p className="text-xs text-muted-foreground">Campo obrigatório</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('nome_empresa')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'nome_empresa') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'nome_empresa'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger className="border-red-300 bg-red-50 dark:bg-red-950/20">
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`empresa-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o nome da empresa
                </div>
              </div>

              {/* Data */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📅 Data</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={getSelectValue('data')}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'data') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'data'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`data-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém a data
                </div>
              </div>

              {/* Secretaria */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">🏛️ Secretaria</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={(() => {
                      const found = Object.entries(columnMapping).find(([_, value]) => value === 'secretaria_id')
                      const result = found?.[0] || 'unmapped'
                      return result && result.trim() !== '' ? result : 'unmapped'
                    })()}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'secretaria_id') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'secretaria_id'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`secretaria-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém a secretaria
                </div>
              </div>

              {/* Tipo de Bordero */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📋 Tipo de Bordero</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={(() => {
                      const found = Object.entries(columnMapping).find(([_, value]) => value === 'tipo_id')
                      const result = found?.[0] || 'unmapped'
                      return result && result.trim() !== '' ? result : 'unmapped'
                    })()}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'tipo_id') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'tipo_id'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`tipo-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém o tipo do bordero
                </div>
              </div>

              {/* Observação */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <div>
                  <Label className="font-medium text-blue-600">📝 Observação</Label>
                  <p className="text-xs text-muted-foreground">Campo opcional</p>
                </div>
                <div>
                  <Select
                    value={(() => {
                      const found = Object.entries(columnMapping).find(([_, value]) => value === 'observacao')
                      const result = found?.[0] || 'unmapped'
                      return result && result.trim() !== '' ? result : 'unmapped'
                    })()}
                    onValueChange={(column) => {
                      if (!column) return
                      setColumnMapping(prev => {
                        const newMapping = { ...prev }
                        Object.keys(newMapping).forEach(key => {
                          if (newMapping[key] === 'observacao') delete newMapping[key]
                        })
                        if (column !== 'unmapped') newMapping[column] = 'observacao'
                        return newMapping
                      })
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a coluna" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unmapped">Não mapear</SelectItem>
                      {excelColumns.filter(col => col && col.trim() !== '').map((col, index) => (
                        <SelectItem key={`observacao-${index}-${col}`} value={col}>{col}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-xs text-muted-foreground">
                  Coluna que contém observações
                </div>
              </div>
            </div>

            <Separator />

            {/* Preview dos dados com mapeamento */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Eye className="w-4 h-4" />
                👁️ Preview dos Dados (5 primeiras linhas)
              </h4>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {excelColumns.map((column, colIndex) => {
                        const mappedField = columnMapping[column]
                        const isRequired = ['bordero_cod', 'nome_empresa', 'valor'].includes(mappedField || '')
                        return (
                          <TableHead key={`header-${colIndex}-${column}`} className={isRequired ? 'bg-red-50 dark:bg-red-950/20' : ''}>
                            <div className="space-y-1">
                              <div className="font-medium">{column}</div>
                              <div className="text-xs text-muted-foreground">
                                {mappedField && mappedField !== 'unmapped' ? (
                                  <Badge variant="outline" className={isRequired ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}>
                                    {mappedField === 'bordero_cod' ? '🔢 Código' :
                                     mappedField === 'nome_empresa' ? '🏢 Empresa' :
                                     mappedField === 'valor' ? '💰 Valor' :
                                     mappedField === 'tipo_id' ? '📋 Tipo' :
                                     mappedField === 'secretaria_id' ? '🏛️ Secretaria' :
                                     mappedField === 'direcionamento_id' ? '📍 Direcionamento' :
                                     mappedField === 'status' ? '📊 Status' :
                                     mappedField === 'data' ? '📅 Data' :
                                     mappedField === 'observacao' ? '📝 Observação' : mappedField}
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-gray-100 text-gray-600">
                                    ❌ Não mapeado
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {excelData.slice(0, 5).map((row, rowIndex) => (
                      <TableRow key={`row-${rowIndex}`}>
                        {excelColumns.map((column, colIndex) => {
                          const mappedField = columnMapping[column]
                          const isRequired = ['bordero_cod', 'nome_empresa', 'valor'].includes(mappedField || '')
                          return (
                            <TableCell key={`cell-${rowIndex}-${colIndex}-${column}`} className={`text-xs ${isRequired ? 'bg-red-50 dark:bg-red-950/20 font-medium' : ''}`}>
                              {String(row[column] || '').substring(0, 30)}
                              {String(row[column] || '').length > 30 ? '...' : ''}
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-xs text-muted-foreground">
                🔴 Colunas destacadas em vermelho = campos obrigatórios mapeados |
                🔵 Colunas azuis = campos opcionais mapeados |
                ⚪ Colunas cinzas = não mapeadas
              </p>
            </div>

            <div className="flex gap-4">
              <Button variant="outline" onClick={() => setStep('upload')}>
                Voltar
              </Button>
              <Button
                onClick={() => {
                  loadReferenceData().then(() => validateMappedData())
                }}
                disabled={
                  !Object.values(columnMapping).includes('bordero_cod') ||
                  !Object.values(columnMapping).includes('valor') ||
                  !Object.values(columnMapping).includes('nome_empresa')
                }
              >
                Continuar para Validação
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Validação */}
      {step === 'validation' && (
        <Card>
          <CardHeader>
            <CardTitle>Validação dos Dados</CardTitle>
            <p className="text-sm text-muted-foreground">
              Revise os dados antes da importação
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Resumo de validação */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium">✅ Válidos</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {mappedData.filter(row => row.status === 'valid').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium">⚠️ Avisos</span>
                </div>
                <div className="text-2xl font-bold text-yellow-600">
                  {mappedData.filter(row => row.status === 'warning').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <XCircle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium">❌ Erros</span>
                </div>
                <div className="text-2xl font-bold text-red-600">
                  {mappedData.filter(row => row.status === 'error').length}
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">📊 Total</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {mappedData.length}
                </div>
              </Card>
            </div>

            {/* Filtros de visualização */}
            <div className="flex items-center gap-4">
              <Label>Filtrar por status:</Label>
              <Select value={validationFilter || 'all'} onValueChange={(value: any) => setValidationFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filtrar por status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="valid">✅ Válidos</SelectItem>
                  <SelectItem value="warning">⚠️ Avisos</SelectItem>
                  <SelectItem value="error">❌ Erros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Dados inconsistentes detectados */}
            {(inconsistentData.secretarias.length > 0 || inconsistentData.direcionamentos.length > 0 || inconsistentData.tipos.length > 0) && (
              <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20">
                <CardHeader>
                  <CardTitle className="text-yellow-800 dark:text-yellow-200 flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Dados Inconsistentes Detectados
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {inconsistentData.secretarias.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Secretarias não encontradas:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.secretarias.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {inconsistentData.direcionamentos.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Direcionamentos não encontrados:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.direcionamentos.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {inconsistentData.tipos.length > 0 && (
                    <div>
                      <p className="font-medium text-sm">Tipos não encontrados:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {inconsistentData.tipos.map(nome => (
                          <Badge key={nome} variant="outline" className="text-yellow-700">
                            {nome}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Código</TableHead>
                    <TableHead>Empresa</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Erros/Avisos</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mappedData
                    .filter(row => validationFilter === 'all' || row.status === validationFilter)
                    .slice(0, 20)
                    .map((row, index) => (
                    <TableRow key={index} className={
                      row.status === 'error' ? 'bg-red-50 dark:bg-red-950/20' :
                      row.status === 'warning' ? 'bg-yellow-50 dark:bg-yellow-950/20' :
                      'bg-green-50 dark:bg-green-950/20'
                    }>
                      <TableCell>{renderStatus(row.status)}</TableCell>
                      <TableCell className="font-mono text-sm">{row.mapped.bordero_cod || '-'}</TableCell>
                      <TableCell className="max-w-[200px] truncate">{row.mapped.nome_empresa || '-'}</TableCell>
                      <TableCell className="font-medium">
                        {row.mapped.valor ?
                          new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(row.mapped.valor)
                          : '-'
                        }
                      </TableCell>
                      <TableCell>
                        {row.errors.length > 0 && (
                          <div className="space-y-1">
                            {row.errors.map((error, i) => (
                              <div key={i} className="text-xs text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded">
                                {error}
                              </div>
                            ))}
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {mappedData.filter(row => validationFilter === 'all' || row.status === validationFilter).length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        Nenhum registro encontrado com o filtro selecionado.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="flex gap-4">
              <Button variant="outline" onClick={() => setStep('mapping')}>
                Voltar
              </Button>
              <Button
                onClick={executeImport}
                disabled={mappedData.filter(row => row.status === 'valid').length === 0}
              >
                Importar {mappedData.filter(row => row.status === 'valid').length} Borderos
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Importação */}
      {step === 'import' && (
        <Card>
          <CardHeader>
            <CardTitle>Importação em Andamento</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Progress value={importProgress} className="w-full" />

            {importing && (
              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Importando borderos... {importProgress}%
                </p>
              </div>
            )}

            {importResults && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="p-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium">Importados</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {importResults.success || 0}
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4 text-red-600" />
                      <span className="text-sm font-medium">Falhas</span>
                    </div>
                    <div className="text-2xl font-bold text-red-600">
                      {importResults.errors?.length || 0}
                    </div>
                  </Card>
                </div>

                <div className="flex gap-4">
                  <Button onClick={() => router.push('/dashboard/borderos')}>
                    Ver Borderos
                  </Button>
                  <Button variant="outline" onClick={() => {
                    setStep('upload')
                    setExcelData([])
                    setMappedData([])
                    setColumnMapping({})
                    setImportResults(null)
                    setImportProgress(0)
                  }}>
                    Nova Importação
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
