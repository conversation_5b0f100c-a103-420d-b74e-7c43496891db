-- Script para corrigir políticas RLS da tabela usuarios
-- Execute este script no Supabase SQL Editor

-- 1. Verificar se a tabela usuarios existe e tem as colunas corretas
DO $$
BEGIN
  -- Verificar se a coluna updated_at existe
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'usuarios' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE usuarios ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Coluna updated_at adicionada à tabela usuarios';
  END IF;

  -- Verificar se a coluna created_at existe
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'usuarios' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE usuarios ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Coluna created_at adicionada à tabela usuarios';
  END IF;
END
$$;

-- 2. Habilitar RLS na tabela usuarios
ALTER TABLE usuarios ENABLE ROW LEVEL SECURITY;

-- 3. Remover políticas existentes
DROP POLICY IF EXISTS "Permitir acesso total a usuarios" ON usuarios;
DROP POLICY IF EXISTS "usuarios_policy" ON usuarios;
DROP POLICY IF EXISTS "usuarios_all_access" ON usuarios;
DROP POLICY IF EXISTS "Permitir tudo para usuários autenticados" ON usuarios;
DROP POLICY IF EXISTS "usuarios_select_policy" ON usuarios;
DROP POLICY IF EXISTS "usuarios_insert_policy" ON usuarios;
DROP POLICY IF EXISTS "usuarios_update_policy" ON usuarios;
DROP POLICY IF EXISTS "usuarios_delete_policy" ON usuarios;

-- 4. Criar políticas RLS corretas
-- Política para SELECT (usuários autenticados podem ver todos os usuários)
CREATE POLICY "usuarios_select_policy" ON usuarios
FOR SELECT TO authenticated
USING (true);

-- Política para INSERT (usuários autenticados podem criar usuários)
CREATE POLICY "usuarios_insert_policy" ON usuarios
FOR INSERT TO authenticated
WITH CHECK (true);

-- Política para UPDATE (usuários autenticados podem atualizar usuários)
CREATE POLICY "usuarios_update_policy" ON usuarios
FOR UPDATE TO authenticated
USING (true)
WITH CHECK (true);

-- Política para DELETE (usuários autenticados podem deletar usuários)
CREATE POLICY "usuarios_delete_policy" ON usuarios
FOR DELETE TO authenticated
USING (true);

-- 5. Criar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_usuarios_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_usuarios_updated_at ON usuarios;
CREATE TRIGGER update_usuarios_updated_at
  BEFORE UPDATE ON usuarios
  FOR EACH ROW
  EXECUTE FUNCTION update_usuarios_updated_at();

-- 6. Verificar políticas criadas
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE tablename = 'usuarios'
ORDER BY policyname;

-- 7. Testar inserção (opcional - descomente para testar)
-- INSERT INTO usuarios (nome, email, senha, nivel_acesso_id)
-- VALUES ('Teste', '<EMAIL>', 'senha123', (SELECT id FROM niveis_acesso LIMIT 1));

SELECT 'Políticas RLS para usuarios configuradas com sucesso!' as status;
SELECT 'Execute: SELECT * FROM pg_policies WHERE tablename = ''usuarios''; para verificar as políticas.' as instrucao;
