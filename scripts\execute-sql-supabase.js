const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

async function executeSQL() {
  try {
    console.log('🔧 Executando SQL para configurar o sistema...\n')

    // 1. Criar configuracoes_sistema usando uma abordagem diferente
    console.log('1. Configurando tabela configuracoes_sistema...')
    
    // Primeiro, vamos tentar criar a tabela usando uma função personalizada
    const createConfigTableSQL = `
      DO $$
      BEGIN
        -- Criar tabela se não existir
        CREATE TABLE IF NOT EXISTS configuracoes_sistema (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          nome VARCHAR(255) NOT NULL DEFAULT 'CRM de Bordero',
          modo_escuro BOOLEAN DEFAULT false,
          notificacoes_email BOOLEAN DEFAULT true,
          logo_url TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Habilitar RLS
        ALTER TABLE configuracoes_sistema ENABLE ROW LEVEL SECURITY;
        
        -- Criar política
        DROP POLICY IF EXISTS "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema;
        CREATE POLICY "Permitir acesso total a configuracoes_sistema" ON configuracoes_sistema
        FOR ALL USING (true);
        
        -- Inserir configuração padrão
        INSERT INTO configuracoes_sistema (nome, modo_escuro, notificacoes_email)
        SELECT 'CRM de Bordero', false, true
        WHERE NOT EXISTS (SELECT 1 FROM configuracoes_sistema);
        
        RAISE NOTICE 'Tabela configuracoes_sistema configurada com sucesso';
      END
      $$;
    `

    try {
      const { error: sqlError } = await supabase.rpc('exec_sql', { sql: createConfigTableSQL })
      if (sqlError) {
        console.log('❌ Erro ao executar SQL via RPC:', sqlError.message)
        console.log('📋 Execute manualmente no Supabase:')
        console.log(createConfigTableSQL)
      } else {
        console.log('✅ Tabela configuracoes_sistema criada via RPC')
      }
    } catch (rpcError) {
      console.log('⚠️ RPC não disponível, tentando método alternativo...')
      
      // Método alternativo: usar uma função que sabemos que existe
      try {
        // Tentar usar uma função built-in do PostgreSQL
        const { data, error } = await supabase
          .from('configuracoes_sistema')
          .select('*')
          .limit(1)

        if (error && error.code === '42P01') {
          console.log('❌ Tabela não existe. Execute o SQL manualmente:')
          console.log(createConfigTableSQL)
        } else {
          console.log('✅ Tabela configuracoes_sistema já existe')
        }
      } catch (err) {
        console.log('❌ Erro ao verificar tabela:', err.message)
      }
    }

    // 2. Criar tabelas de log
    console.log('\n2. Configurando tabelas de log...')
    
    const createLogTablesSQL = `
      DO $$
      BEGIN
        -- Criar tabela log_atividades
        CREATE TABLE IF NOT EXISTS log_atividades (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          usuario_id UUID REFERENCES usuarios(id),
          acao VARCHAR(100) NOT NULL,
          entidade VARCHAR(100) NOT NULL,
          entidade_id UUID,
          detalhes JSONB,
          ip VARCHAR(45),
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Criar tabela bordero_logs
        CREATE TABLE IF NOT EXISTS bordero_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          bordero_id UUID REFERENCES borderos(id),
          usuario_id UUID REFERENCES usuarios(id),
          acao VARCHAR(100) NOT NULL,
          detalhes JSONB,
          data_hora TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Habilitar RLS
        ALTER TABLE log_atividades ENABLE ROW LEVEL SECURITY;
        ALTER TABLE bordero_logs ENABLE ROW LEVEL SECURITY;

        -- Criar políticas
        DROP POLICY IF EXISTS "Permitir acesso total a log_atividades" ON log_atividades;
        CREATE POLICY "Permitir acesso total a log_atividades" ON log_atividades
        FOR ALL USING (true);

        DROP POLICY IF EXISTS "Permitir acesso total a bordero_logs" ON bordero_logs;
        CREATE POLICY "Permitir acesso total a bordero_logs" ON bordero_logs
        FOR ALL USING (true);
        
        RAISE NOTICE 'Tabelas de log configuradas com sucesso';
      END
      $$;
    `

    try {
      const { error: logSqlError } = await supabase.rpc('exec_sql', { sql: createLogTablesSQL })
      if (logSqlError) {
        console.log('❌ Erro ao criar tabelas de log via RPC:', logSqlError.message)
        console.log('📋 Execute manualmente no Supabase:')
        console.log(createLogTablesSQL)
      } else {
        console.log('✅ Tabelas de log criadas via RPC')
      }
    } catch (rpcError) {
      console.log('⚠️ RPC não disponível para tabelas de log')
      console.log('📋 Execute manualmente no Supabase:')
      console.log(createLogTablesSQL)
    }

    // 3. Verificar resultado final
    console.log('\n3. Verificando configuração final...')
    
    try {
      // Testar configuracoes_sistema
      const { data: configData, error: configError } = await supabase
        .from('configuracoes_sistema')
        .select('*')

      if (configError) {
        console.log('❌ Erro ao verificar configuracoes_sistema:', configError.message)
      } else {
        console.log(`✅ configuracoes_sistema: ${configData.length} registros`)
      }

      // Testar niveis_acesso
      const { data: niveisData, error: niveisError } = await supabase
        .from('niveis_acesso')
        .select('nome, permissoes')

      if (niveisError) {
        console.log('❌ Erro ao verificar niveis_acesso:', niveisError.message)
      } else {
        console.log(`✅ niveis_acesso: ${niveisData.length} registros com permissões`)
      }

      // Testar log_atividades
      const { data: logData, error: logError } = await supabase
        .from('log_atividades')
        .select('id')
        .limit(1)

      if (logError) {
        console.log('❌ log_atividades não existe ou erro:', logError.message)
      } else {
        console.log('✅ log_atividades: tabela existe')
      }

    } catch (error) {
      console.log('❌ Erro na verificação final:', error.message)
    }

    console.log('\n🎉 Configuração concluída!')
    console.log('\n📋 Se alguma tabela não foi criada automaticamente, execute o SQL manualmente no Supabase SQL Editor.')

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message)
  }
}

executeSQL()
