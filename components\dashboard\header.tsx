"use client"

import { Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Notifications } from "@/components/dashboard/notifications"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { useState } from "react"
import { useSupabase } from "@/lib/supabase-provider"

export function DashboardHeader() {
  const { userDetails, signOut } = useSupabase()
  const [showSearch, setShowSearch] = useState(false)

  const handleLogout = async () => {
    try {
      console.log("Header logout iniciado...")
      await signOut()
      // Forçar redirecionamento
      window.location.href = "/login"
    } catch (error) {
      console.error("Erro no logout do header:", error)
      // Mesmo com erro, forçar redirecionamento
      window.location.href = "/login"
    }
  }

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-2 border-b bg-background px-3 md:px-6">
      <SidebarTrigger className="lg:hidden" />

      {/* Versão mobile do campo de busca */}
      {showSearch ? (
        <div className="flex-1 md:hidden">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Buscar..."
              className="w-full appearance-none bg-background pl-8 shadow-none pr-8"
              autoFocus
              onBlur={() => setShowSearch(false)}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-9 w-9 p-0"
              onClick={() => setShowSearch(false)}
            >
              &times;
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="flex-1 md:hidden">
            <Button variant="ghost" size="icon" className="md:hidden" onClick={() => setShowSearch(true)}>
              <Search className="h-5 w-5" />
              <span className="sr-only">Buscar</span>
            </Button>
          </div>

          {/* Versão desktop do campo de busca */}
          <div className="hidden md:block md:flex-1">
            <form>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar..."
                  className="w-full appearance-none bg-background pl-8 shadow-none md:w-2/3 lg:w-1/3"
                />
              </div>
            </form>
          </div>
        </>
      )}

      <div className="flex items-center gap-1 md:gap-2">
        <ThemeToggle />
        <Notifications />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src={userDetails?.avatar_url || "/diverse-avatars.png"} alt="Avatar" />
                <AvatarFallback>{userDetails?.nome?.charAt(0) || "U"}</AvatarFallback>
              </Avatar>
              <span className="sr-only">Perfil</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Minha Conta</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Perfil</DropdownMenuItem>
            <DropdownMenuItem>Configurações</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>Sair</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}
