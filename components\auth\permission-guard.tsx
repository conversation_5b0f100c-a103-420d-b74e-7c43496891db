"use client"

import { usePermissions, type Permission } from "@/hooks/use-permissions"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  fallback?: React.ReactNode
  redirectTo?: string
}

export function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  redirectTo = "/dashboard"
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions()
  const router = useRouter()

  // Determinar quais permissões verificar
  const permissionsToCheck = permission ? [permission] : permissions

  // Verificar se o usuário tem as permissões necessárias
  const hasRequiredPermissions = () => {
    if (permissionsToCheck.length === 0) return true

    if (permission) {
      return hasPermission(permission)
    }

    if (requireAll) {
      return hasAllPermissions(permissionsToCheck)
    }

    return hasAnyPermission(permissionsToCheck)
  }

  useEffect(() => {
    if (!loading && !hasRequiredPermissions()) {
      if (redirectTo) {
        router.push(`${redirectTo}?error=access_denied`)
      }
    }
  }, [loading, redirectTo, router])

  // Mostrar loading enquanto verifica permissões
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]" suppressHydrationWarning>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Verificando permissões...</p>
        </div>
      </div>
    )
  }

  // Se não tem permissão, mostrar fallback ou mensagem de erro
  if (!hasRequiredPermissions()) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Acesso Negado</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Você não tem permissão para acessar esta página.
              </p>
              <Button onClick={() => router.push("/dashboard")} variant="outline">
                Voltar ao Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Se tem permissão, renderizar o conteúdo
  return <>{children}</>
}

// Componente para verificar permissões inline
interface PermissionCheckProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  fallback?: React.ReactNode
}

export function PermissionCheck({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null
}: PermissionCheckProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions()

  if (loading) return null

  const permissionsToCheck = permission ? [permission] : permissions

  const hasRequiredPermissions = () => {
    if (permissionsToCheck.length === 0) return true

    if (permission) {
      return hasPermission(permission)
    }

    if (requireAll) {
      return hasAllPermissions(permissionsToCheck)
    }

    return hasAnyPermission(permissionsToCheck)
  }

  if (!hasRequiredPermissions()) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
