# Correção do Erro de Permissão na Importação de Borderos

## Problemas

### 1. Erro de Permissão

Durante a importação de borderos, o sistema estava apresentando o seguinte erro:

```
Erro de inserção: must be owner of materialized view mv_bordero_counts_by_status (42501)
```

Este erro ocorre porque o sistema estava tentando atualizar diretamente as materialized views após a inserção de borderos, mas o usuário da aplicação não tem permissões de proprietário para fazer isso.

### 2. Duplicação na Importação

Quando ocorre um erro durante a importação, o sistema para por um minuto, volta para a etapa de mapeamento, e quando o usuário tenta importar novamente, acaba importando borderos duplicados. Isso acontece porque:

1. O sistema não trata adequadamente os erros durante a importação
2. Não há um mecanismo para voltar ao passo anterior (validação) automaticamente em caso de erro
3. Quando o usuário tenta importar novamente, os dados já validados ainda estão em memória

### 3. Botão "Ações" não funcional

O botão "Ações" na tabela de borderos não está funcionando. Este botão deveria abrir um menu com opções como visualizar, editar, mudar o status, etc., mas atualmente é apenas um botão sem funcionalidade.

## Soluções Implementadas

### 1. Para o Erro de Permissão: Abordagem baseada em fila

1. Em vez de tentar atualizar as materialized views diretamente, o sistema agora adiciona uma entrada em uma tabela de fila (`dashboard_refresh_queue`).
2. Um job agendado processa esta fila periodicamente, atualizando as materialized views com as permissões adequadas.
3. Também criamos um endpoint administrativo para processar a fila manualmente quando necessário.

### 2. Para o Problema de Duplicação: Melhor tratamento de erros

1. Melhoramos o tratamento de erros durante a importação
2. Adicionamos um mecanismo para voltar automaticamente à tela de validação em caso de falha completa
3. Adicionamos um botão "Voltar para Validação" nos resultados da importação quando há erros
4. Implementamos mensagens de erro mais específicas para ajudar na identificação do problema

### 3. Para o Botão "Ações": Implementação do menu de ações

1. Substituímos o botão simples por um `DropdownMenu` com várias opções
2. Implementamos as funcionalidades para cada ação (visualizar, editar, mudar status, etc.)
3. Adicionamos um diálogo para solicitar correção
4. Implementamos a funcionalidade no layout mobile também

## Arquivos Criados/Modificados

1. **FIX_PERMISSION_ERROR.sql**
   - Remove o trigger existente que estava causando o erro
   - Cria uma tabela de fila para atualização do dashboard
   - Cria um novo trigger que apenas insere na fila em vez de tentar atualizar as views diretamente
   - Cria uma função para processar a fila com permissões adequadas

2. **app/api/admin/process-dashboard-queue/route.ts**
   - Endpoint para processar a fila manualmente
   - Requer autenticação e permissões de administrador

3. **lib/dashboard-refresh-job.ts**
   - Implementa um job agendado para processar a fila periodicamente
   - Executa a cada 5 minutos por padrão

4. **app/api/_init-server.ts** e **app/api/init/route.ts**
   - Iniciam o job quando o servidor é iniciado
   - Apenas em ambiente de produção

5. **app/api/borderos/import/route.ts**
   - Modificado para usar a abordagem baseada em fila
   - Adiciona uma entrada na fila após inserir um bordero com sucesso

6. **app/dashboard/borderos/importar/page.tsx**
   - Melhorado o tratamento de erros durante a importação
   - Adicionado mecanismo para voltar à tela de validação em caso de falha
   - Adicionado botão para voltar à validação nos resultados da importação

7. **components/dashboard/bordero-table-with-pagination.tsx**
   - Implementado o menu de ações com dropdown
   - Adicionadas funções para atualizar o status dos borderos
   - Adicionado diálogo para solicitar correção
   - Implementado o menu de ações no layout mobile

8. **APPLY_FIXES.sh**
   - Script para aplicar todas as alterações

9. **FIX_IMPORT_DUPLICATION.js** e **FIX_ACTIONS_BUTTON.js**
   - Contêm as correções para os problemas de duplicação e botão de ações

## Como Aplicar as Correções

### Método 1: Usando o Script Automatizado

1. Certifique-se de que o PostgreSQL está instalado e configurado corretamente
2. Execute o script `APPLY_FIXES.sh`:

```bash
chmod +x APPLY_FIXES.sh
./APPLY_FIXES.sh
```

### Método 2: Aplicação Manual

1. Execute o script SQL para corrigir o problema de permissão:

```bash
psql -f FIX_PERMISSION_ERROR.sql
```

2. Substitua o arquivo de importação de borderos:

```bash
mv app/api/borderos/import/route.ts.new app/api/borderos/import/route.ts
```

3. Aplique as correções para o problema de duplicação:

```bash
# Backup do arquivo original
cp app/dashboard/borderos/importar/page.tsx app/dashboard/borderos/importar/page.tsx.bak

# Aplicar as correções usando o editor de sua preferência
# Substitua a função executeImport e a seção de resultados da importação
# conforme descrito no arquivo FIX_IMPORT_DUPLICATION.js
```

4. Aplique as correções para o botão de ações:

```bash
# Backup do arquivo original
cp components/dashboard/bordero-table-with-pagination.tsx components/dashboard/bordero-table-with-pagination.tsx.bak

# Aplicar as correções usando o editor de sua preferência
# Adicione as importações, estados, funções e componentes necessários
# conforme descrito no arquivo FIX_ACTIONS_BUTTON.js
```

5. Reinicie o servidor:

```bash
npm run build
npm run start
```

## Verificação

Para verificar se as correções foram aplicadas corretamente:

1. Tente importar borderos novamente
2. Verifique os logs do servidor para confirmar que não há erros de permissão
3. Teste o cenário de erro:
   - Tente importar um bordero com dados inválidos
   - Verifique se o sistema volta para a tela de validação em caso de erro
   - Verifique se não há duplicação ao tentar importar novamente
4. Teste o botão de ações:
   - Verifique se o menu de ações abre ao clicar no botão
   - Teste as diferentes ações (visualizar, editar, mudar status, etc.)
   - Teste o diálogo de solicitação de correção
5. Acesse `/api/admin/process-dashboard-queue` para processar a fila manualmente

## Monitoramento

O job agendado processará a fila a cada 5 minutos. Você pode verificar os logs do servidor para confirmar que o job está sendo executado corretamente.

Se necessário, você pode ajustar o intervalo de processamento no arquivo `lib/dashboard-refresh-job.ts`.

## Considerações Adicionais

- O job agendado só é iniciado em ambiente de produção. Em ambiente de desenvolvimento, você precisa processar a fila manualmente.
- Se você precisar processar a fila imediatamente, pode acessar o endpoint `/api/admin/process-dashboard-queue` (requer permissões de administrador).
- A tabela `dashboard_refresh_queue` pode crescer com o tempo. Considere implementar uma limpeza periódica para remover entradas antigas.